// 测试API接口的简单脚本
// 在浏览器控制台中运行此代码来测试API连接

async function testAgencyAPI() {
    const apiUrl = 'http://www.huiyiyunai.com:8000/api/agency/submit';

    const testData = {
        companyName: '测试公司',
        location: '四川省成都市',
        contactName: '张三',
        contactPhone: '13800138000',
        industry: '互联网',
        submitTime: new Date().toISOString(),
        source: '招商代理页面'
    };

    try {
        console.log('🚀 开始测试API接口...');
        console.log('📍 API地址:', apiUrl);
        console.log('📋 测试数据:', testData);

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });

        console.log('📡 响应状态:', response.status);
        console.log('📡 响应头:', Object.fromEntries(response.headers.entries()));

        const result = await response.json();
        console.log('📝 响应结果:', result);

        if (response.ok && result.success) {
            console.log('✅ API测试成功!');
            return true;
        } else {
            console.log('❌ API测试失败:', result.message || '未知错误');
            return false;
        }
    } catch (error) {
        console.error('❌ 网络错误:', error);
        return false;
    }
}

// 测试健康检查接口
async function testHealthAPI() {
    const healthUrl = 'http://www.huiyiyunai.com:8000/api/health';

    try {
        console.log('🔍 测试健康检查接口...');
        const response = await fetch(healthUrl);
        const result = await response.json();

        console.log('💚 健康检查结果:', result);
        return response.ok;
    } catch (error) {
        console.error('❌ 健康检查失败:', error);
        return false;
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('=== 开始API测试 ===');

    const healthOK = await testHealthAPI();
    const apiOK = await testAgencyAPI();

    console.log('=== 测试结果 ===');
    console.log('健康检查:', healthOK ? '✅ 通过' : '❌ 失败');
    console.log('提交接口:', apiOK ? '✅ 通过' : '❌ 失败');

    if (healthOK && apiOK) {
        console.log('🎉 所有测试通过！');
    } else {
        console.log('⚠️  部分测试失败，请检查服务器状态');
    }
}

// 如果在Node.js环境中，使用以下代码
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testAgencyAPI, testHealthAPI, runAllTests };
}

// 自动运行测试（可注释掉）
// runAllTests();
