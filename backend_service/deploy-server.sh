#!/bin/bash

# 服务器部署脚本 - 元智启代理商后台服务
echo "=== 部署元智启代理商后台服务 ==="

# 停止并删除已存在的容器（如果有）
echo "停止并删除旧容器..."
docker stop hyywebsite-service 2>/dev/null || true
docker rm hyywebsite-service 2>/dev/null || true

# 拉取最新镜像
echo "拉取最新镜像..."
docker pull swr.cn-southwest-2.myhuaweicloud.com/xplan/hyywebsite-service

# 启动容器
echo "启动服务容器..."
docker run -d \
   --name hyywebsite-service \
   -p 4999:8000 \
   -e TZ=Asia/Shanghai \
   -e MYSQL_HOST=*********** \
   -e MYSQL_PORT=3306 \
   -e MYSQL_USER=root \
   -e MYSQL_PASSWORD=xplan@123 \
   -e MYSQL_DATABASE=officialweb \
   --restart unless-stopped \
   swr.cn-southwest-2.myhuaweicloud.com/xplan/hyywebsite-service

# 检查容器状态
echo "检查容器状态..."
sleep 3
docker ps | grep hyywebsite-service

# 检查服务健康状态
echo "检查服务健康状态..."
sleep 5
curl -s http://localhost:4999/api/health | python3 -m json.tool || echo "服务启动中，请稍后再试"

echo "=== 部署完成 ==="
echo "服务地址: http://localhost:4999"
echo "API文档: http://localhost:4999/docs"
echo "健康检查: http://localhost:4999/api/health"
