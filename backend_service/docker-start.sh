#!/bin/bash

# 元智启代理商后台服务 Docker 启动脚本

echo "=== 元智启代理商后台服务 Docker 启动 ==="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装，请先安装 docker-compose"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 检查是否存在.env文件
if [ ! -f ".env" ]; then
    echo "📝 创建默认 .env 文件..."
    cat > .env << EOF
# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=False

# MySQL数据库配置
MYSQL_HOST=***********
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=
MYSQL_DATABASE=officialweb
MYSQL_CHARSET=utf8mb4

# 防刷配置
RATE_LIMIT_PER_MINUTE=3
RATE_LIMIT_PER_HOUR=10
PHONE_DAILY_LIMIT=3
CONTENT_FINGERPRINT_LIMIT=1

# 安全配置
ADMIN_TOKEN=admin_secret_token_2024
SECRET_KEY=your_very_secure_secret_key_here

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=backend_service.log
EOF
    echo "✅ 已创建默认 .env 文件，请根据需要修改配置"
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 构建并启动服务
echo "🚀 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
if docker-compose ps | grep -q "Up"; then
    echo "✅ 服务启动成功！"
    echo ""
    echo "📋 服务信息："
    echo "  - 容器状态: $(docker-compose ps --format table)"
    echo "  - Swagger文档: http://localhost:4999/docs"
    echo "  - ReDoc文档: http://localhost:4999/redoc"
    echo "  - 健康检查: http://localhost:4999/api/health"
    echo ""
    echo "📝 常用命令："
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 停止服务: docker-compose down"
    echo "  - 重启服务: docker-compose restart"
    echo ""

    # 测试健康检查
    echo "🏥 测试健康检查..."
    sleep 5
    if curl -s http://localhost:4999/api/health > /dev/null; then
        echo "✅ 健康检查通过"
    else
        echo "⚠️  健康检查失败，服务可能还在启动中"
    fi
else
    echo "❌ 服务启动失败"
    echo "📋 容器状态："
    docker-compose ps
    echo ""
    echo "📋 错误日志："
    docker-compose logs --tail=20
fi

echo ""
echo "=== 启动完成 ==="
