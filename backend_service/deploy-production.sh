#!/bin/bash

# 生产环境部署脚本 - CentOS服务器
# 使用方法: ./deploy-production.sh [镜像地址]

set -e

# 默认配置
DEFAULT_IMAGE="your-registry.com/hyy-backend:latest"
CONTAINER_NAME="hyy-backend"
PORT="8000"

# 解析参数
IMAGE_NAME=${1:-$DEFAULT_IMAGE}

echo "🚀 部署元智启后台服务到生产环境..."
echo "📋 配置信息:"
echo "   - 镜像地址: ${IMAGE_NAME}"
echo "   - 容器名称: ${CONTAINER_NAME}"
echo "   - 服务端口: ${PORT}"
echo "   - 文档功能: 已禁用"
echo ""

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装"
    exit 1
fi

# 停止旧容器
if [ "$(docker ps -aq -f name=${CONTAINER_NAME})" ]; then
    echo "🛑 停止旧容器..."
    docker stop ${CONTAINER_NAME} || true
    docker rm ${CONTAINER_NAME} || true
fi

# 拉取镜像
echo "📥 拉取最新镜像..."
docker pull ${IMAGE_NAME}

# 启动容器
echo "🚀 启动生产服务..."
docker run -d \
    --name ${CONTAINER_NAME} \
    --restart unless-stopped \
    -p ${PORT}:8000 \
    -e TZ=Asia/Shanghai \
    -e PYTHONUNBUFFERED=1 \
    ${IMAGE_NAME}

# 检查状态
sleep 5
if docker ps | grep -q ${CONTAINER_NAME}; then
    echo ""
    echo "✅ 部署成功!"
    echo "🌐 服务地址: http://$(hostname -I | awk '{print $1}'):${PORT}"
    echo "📊 健康检查: http://$(hostname -I | awk '{print $1}'):${PORT}/api/health"
    echo ""
    echo "📋 管理命令:"
    echo "   查看日志: docker logs -f ${CONTAINER_NAME}"
    echo "   重启服务: docker restart ${CONTAINER_NAME}"
    echo "   停止服务: docker stop ${CONTAINER_NAME}"
else
    echo "❌ 部署失败!"
    docker logs ${CONTAINER_NAME}
    exit 1
fi
