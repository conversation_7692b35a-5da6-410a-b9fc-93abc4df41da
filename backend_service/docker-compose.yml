# version字段在新版本docker-compose中已过时，移除以避免警告

services:
  agency-backend:
    build:
      context: .
      dockerfile: Dockerfile
    image: hyywebsite-service:latest
    container_name: hyywebsite-service
    ports:
      - "4999:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=False
      # MySQL数据库配置（如需使用MySQL版本）
      - MYSQL_HOST=***********
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-xplan@123}
      - MYSQL_DATABASE=officialweb
      - MYSQL_CHARSET=utf8mb4
      # 防刷配置
      - RATE_LIMIT_PER_MINUTE=3
      - RATE_LIMIT_PER_HOUR=10
      - PHONE_DAILY_LIMIT=3
      - CONTENT_FINGERPRINT_LIMIT=1
      # 安全配置
      - ADMIN_TOKEN=${ADMIN_TOKEN:-admin_secret_token_2024}
      - SECRET_KEY=${SECRET_KEY:-your_very_secure_secret_key_here}
      # 日志配置
      - LOG_LEVEL=INFO
      - LOG_FILE=backend_service.log
    volumes:
      # 持久化日志文件
      - ./logs:/app/logs
      # 如果需要挂载配置文件
      - ./.env:/app/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - agency-network

  # 如果需要本地MySQL数据库，可以取消注释以下配置
  # mysql:
  #   image: mysql:8.0
  #   container_name: agency-mysql
  #   environment:
  #     MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
  #     MYSQL_DATABASE: officialweb
  #     MYSQL_USER: ${MYSQL_USER:-agency_user}
  #     MYSQL_PASSWORD: ${MYSQL_PASSWORD:-agency_password}
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
  #   restart: unless-stopped
  #   networks:
  #     - agency-network

networks:
  agency-network:
    driver: bridge

# volumes:
#   mysql_data:
