#!/bin/bash

echo "=== 重启元智启代理商后台服务 ==="

# 停止现有进程
echo "🛑 停止现有服务..."
pkill -f "uvicorn.*main:app" || echo "没有找到运行中的服务"

# 等待进程完全停止
sleep 2

# 启动新服务
echo "🚀 启动服务..."
nohup uvicorn main:app --host 0.0.0.0 --port 8000 > service.log 2>&1 &

# 等待服务启动
sleep 3

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -s http://localhost:8000/api/health > /dev/null; then
    echo "✅ 服务启动成功！"
    echo "📋 服务信息："
    echo "  - 健康检查: http://localhost:8000/api/health"
    echo "  - API地址: http://localhost:8000/api/agency/submit"
    echo "  - 日志文件: service.log"
else
    echo "❌ 服务启动失败，请检查日志文件 service.log"
fi

echo "=== 重启完成 ==="
