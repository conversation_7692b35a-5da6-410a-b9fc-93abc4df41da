# 元智启代理商后台服务

这是一个使用FastAPI开发的后台服务，用于处理元智启平台代理商申请提交，包含完善的防刷机制和数据管理功能。

## 功能特性

### 🚀 核心功能
- **代理商申请提交**：接收和处理代理商申请数据
- **数据验证**：完整的表单数据验证机制
- **数据存储**：使用SQLite数据库存储申请数据
- **管理员接口**：提供数据查询和管理功能

### 🛡️ 防刷机制
- **IP频率限制**：每个IP每分钟最多3次请求，每小时最多10次
- **手机号限制**：每个手机号每天最多提交3次申请
- **内容指纹检查**：防止重复提交相同内容（1小时内）
- **自动清理**：定期清理过期的防刷记录

### 🔒 安全特性
- **CORS配置**：跨域请求安全控制
- **数据验证**：严格的输入数据验证
- **错误处理**：完善的异常处理机制
- **日志记录**：详细的操作日志记录

## 安装和运行

### 方式一：Docker 部署（推荐）

#### 1. 环境要求
- Docker
- docker-compose

#### 2. 一键启动
```bash
cd backend_service
./docker-start.sh
```

#### 3. 手动Docker部署
```bash
# 构建并启动服务
docker-compose up --build -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

#### 4. 单独构建Docker镜像
```bash
# 构建镜像
docker build -t agency-backend .

# 运行容器
docker run -d \
  --name agency-backend \
  -p 8000:8000 \
  -e MYSQL_PASSWORD=your_password \
  agency-backend
```

### 方式二：本地开发部署

#### 1. 环境要求
- Python 3.8+
- pip

#### 2. 安装依赖
```bash
cd backend_service
pip install -r requirements.txt
```

#### 3. 运行服务
```bash
# 简化版本（内存存储）
python simple_main.py

# 完整版本（MySQL数据库）
python main.py
```

## API接口文档

### 1. 提交代理商申请
- **URL**: `POST /api/agency/submit`
- **描述**: 接收代理商申请提交
- **请求体**:
```json
{
  "companyName": "公司名称",
  "location": "省市地区",
  "contactName": "联系人姓名",
  "contactPhone": "手机号码",
  "industry": "所属行业",
  "source": "招商代理页面"
}
```
- **响应**:
```json
{
  "success": true,
  "message": "提交成功，我们会尽快与您联系",
  "data": {
    "submissionId": 123,
    "submitTime": "2024-01-01T12:00:00"
  }
}
```

### 2. 获取申请列表（管理员）
- **URL**: `GET /api/agency/list`
- **描述**: 获取代理商申请列表
- **请求头**: `Admin-Token: your_admin_token`
- **查询参数**:
  - `page`: 页码（默认1）
  - `page_size`: 每页数量（默认20）
- **响应**:
```json
{
  "success": true,
  "data": {
    "submissions": [...],
    "page": 1,
    "pageSize": 20,
    "total": 100
  }
}
```

### 3. 健康检查
- **URL**: `GET /api/health`
- **描述**: 服务健康状态检查
- **响应**:
```json
{
  "status": "healthy",
  "service": "元智启代理商后台服务",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00"
}
```

## 防刷机制详解

### IP频率限制
- 每个IP每分钟最多3次请求
- 每个IP每小时最多10次请求
- 超出限制返回429状态码

### 手机号限制
- 每个手机号每天最多提交3次申请
- 超出限制返回429状态码

### 内容指纹检查
- 基于公司名称、联系人、手机号、地区生成MD5指纹
- 1小时内相同内容只能提交1次
- 检测到重复提交返回409状态码

### 自动清理机制
- 每5分钟自动清理过期的防刷记录
- 避免内存占用过多
- 保证防刷机制的准确性

## 配置说明

### 环境变量配置
创建 `.env` 文件进行配置（参考 `env_example.txt`）：
```env
# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=False


# 防刷配置
RATE_LIMIT_PER_MINUTE=3
RATE_LIMIT_PER_HOUR=10
PHONE_DAILY_LIMIT=3

# 安全配置
ADMIN_TOKEN=your_secure_admin_token_here
SECRET_KEY=your_very_secure_secret_key_here
```

### 数据库配置
- 使用MySQL数据库
- 数据库表：`collectcompanyinfo`
- 自动创建表结构和索引
- 支持连接池配置优化性能

### 数据库测试
在启动服务前，可以先测试数据库连接：
```bash
# 测试MySQL连接和基本功能
python test_service.py
```

## 日志记录

### 日志级别
- INFO：正常操作记录
- WARNING：防刷机制触发
- ERROR：错误和异常记录

### 日志文件
- 文件位置：`backend_service.log`
- 同时输出到控制台
- 包含时间戳、级别、消息内容

## 监控和维护

### 健康检查
- 接口：`GET /api/health`
- 用于监控服务状态
- 返回服务信息和时间戳

### 数据备份
```bash
# 备份SQLite数据库
cp agency_submissions.db agency_submissions_backup_$(date +%Y%m%d).db
```

### 日志轮转
建议配置日志轮转避免日志文件过大：
```bash
# 使用logrotate配置
sudo nano /etc/logrotate.d/agency-backend
```

## 安全建议

1. **修改默认token**：生产环境必须修改ADMIN_TOKEN
2. **使用HTTPS**：生产环境建议使用HTTPS
3. **防火墙配置**：限制API访问来源
4. **定期备份**：定期备份数据库文件
5. **监控日志**：监控异常访问和攻击尝试

## 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
lsof -i :8000
# 修改端口配置
export PORT=8001
```

2. **数据库权限问题**
```bash
# 检查数据库文件权限
ls -la agency_submissions.db
# 修改权限
chmod 664 agency_submissions.db
```

3. **依赖安装失败**
```bash
# 升级pip
pip install --upgrade pip
# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 联系支持

如有问题请联系：
- 邮箱：<EMAIL>
- 电话：188-4845-7100
