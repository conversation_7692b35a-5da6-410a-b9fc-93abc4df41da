#!/bin/bash

# 停止FastAPI后台服务脚本
echo "正在停止FastAPI后台服务..."

# 查找并停止uvicorn进程
PIDS=$(ps aux | grep "uvicorn.*main:app" | grep -v grep | awk '{print $2}')

if [ -z "$PIDS" ]; then
    echo "没有找到正在运行的FastAPI服务进程"
else
    echo "找到进程ID: $PIDS"
    for PID in $PIDS; do
        echo "正在停止进程 $PID..."
        kill -TERM $PID
        sleep 2

        # 检查进程是否还在运行
        if ps -p $PID > /dev/null; then
            echo "进程 $PID 仍在运行，强制停止..."
            kill -KILL $PID
        fi
    done
    echo "FastAPI服务已停止"
fi

# 也检查Docker容器
CONTAINER_ID=$(docker ps | grep "backend_service" | awk '{print $1}')
if [ ! -z "$CONTAINER_ID" ]; then
    echo "发现Docker容器正在运行，正在停止..."
    docker stop $CONTAINER_ID
    echo "Docker容器已停止"
fi

echo "服务停止完成！"
