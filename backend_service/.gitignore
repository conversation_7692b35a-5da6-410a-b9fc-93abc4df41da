# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/
backend_service.log

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production

# 数据库文件
*.db
*.sqlite
*.sqlite3

# Docker相关
.docker/

# 临时文件
.tmp/
temp/
*.tmp

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# 备份文件
*.bak
*.backup

# 生产环境配置文件
config_production.py
secrets.py

# 上传文件目录
uploads/
media/

# 证书文件
*.pem
*.key
*.crt
