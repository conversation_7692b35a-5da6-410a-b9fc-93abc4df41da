from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, field_validator
from typing import Optional
import time
import hashlib
from datetime import datetime
import logging
import uvicorn
from collections import defaultdict
import re
import pymysql
import uuid

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backend_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="元智启代理商后台服务",
    description="处理代理商申请提交的后台API服务",
    version="1.0.0",
    docs_url=None,  # 禁用Swagger文档
    redoc_url=None  # 禁用ReDoc文档
)

# CORS配置 - 允许前端访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 临时允许所有来源，解决生产环境CORS问题
    allow_credentials=False,  # 当allow_origins=["*"]时，必须设置为False
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 数据库配置
DB_CONFIG = {
    'host': '***********',
    'port': 3306,
    'user': 'root',
    'password': 'xplan@123',
    'database': 'officialweb',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

def save_to_database(data, client_ip):
    """保存数据到数据库"""
    try:
        connection = get_db_connection()
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 生成UUID作为主键
            record_id = str(uuid.uuid4())

            # 插入SQL语句
            sql = """
            INSERT INTO collectcompanyinfo
            (Id, CompanyName, Area, Contact, ContactPhone, Industry, Remark, CreateTime)
            VALUES (%(Id)s, %(CompanyName)s, %(Area)s, %(Contact)s, %(ContactPhone)s, %(Industry)s, %(Remark)s, %(CreateTime)s)
            """

            # 准备数据
            insert_data = {
                'Id': record_id,
                'CompanyName': data.companyName,
                'Area': data.location,
                'Contact': data.contactName,
                'ContactPhone': data.contactPhone,
                'Industry': data.industry or '未选择',
                'Remark': f"来源: {data.source or '招商代理页面'}, IP: {client_ip}, 提交时间: {data.submitTime or datetime.now().isoformat()}",
                'CreateTime': datetime.now()
            }

            cursor.execute(sql, insert_data)
            connection.commit()

            logger.info(f"成功保存公司信息到数据库, ID: {record_id}, 公司: {data.companyName}")
            return record_id

    except Exception as e:
        logger.error(f"保存到数据库失败: {e}")
        raise
    finally:
        if 'connection' in locals():
            connection.close()

# 数据模型
class AgencySubmission(BaseModel):
    companyName: str
    location: str
    contactName: str
    contactPhone: str
    industry: Optional[str] = "未选择"
    submitTime: Optional[str] = None
    source: Optional[str] = "招商代理页面"

    @field_validator('companyName')
    @classmethod
    def validate_company_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('公司名称不能为空且长度至少2个字符')
        if len(v.strip()) > 50:
            raise ValueError('公司名称长度不能超过50个字符')
        return v.strip()

    @field_validator('location')
    @classmethod
    def validate_location(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('省市地区不能为空')
        return v.strip()

    @field_validator('contactName')
    @classmethod
    def validate_contact_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('联系人姓名不能为空')
        if len(v.strip()) > 50:
            raise ValueError('联系人姓名长度不能超过50个字符')
        return v.strip()

    @field_validator('contactPhone')
    @classmethod
    def validate_contact_phone(cls, v):
        if not v:
            raise ValueError('联系方式不能为空')
        # 中国手机号验证
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, v.strip()):
            raise ValueError('请输入正确的手机号码')
        return v.strip()

# 防刷机制类
class AntiSpamManager:
    def __init__(self):
        # IP限制：每个IP每分钟最多3次请求，每小时最多10次
        self.ip_requests = defaultdict(list)
        self.ip_hourly_requests = defaultdict(list)

        # 手机号限制：每个手机号每天最多提交3次
        self.phone_daily_requests = defaultdict(list)

        # 内容指纹限制：相同内容每小时最多提交1次
        self.content_fingerprints = defaultdict(list)

    def get_client_ip(self, request: Request) -> str:
        """获取客户端真实IP"""
        # 优先从X-Forwarded-For获取
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        # 从X-Real-IP获取
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # 最后使用客户端IP
        return request.client.host

    def generate_content_fingerprint(self, data: AgencySubmission) -> str:
        """生成内容指纹"""
        content = f"{data.companyName}|{data.contactName}|{data.contactPhone}|{data.location}"
        return hashlib.md5(content.encode()).hexdigest()

    def check_ip_rate_limit(self, ip: str) -> bool:
        """检查IP频率限制"""
        now = time.time()

        # 清理1分钟前的记录
        self.ip_requests[ip] = [t for t in self.ip_requests[ip] if now - t < 60]
        # 清理1小时前的记录
        self.ip_hourly_requests[ip] = [t for t in self.ip_hourly_requests[ip] if now - t < 3600]

        # 检查限制
        if len(self.ip_requests[ip]) >= 3:  # 每分钟最多3次
            return False
        if len(self.ip_hourly_requests[ip]) >= 10:  # 每小时最多10次
            return False

        # 记录请求
        self.ip_requests[ip].append(now)
        self.ip_hourly_requests[ip].append(now)
        return True

    def check_phone_daily_limit(self, phone: str) -> bool:
        """检查手机号每日限制"""
        now = time.time()

        # 清理24小时前的记录
        self.phone_daily_requests[phone] = [t for t in self.phone_daily_requests[phone] if now - t < 86400]

        # 检查限制（每天最多3次）
        if len(self.phone_daily_requests[phone]) >= 3:
            return False

        # 记录请求
        self.phone_daily_requests[phone].append(now)
        return True

    def check_content_fingerprint(self, fingerprint: str) -> bool:
        """检查内容指纹重复提交"""
        now = time.time()

        # 清理1小时前的记录
        self.content_fingerprints[fingerprint] = [t for t in self.content_fingerprints[fingerprint] if now - t < 3600]

        # 检查是否重复（1小时内相同内容只能提交1次）
        if len(self.content_fingerprints[fingerprint]) >= 1:
            return False

        # 记录指纹
        self.content_fingerprints[fingerprint].append(now)
        return True

# 创建防刷管理器实例
anti_spam = AntiSpamManager()

# API路由
@app.post("/api/agency/submit")
async def submit_agency_application(
    data: AgencySubmission,
    request: Request
):
    """接收代理商申请提交"""
    try:
        # 获取客户端IP
        client_ip = anti_spam.get_client_ip(request)
        logger.info(f"收到代理商申请，IP: {client_ip}, 公司: {data.companyName}")

        # 防刷检查
        # 1. IP频率限制
        if not anti_spam.check_ip_rate_limit(client_ip):
            logger.warning(f"IP {client_ip} 请求过于频繁")
            raise HTTPException(
                status_code=429,
                detail="请求过于频繁，请稍后再试"
            )

        # 2. 手机号每日限制
        if not anti_spam.check_phone_daily_limit(data.contactPhone):
            logger.warning(f"手机号 {data.contactPhone} 今日提交次数过多")
            raise HTTPException(
                status_code=429,
                detail="该手机号今日提交次数已达上限"
            )

        # 3. 内容指纹检查
        content_fingerprint = anti_spam.generate_content_fingerprint(data)
        if not anti_spam.check_content_fingerprint(content_fingerprint):
            logger.warning(f"检测到重复内容提交，指纹: {content_fingerprint}")
            raise HTTPException(
                status_code=409,
                detail="检测到重复提交，请勿重复提交相同信息"
            )

        # 保存到数据库
        submission_id = save_to_database(data, client_ip)

        logger.info(f"保存代理商申请成功，ID: {submission_id}, 公司: {data.companyName}")

        # 返回成功响应
        return {
            "success": True,
            "message": "提交成功，我们会尽快与您联系",
            "data": {
                "submissionId": submission_id,
                "submitTime": datetime.now().isoformat()
            }
        }

    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"数据验证失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"处理代理商申请时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail="服务器内部错误，请稍后重试"
        )

@app.get("/api/agency/list")
async def get_agency_submissions(
    page: int = 1,
    page_size: int = 20,
    request: Request = None
):
    """获取代理商申请列表（管理员接口）"""
    try:
        # 简单的管理员验证
        admin_token = request.headers.get("Admin-Token")
        if admin_token != "admin_secret_token_2024":
            raise HTTPException(status_code=401, detail="未授权访问")

        connection = get_db_connection()
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 获取总记录数
            cursor.execute("SELECT COUNT(*) as total FROM collectcompanyinfo")
            total = cursor.fetchone()['total']

            # 分页查询
            offset = (page - 1) * page_size
            cursor.execute("""
                SELECT Id, CompanyName, Area, Contact, ContactPhone, Industry, Remark, CreateTime
                FROM collectcompanyinfo
                ORDER BY CreateTime DESC
                LIMIT %s OFFSET %s
            """, (page_size, offset))

            records = cursor.fetchall()

            return {
                "success": True,
                "data": {
                    "submissions": records,
                    "page": page,
                    "pageSize": page_size,
                    "total": total,
                    "totalPages": (total + page_size - 1) // page_size
                }
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取代理商申请列表时出错: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
    finally:
        if 'connection' in locals():
            connection.close()

@app.get("/api/health")
async def health_check():
    """健康检查接口"""
    try:
        # 测试数据库连接
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) as total FROM collectcompanyinfo")
            total = cursor.fetchone()[0]
        connection.close()

        return {
            "status": "healthy",
            "service": "元智启代理商后台服务",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "database": "connected",
            "submissions_count": total
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "service": "元智启代理商后台服务",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "database": "disconnected",
            "error": str(e)
        }

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "元智启代理商后台服务",
        "version": "1.0.0",
        "health": "/api/health",
        "status": "running"
    }

if __name__ == "__main__":
    print("=== 元智启代理商后台服务启动 ===")
    print("服务地址: http://localhost:8000")
    print("健康检查: http://localhost:8000/api/health")
    print("数据库连接: ***********:3306/officialweb")
    print("按 Ctrl+C 停止服务")
    print("=" * 40)

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # 生产环境关闭热重载
        log_level="info"
    )
