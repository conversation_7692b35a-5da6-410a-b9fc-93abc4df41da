# 慧医云官网 - 现代化前端项目

基于 Nuxt.js 3 + Tailwind CSS + Element Plus 构建的现代化企业官网，完全复制 templete demo 的 UI 样式和布局。

## 项目特色

- 🚀 **现代化技术栈**: Nuxt.js 3 + Vue 3 + Tailwind CSS + Element Plus
- 🎨 **完全一致的UI**: 与 templete demo 保持100%一致的样式和布局
- 📱 **响应式设计**: 支持PC端和移动端，使用 flexible.js 进行移动端适配
- ⚡ **性能优化**: SSR/SSG支持，代码分割，图片优化
- 🛠️ **开发体验**: TypeScript支持，ESLint + Stylelint，热重载
- 🎯 **SEO友好**: 完整的Meta标签管理，结构化数据

## 技术架构

### 核心框架
- **Nuxt.js 3.8.0** - 全栈Vue框架，支持SSR/SSG
- **Vue 3** - 现代化响应式框架
- **Tailwind CSS 3.3.6** - 原子化CSS框架
- **Element Plus 2.4.4** - Vue 3组件库

### 开发工具
- **TypeScript** - 类型安全
- **ESLint** - 代码规范检查
- **Stylelint** - 样式代码检查
- **Sass/SCSS** - CSS预处理器

### 特色功能
- **移动端适配**: 使用flexible.js，与demo保持一致
- **Flex布局系统**: 完整复制demo的flex工具类
- **自定义主题**: 医疗AI主题色彩系统
- **组件化开发**: 高度可复用的组件结构

## 项目结构

```
hyy-website-modern/
├── assets/
│   └── styles/
│       ├── tailwind.css        # Tailwind CSS配置
│       ├── common.scss         # 通用样式（与demo一致）
│       └── main.scss           # 主样式文件
├── components/
│   ├── AppHeader.vue           # 头部导航组件
│   └── AppFooter.vue           # 页脚组件
├── layouts/
│   └── default.vue             # 默认布局
├── pages/
│   ├── index.vue               # 首页（完全复制demo）
│   ├── products/               # 产品页面
│   ├── news/                   # 新闻中心
│   ├── about.vue               # 关于我们
│   ├── contact.vue             # 联系我们
│   └── agency.vue              # 招商代理
├── public/
│   ├── flexible.js             # 移动端适配脚本
│   └── images/                 # 图片资源
├── nuxt.config.ts              # Nuxt配置
├── tailwind.config.js          # Tailwind配置
└── package.json                # 项目配置
```

## 页面结构

### 首页 (/)
- Hero区域 - 公司介绍和口号
- 核心产品 - 元智启AI平台介绍
- 产品特点 - 5大核心优势展示
- 行业解决方案 - AI医助、AI患助、AI智能营销作战中心
- 新闻动态 - 最新资讯展示
- 联系方式 - 完整联系信息

### 产品中心
- 元智启AI - 企业级AI应用配置平台
- AI医助 - 医疗场景智能辅助工具
- AI患助 - 全流程就医体验重构
- AI智能营销作战中心 - 可视化营销指挥平台

### 其他页面
- 新闻中心 - 行业资讯和公司动态
- 关于我们 - 公司介绍和发展历程
- 联系我们 - 联系方式和地址信息
- 招商代理 - 元智启招商代理信息

## 开发指南

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

### 代码检查
```bash
npm run lint
npm run lint:style
```

## 样式系统

### Flex布局工具类
完全复制 templete demo 的布局系统：
```css
.flex-col { display: flex; flex-direction: column; }
.flex-row { display: flex; flex-direction: row; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }
```

### 自定义主题色
```css
:root {
  --el-color-primary: #1890ff;
  --el-color-success: #52c41a;
  --el-color-warning: #fa8c16;
  --el-color-danger: #f5222d;
}
```

### 响应式断点
```javascript
screens: {
  'xs': '475px',
  'sm': '640px',
  'md': '768px',
  'lg': '1024px',
  'xl': '1280px',
  '2xl': '1536px',
}
```

## 部署说明

### 静态站点生成 (SSG)
```bash
npm run generate
```

### 服务端渲染 (SSR)
```bash
npm run build
npm run start
```

### 环境变量
```bash
NUXT_PUBLIC_API_BASE=https://api.example.com
```

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 公司：四川慧医云科技有限公司
- 电话：188-4845-7100
- 邮箱：<EMAIL>
- 地址：四川省成都市武侯区高攀路26号-1
