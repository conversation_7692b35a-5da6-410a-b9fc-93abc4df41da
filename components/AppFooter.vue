<template>
  <div class="group_14 flex-col">
    <div class="box_19 flex-row justify-between">
      <div class="block_9 flex-col justify-between">
        <div class="image-text_16 flex-row justify-between">
          <img
            class="thumbnail_5"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngea043ae9ec6014ad38088a2f2d3dd21fc75e461841ee95cc36fef57f3c182030.png"
          />
          <span class="text-group_18">电话:&nbsp;188-4845-7100</span>
        </div>
        <div class="image-text_17 flex-row justify-between">
          <img
            class="thumbnail_6"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng2efd9eecb4b1ae4e633c2623d4c2cbff09e6047b02ea6ea7e4679990dbe373d0.png"
          />
          <div class="text-group_19">
            <span class="text_38">邮箱：</span>
            <span class="text_39">yuanzhiqi_ai&#64;163.com</span>
          </div>
        </div>
        <div class="image-text_18 flex-row justify-between">
          <img
            class="thumbnail_7"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngc1bc8b2daaf10e93323a34f07b72c267813611eaadda97b3772f335b4b57114a.png"
          />
          <span class="text-group_20">地址：四川省成都市武侯区高攀路26号-1</span>
        </div>
      </div>
      <div class="image-text_19 flex-col justify-between">
        <div class="group_15 flex-col"></div>
        <span class="text-group_21">扫描关注企业微信号</span>
      </div>
    </div>
    <div class="box_10 flex-col">
      <img
        class="image_11"
        referrerpolicy="no-referrer"
        src="/pages/assets/img/SketchPng96d042623f94647948f434b31e8adf406da2ca802c9aa7ddff1f06e967470ed7.png"
      />
      <span class="paragraph_3">
        聚焦医药及医疗行业
        <br />
        提供AI深度业务应用的解决方案服务商
      </span>
      <!-- 备案信息 -->
      <div class="beian-info">
        <a
          href="https://beian.miit.gov.cn/#/Integrated/index"
          target="_blank"
          rel="noopener noreferrer"
          class="beian-link"
        >
          蜀ICP备2025131893号-1
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页脚组件 - 静态显示，无动画效果
</script>

<style scoped lang="less">
.group_14 {
  background-color: rgba(17, 26, 52, 1);
  width: 51.2rem;
  height: 11.04rem;
  .box_19 {
    width: 31.787rem;
    height: 3.44rem;
    margin: 1.36rem 0 0 9.6rem;
    .block_9 {
      width: 7.44rem;
      height: 2.934rem;
      margin-top: 0.507rem;
      .image-text_16 {
        width: 4.374rem;
        height: 0.374rem;
        .thumbnail_5 {
          width: 0.85rem;
          height: 0.85rem;
        }
        .text-group_18 {
          width: 3.734rem;
          height: 0.85rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.85rem;
        }
      }
      .image-text_17 {
        width: 5.707rem;
        height: 0.374rem;
        margin-top: 0.907rem;
        .thumbnail_6 {
          width: 0.85rem;
          height: 0.85rem;
        }
        .text-group_19 {
          width: 5.067rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
          .text_38 {
            width: 5.067rem;
            height: 0.85rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.85rem;
          }
          .text_39 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
      }
      .image-text_18 {
        width: 7.44rem;
        height: 0.374rem;
        margin-top: 0.907rem;
        .thumbnail_7 {
          width: 0.85rem;
          height: 0.85rem;
        }
        .text-group_20 {
          width: 6.8rem;
          height: 0.85rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.85rem;
        }
      }
    }
    .image-text_19 {
      width: 3.36rem;
      height: 3.44rem;
      .group_15 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/e13d9355f9cb463191769802633149b7_mergeImage.png);
        background-repeat: no-repeat;
        background-size: cover;
        width: 3.05rem;
        height: 3.05rem;
        margin-left: 0.347rem;
      }
      .text-group_21 {
        width: 3.36rem;
        height: 0.374rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.374rem;
        margin-top: 0.4rem;
        margin-left: 0.3rem;
      }
    }
  }
  .box_10 {
    width: 51.2rem;
    height: 4.054rem;
    background: url(/pages/assets/img/SketchPng5c8ba53f71bd92f957537da4d03c76390cd3535fdd5fe926d77e0f7c541f1f80.png)
      100% no-repeat;
    background-size: 100% 100%;
    margin: 1.387rem 0 0.8rem 0;
    .image_11 {
      width: 4.267rem;
      height: 0.64rem;
      margin: 0.854rem 0 0 9.6rem;
    }
    .paragraph_3 {
      width: 7.227rem;
      height: 1.494rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 0.426rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      line-height: 0.747rem;
      margin: 0.587rem 0 0.48rem 9.6rem;
    }

        // 备案信息样式
    .beian-info {
      text-align: center;
      margin: 0.4rem 0 0 0;
    background-color: #111a34;
      .beian-link {
        color: rgba(255, 255, 255, 1);  // 与底部其他文字颜色一致
        font-size: 0.32rem;
        text-decoration: none;
        transition: color 0.3s ease;

        &:hover {
          color: rgba(33, 135, 250, 1);  // 悬停时使用主题蓝色
          text-decoration: underline;
        }

        &:visited {
          color: rgba(255, 255, 255, 1);  // 访问后保持白色
        }
      }
    }
  }
}

/* 添加flex布局类 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}
</style>
