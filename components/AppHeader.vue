<template>
  <header class="group_1 flex-col">
    <div class="block_7 flex-row">
      <img
        class="image_1"
        referrerpolicy="no-referrer"
        src="/pages/assets/img/SketchPng96d042623f94647948f434b31e8adf406da2ca802c9aa7ddff1f06e967470ed7.png"
      />
      <NuxtLink to="/" class="nav-item">
        <span class="text_1" :class="{ active: $route.path === '/' }">首页</span>
      </NuxtLink>

      <div class="nav-dropdown">
        <span class="text_2" :class="{ active: $route.path && $route.path.startsWith('/products') }">产品中心</span>
        <img
          class="thumbnail_1"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng9c4b7deb2a38fb2f7601847c4dce7d457558f47854528fc768791b988e8a08f6.png"
        />
        <div class="dropdown-menu">
          <a href="http://www.yuanzhiqi.com/home" target="_blank" rel="noopener noreferrer" class="dropdown-item">元智启AI</a>
          <NuxtLink to="/products/aiyizhu" class="dropdown-item">AI医助</NuxtLink>
          <NuxtLink to="/products/aihuanzhu" class="dropdown-item">AI患助</NuxtLink>
          <NuxtLink to="/products/marketing" class="dropdown-item">AI智能营销作战中心</NuxtLink>
        </div>
      </div>

      <NuxtLink to="/agency" class="nav-item">
        <span class="text_3" :class="{ active: $route.path === '/agency' }">元智启招商代理</span>
      </NuxtLink>

      <NuxtLink to="/news" class="nav-item">
        <span class="text_4" :class="{ active: $route.path === '/news' }">新闻中心</span>
      </NuxtLink>

      <NuxtLink to="/contact" class="nav-item">
        <span class="text_5" :class="{ active: $route.path === '/contact' }">联系我们</span>
      </NuxtLink>

      <NuxtLink to="/about" class="nav-item">
        <span class="text_6" :class="{ active: $route.path === '/about' }">关于我们</span>
      </NuxtLink>
    </div>
    <!--  -->
  </header>
</template>

<script setup>
// 移除不必要的 route 变量，直接使用 $route
// 这样可以避免 SSR 环境中的潜在问题

// 移除下划线功能相关代码

// 移除所有隐藏动画相关的代码
</script>

<style scoped lang="less">
.group_1 {
  background-color: rgba(255, 255, 255, 1);
  height: 2.187rem;
  width: 51.2rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.5s ease-in-out;
  transform: translateY(0);



    .block_7 {
    width: 40.534rem;
    height: 0.64rem;
    margin: 0.774rem 0 0 5.334rem;
    display: flex;
    flex-direction: row;
    align-items: flex-start;

    .image_1 {
      width: 4.267rem;
      height: 0.64rem;
      flex-shrink: 0;
    }

        .nav-item {
      text-decoration: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      flex-shrink: 0;
    }

    .nav-dropdown {
      position: relative;
      cursor: pointer;
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background: #ffffff;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        padding: 12px 0;
        min-width: 200px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        z-index: 1000;
      }

      &:hover .dropdown-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .dropdown-item {
        display: block;
        padding: 12px 20px;
        color: #333333;
        text-decoration: none;
        font-size: 14px;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: #f5f5f5;
          color: #1890ff;
        }
      }
    }

    .text_1, .text_2, .text_3, .text_4, .text_5, .text_6 {
      height: 0.48rem;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 0.453rem;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 0.48rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        color: rgba(33, 135, 250, 1);
      }

      &.active {
        color: rgba(33, 135, 250, 1);
        font-family: PingFangSC-Medium;
        font-weight: 500;
      }
    }

    .text_1 {
      width: 0.907rem;
      margin: 0.08rem 0 0 15.867rem;
    }

    .text_2 {
      width: 1.814rem;
      margin: 0.08rem 0 0 2.08rem;
    }

    .thumbnail_1 {
      width: 0.48rem;
      height: 0.48rem;
      margin: 0.08rem 0 0 0.107rem;
    }

    .text_3 {
      width: 3.174rem;
      margin: 0.08rem 0 0 1.6rem;
    }

    .text_4 {
      width: 1.814rem;
      margin: 0.08rem 0 0 1.6rem;
    }

    .text_5 {
      width: 1.814rem;
      margin: 0.08rem 0 0 1.6rem;
    }

    .text_6 {
      width: 1.814rem;
      margin: 0.08rem 0 0 1.6rem;
    }
  }


}
</style>
