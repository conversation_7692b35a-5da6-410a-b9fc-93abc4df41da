{"name": "hyy-website-modern", "version": "1.0.0", "description": "慧医云官网 - 基于Nuxt.js的现代化前端项目", "author": "慧医云科技团队", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "start": "nuxt start", "lint": "eslint . --fix", "lint:style": "stylelint **/*.{vue,css,scss,less} --fix"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxt/eslint-config": "^0.2.0", "@nuxtjs/tailwindcss": "^6.8.4", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "eslint": "^8.55.0", "less": "^4.3.0", "nuxt": "^3.8.0", "postcss": "^8.4.32", "stylelint": "^15.11.0", "stylelint-config-standard": "^34.0.0", "tailwindcss": "^3.3.6"}, "dependencies": {"@element-plus/nuxt": "^1.0.7", "@nuxtjs/google-fonts": "^3.0.2", "@nuxtjs/sitemap": "^7.4.1", "@pinia/nuxt": "^0.5.1", "element-plus": "^2.4.4", "pinia": "^2.1.7", "sass": "^1.69.5", "swiper": "^11.2.8", "vue-toastification": "^2.0.0-rc.5"}, "overrides": {"vue": "latest"}}