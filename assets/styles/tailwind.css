@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  html {
    font-family: 'PingFang SC', 'Roboto', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  }

  body {
    @apply text-gray-900 bg-white;
    font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
  }

  * {
    box-sizing: border-box;
  }
}

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply bg-medical-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-xl shadow-lg p-6 border border-gray-100;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* 自定义工具样式 */
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .shadow-custom {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}
