// 通用样式 - 保持与demo一致的布局系统
body * {
  box-sizing: border-box;
  flex-shrink: 0;
}

body {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
  margin: 0;
  padding: 0;
}

button {
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  outline: none;
  background-color: transparent;
  cursor: pointer;
}

button:active {
  opacity: 0.6;
}

// Flex布局工具类 - 与demo保持一致
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-start {
  display: flex;
  justify-content: flex-start;
}

.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}

.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}

.justify-around {
  display: flex;
  justify-content: space-around;
}

.justify-between {
  display: flex;
  justify-content: space-between;
}

.align-start {
  display: flex;
  align-items: flex-start;
}

.align-center {
  display: flex;
  align-items: center;
}

.align-end {
  display: flex;
  align-items: flex-end;
}

// 页面容器样式
.page {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

// 响应式图片
img {
  max-width: 100%;
  height: auto;
}

// 文本样式
.text-wrapper {
  display: inline-block;
}

// 动画过渡
.transition-all {
  transition: all 0.3s ease;
}

// 移动端适配
@media (max-width: 768px) {
  .container-custom {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
