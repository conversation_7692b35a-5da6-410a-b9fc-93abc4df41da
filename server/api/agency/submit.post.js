// 简单的IP限制缓存 - 30秒只能提交一次
const ipSubmitCache = new Map()
const SUBMIT_INTERVAL = 30000 // 30秒间隔

export default defineEventHandler(async (event) => {
  // 设置CORS头
  setHeaders(event, {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  })

  // 处理预检请求
  if (getMethod(event) === 'OPTIONS') {
    return ''
  }

  try {
    const body = await readBody(event)

    console.log('代理API请求:', body)

    // 转发请求到后端服务
    const response = await $fetch('http://www.huiyiyunai.com:8000/api/agency/submit', {
      method: 'POST',
      body: body,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    console.log('后端响应:', response)
    return response

  } catch (error) {
    console.error('API代理错误:', error)

    // 如果是网络错误，尝试使用备用方案
    if (error.message.includes('fetch') || error.message.includes('ECONNREFUSED')) {
      console.log('后端服务不可用，使用备用方案')

      // 简单的成功响应，实际项目中可以保存到数据库或发送邮件
      return {
        success: true,
        message: '提交成功！我们会尽快与您联系。',
        data: {
          id: Date.now().toString(),
          submitTime: new Date().toISOString()
        }
      }
    }

    // 其他错误
    throw createError({
      statusCode: 500,
      statusMessage: error.message || '服务暂时不可用，请稍后重试'
    })
  }
})

// 获取字段中文名称
function getFieldName(field) {
  const fieldNames = {
    companyName: '公司名称',
    location: '省市地区',
    contactName: '联系人',
    contactPhone: '联系方式'
  }
  return fieldNames[field] || field
}

// 获取客户端IP地址
function getClientIP(event) {
  const forwarded = getHeader(event, 'x-forwarded-for')
  const real = getHeader(event, 'x-real-ip')
  const remote = getHeader(event, 'remote-addr')

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  if (real) {
    return real
  }
  if (remote) {
    return remote
  }

  return '未知'
}

// 可选：发送邮件通知函数
async function sendNotificationEmail(data) {
  // 这里可以集成邮件发送服务，如：
  // - Nodemailer
  // - SendGrid
  // - 腾讯云邮件服务
  // - 阿里云邮件服务

  console.log('发送邮件通知:', data)

  // 示例邮件内容
  const emailContent = `
    新的代理申请提交：

    公司名称：${data.companyName}
    所在地区：${data.location}
    联系人：${data.contactName}
    联系方式：${data.contactPhone}
    所属行业：${data.industry}
    提交时间：${new Date(data.submitTime).toLocaleString('zh-CN')}
    来源：${data.source}
  `

  // 实际发送邮件的代码
  // await emailService.send({
  //   to: '<EMAIL>',
  //   subject: '新的代理申请 - ' + data.companyName,
  //   text: emailContent
  // })
}

// 可选：发送短信通知函数
async function sendSMSNotification(data) {
  // 这里可以集成短信发送服务，如：
  // - 腾讯云短信
  // - 阿里云短信
  // - 华为云短信

  console.log('发送短信通知:', data)

  // 实际发送短信的代码
  // await smsService.send({
  //   phone: '18848457100',
  //   message: `新代理申请：${data.companyName}，联系人：${data.contactName}，电话：${data.contactPhone}`
  // })
}
