export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const page = parseInt(query.page) || 1
    const limit = parseInt(query.limit) || 20
    const status = query.status || 'all'

    // 读取数据文件
    const fs = await import('fs/promises')
    const path = await import('path')

    const dataDir = path.join(process.cwd(), 'data')
    const filePath = path.join(dataDir, 'agency-submissions.json')

    let submissions = []

    try {
      const fileContent = await fs.readFile(filePath, 'utf-8')
      submissions = JSON.parse(fileContent)
    } catch (error) {
      // 文件不存在或格式错误
      submissions = []
    }

    // 按提交时间倒序排列
    submissions.sort((a, b) => new Date(b.submitTime) - new Date(a.submitTime))

    // 状态筛选
    if (status !== 'all') {
      submissions = submissions.filter(item => item.status === status)
    }

    // 分页
    const total = submissions.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedData = submissions.slice(startIndex, endIndex)

    // 统计信息
    const stats = {
      total: submissions.length,
      pending: submissions.filter(item => item.status === 'pending').length,
      processed: submissions.filter(item => item.status === 'processed').length,
      rejected: submissions.filter(item => item.status === 'rejected').length
    }

    return {
      success: true,
      data: {
        list: paginatedData,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        stats
      }
    }

  } catch (error) {
    console.error('获取代理申请列表错误:', error)

    return {
      success: false,
      message: '获取数据失败',
      data: {
        list: [],
        pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
        stats: { total: 0, pending: 0, processed: 0, rejected: 0 }
      }
    }
  }
})
