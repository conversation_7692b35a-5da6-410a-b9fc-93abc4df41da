<template>
  <div id="app">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<script setup>
// 设置页面头部信息
useHead({
  title: '慧医云科技 - AI医疗解决方案服务商',
  meta: [
    { name: 'description', content: '四川慧医云科技，聚焦医药及医疗行业，提供AI深度业务应用的解决方案服务商' },
    { name: 'keywords', content: 'AI医疗,元智启,AI医助,AI患助,智能营销,医疗科技' },
    { property: 'og:title', content: '慧医云科技 - AI医疗解决方案服务商' },
    { property: 'og:description', content: '四川慧医云科技，聚焦医药及医疗行业，提供AI深度业务应用的解决方案服务商' },
    { property: 'og:type', content: 'website' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
  ]
})
</script>

<style>
#app {
  min-height: 100vh;
  width: 100%;
}
</style>
