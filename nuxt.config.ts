// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  // CSS配置
  css: [
    '~/assets/styles/main.scss',
    '~/assets/styles/common.scss'
  ],

  // 模块配置
  modules: [
    '@nuxtjs/tailwindcss',
    '@element-plus/nuxt',
    '@pinia/nuxt',
    '@nuxtjs/google-fonts',
    '@nuxtjs/sitemap'
  ],

  // Element Plus配置
  elementPlus: {
    /** Options */
  },

  // Tailwind CSS配置
  tailwindcss: {
    cssPath: '~/assets/styles/tailwind.css',
    configPath: 'tailwind.config.js'
  },

  // Google Fonts配置
  googleFonts: {
    families: {
      'PingFang SC': [300, 400, 500, 600, 700],
      'Roboto': [300, 400, 500, 700],
      'Helvetica Neue': [300, 400, 500, 700]
    },
    display: 'swap'
  },

  // 应用配置
  app: {
    head: {
      title: '慧医云科技 - AI医疗解决方案服务商',
      titleTemplate: '%s | 慧医云科技',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: '四川慧医云科技，聚焦医药及医疗行业，提供AI深度业务应用的解决方案服务商' },
        { name: 'keywords', content: 'AI医疗,元智启,AI医助,AI患助,智能营销,医疗科技' },
        { name: 'author', content: '四川慧医云科技有限公司' },
        { name: 'generator', content: 'Nuxt.js' },
        { name: 'theme-color', content: '#2187FA' },
        { name: 'msapplication-TileColor', content: '#2187FA' },
        { property: 'og:site_name', content: '慧医云科技' },
        { property: 'og:locale', content: 'zh_CN' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
        { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
        { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },
        { rel: 'manifest', href: '/site.webmanifest' }
      ],
      script: [
        { src: '/flexible.js' },
        {
          innerHTML: `
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?aa80a7079c2ffc2ad68bbdd19101c199";
              var s = document.getElementsByTagName("script")[0];
              s.parentNode.insertBefore(hm, s);
            })();
          `,
          type: 'text/javascript'
        }
      ]
    }
  },

  // 运行时配置
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3000/api'
    }
  },

  // 构建配置
  build: {
    transpile: ['element-plus/es']
  },

    // 服务器配置
  nitro: {
    compressPublicAssets: true,
    // 开发环境代理配置
    devProxy: {
      '/api': {
        target: 'http://www.huiyiyunai.com:8000',
        changeOrigin: true,
        prependPath: true
      }
    }
  },

  // 路由规则 - 生产环境代理
  routeRules: {
    '/api/**': {
      proxy: 'http://www.huiyiyunai.com:8000/api/**',
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    }
  },

  // SEO 和站点地图配置
  sitemap: {
    hostname: 'https://www.huiyiyunai.com',
    gzip: true,
    routes: [
      '/',
      '/about',
      '/agency',
      '/news',
      '/contact',
      '/products/yuanzhiqi',
      '/products/aiyizhu',
      '/products/aihuanzhu',
      '/products/marketing'
    ]
  },

  // 实验性功能
  experimental: {
    payloadExtraction: false
  },

  // SSR 和 SEO 优化
  ssr: true,

  // 兼容性配置
  compatibilityDate: '2024-01-01'
})
