<template>
  <div class="page flex-col">
    <div class="group_19 flex-col">
      <div class="block_1 flex-col">
        <img
          class="image_2"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png"
          alt="元智启AI应用配置平台"
        />
        <div class="text-wrapper_1">
          <span class="text_7">元智启</span>
          <span class="text_8">-企业级AI应用配置平台</span>
        </div>
        <span class="text_9">低成本、无门槛、AI应用轻松配</span>
        <div class="group_2 flex-row">
          <div class="image-text_12 flex-row justify-between" @click="openAppStore" style="cursor: pointer;">
            <img
              class="thumbnail_2"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPng98a314bf56bfbaba64a312684441b9ada5121f97b8229a839e5595d5380bcef8.png"
              alt="应用广场"
            />
            <span class="text-group_1">应用广场</span>
          </div>
        </div>
      </div>
      <div class="block_2 flex-col justify-end">
        <div class="box_3 flex-row">
          <div class="text-wrapper_2 flex-col" @click="scrollToSection('about')"><span class="text_10">关于元智启ai</span></div>
          <div class="text-wrapper_3 flex-col" @click="scrollToSection('advantages')"><span class="text_11">核心优势</span></div>
          <div class="text-wrapper_4 flex-col" @click="scrollToSection('capabilities')"><span class="text_12">产品能力</span></div>
          <div class="text-wrapper_5 flex-col" @click="scrollToSection('applications')"><span class="text_13">应用场景</span></div>
        </div>
        <div id="about" class="text-group_21 flex-col justify-between">
          <span class="text_14">元智启&nbsp;-&nbsp;企业级AI应用配置平台</span>
          <span class="text_15">企业级AI应用配置平台，支持零代码搭建多种智能应用</span>
        </div>
        <div class="box_4 flex-col">
          <div class="group_3 flex-col">
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPng871191049369103473f8ab12ad255b614220dc5931e5d621f78e1f13153eae78.png"
              alt="元智启AI平台介绍"
            />
            <span class="paragraph_1">
              元智启ai是四川慧医云科技打造的AI应用零代码配置平台，
              <br />
              致力于为企业提供高效智能化的数字化转型解决方案。
              <br />
              平台通过零代码配置技术，帮助企业快速构建专业级智能应用，
              <br />
              具备部署灵活、成本可控、场景适配性强三大核心优势，
              <br />
              可显著降低AI技术应用门槛。
              <br />
            </span>
            <div class="box_5 flex-col">
              <div class="text-wrapper_6">
                <span class="text_16">平台深度适配各类企业应用场景：</span>
                <span class="paragraph_2">
                  &nbsp;成功应用于"智能客服、智能法律咨询、智能随访、智能问/导诊、对话智能生成病例、智能辅诊、智能库存补货、智能资质审核、智能培训、智能财务单据校验、微信智能营销、智能推荐"等诸多应用场景。
                  <br />
                </span>
              </div>
              <img
                class="image_4"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng4345e9f271eabbd7bc13c4627f38785e88b2de434436ddf4d723b57ff4f4ca84.png"
                alt="大模型集成展示"
              />
              <span class="paragraph_3">
                元智启ai目前已接入了deepseek、通义千问、豆包、文心一言、智普等诸多通用大模型及行业大模型。
                <br />
                具备聚合多模态、知识库、接口、数据库、插件和工作流等Agent构建能力，可以将各个模块灵活应用调度，零代码搭建私有知识以及复杂任务执行能力的&nbsp;Agent&nbsp;智能体，并一键接入主流&nbsp;IM&nbsp;和办公协同平台等渠道。
              </span>
            </div>
          </div>
        </div>
      </div>
      <div id="advantages" class="block_3 flex-row">
        <div class="section_11 flex-col justify-between">
          <div class="box_6 flex-row">
            <div class="text-group_22 flex-col justify-between">
              <span class="text_17">低成本</span>
              <span class="paragraph_4">
                开发成本低，时间成本低
                <br />
                人员能力要求低
              </span>
            </div>
            <img
              class="image_5"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPng4e1be1ba55a2c6ec65a96cef6f010c097c2bc19eca198cd172b07d5eb53f24e8.png"
              alt="低成本优势"
            />
          </div>
          <div class="box_7 flex-row">
            <div class="text-group_23 flex-col justify-between">
              <span class="text_18">配置快</span>
              <span class="paragraph_5">
                轻量级配置
                <br />
                一个AI智能体1个小时搞定
              </span>
            </div>
            <img
              class="image_6"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngeb9c43ecb6d97471b4a6441c5491669a1379bedd076ad6f978b9c54040d2e106.png"
              alt="配置快速优势"
            />
          </div>
        </div>
        <div class="section_12 flex-col justify-between">
          <div class="box_8 flex-row">
            <div class="text-group_24 flex-col justify-between">
              <span class="text_19">无门槛</span>
              <span class="paragraph_6">
                小白也能快速掌握
                <br />
                轻松搭建AI智能体
              </span>
            </div>
            <img
              class="image_7"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPng2f0ca918ca6717d0c7d3b7de34acd89dac93f52e33f993c58e531a5baf42132d.png"
              alt="无门槛优势"
            />
          </div>
          <div class="box_9 flex-row">
            <div class="text-group_25 flex-col justify-between">
              <span class="text_20">零代码</span>
              <span class="paragraph_7">
                区别于其他AI开发平台
                <br />
                可实现无代码配置
              </span>
            </div>
            <img
              class="image_8"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngeb66ce13dc35d96466050c2b49094c62ad219d0e5b70913f0e8ce1f63c3cbf62.png"
              alt="零代码优势"
            />
          </div>
        </div>
        <div class="group_6 flex-col">
          <div class="text-group_26 flex-col justify-between">
            <span class="text_21">核心优势</span>
            <span class="text_22">企业&nbsp;AI&nbsp;应用创新平台</span>
          </div>
          <div class="section_3 flex-row">
            <div class="text-group_27 flex-col justify-between">
              <span class="text_23">零代码</span>
              <span class="paragraph_8">
                区别于其他AI开发平台
                <br />
                可实现无代码配置
              </span>
            </div>
            <img
              class="image_9"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngcd7e9ff8a62d010ec16cb6041346f7dd90ea4b404e3f4b27509ff919ad617556.png"
              alt="核心优势展示"
            />
          </div>
        </div>
      </div>
      <div id="capabilities" class="block_4 flex-col">
        <div class="block_11 flex-row">
          <div class="text-group_28 flex-col justify-between">
            <span class="text_24">产品能力</span>
            <span class="text_25">企业&nbsp;AI&nbsp;应用创新平台，低代码轻松构建智能体</span>
          </div>
        </div>
        <div class="section_20 flex-row justify-between">
          <div class="group_38 flex-col justify-between">
            <div class="menu-item" :class="{ 'active': activeTab === 'model' }" @click="setActiveTab('model')">
              <div class="active-indicator" v-if="activeTab === 'model'"></div>
              <img
                class="menu-icon"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng0797b63ecddb01a4fb65680b7d54691691c02beb7cefa85c7de0840529aa3955.png"
                alt="大模型"
              />
              <span class="menu-text">大模型</span>
            </div>

            <div class="menu-item" :class="{ 'active': activeTab === 'knowledge' }" @click="setActiveTab('knowledge')">
              <div class="active-indicator" v-if="activeTab === 'knowledge'"></div>
              <img
                class="menu-icon"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng028cabe928905ec677678f28f8852107a97626bfc5256f99b6f23d1556e4e2f2.png"
                alt="知识库"
              />
              <span class="menu-text">知识库</span>
            </div>

            <div class="menu-item" :class="{ 'active': activeTab === 'database' }" @click="setActiveTab('database')">
              <div class="active-indicator" v-if="activeTab === 'database'"></div>
              <img
                class="menu-icon"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng803ff1e203f09ca036a77389562f0213dfa8be097d8a92db4e75d9fc604a8f0b.png"
                alt="数据库"
              />
              <span class="menu-text">数据库</span>
            </div>

            <div class="menu-item" :class="{ 'active': activeTab === 'plugin' }" @click="setActiveTab('plugin')">
              <div class="active-indicator" v-if="activeTab === 'plugin'"></div>
              <img
                class="menu-icon"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng14734cb58d2862a7482bf1c680a76051a0aa7eebb23dd7f2a975776e9c48b2b2.png"
                alt="插件"
              />
              <span class="menu-text">插件</span>
            </div>

            <div class="menu-item" :class="{ 'active': activeTab === 'workflow' }" @click="setActiveTab('workflow')">
              <div class="active-indicator" v-if="activeTab === 'workflow'"></div>
              <img
                class="menu-icon"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng0022e10f7a254bc8e0c72599e3f08d9e294c8e7be19ade71e8717f574b29c4e6.png"
                alt="工作流"
              />
              <span class="menu-text">工作流</span>
            </div>

            <div class="menu-item" :class="{ 'active': activeTab === 'channel' }" @click="setActiveTab('channel')">
              <div class="active-indicator" v-if="activeTab === 'channel'"></div>
              <img
                class="menu-icon"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng984add0fddca3424201386e11bd07ff64145f81041e48da2fc1db96c83aa3109.png"
                alt="渠道接入"
              />
              <span class="menu-text">渠道接入</span>
            </div>
          </div>

          <div class="content-area">
            <!-- 大模型内容 -->
            <div v-show="activeTab === 'model'" class="group_29 flex-row">
              <div class="group_30 flex-col">
                <span class="text_39">大模型</span>
                <div class="text-wrapper_20">
                  <span class="paragraph_9">
                    构建了强大的多模型融合体系，
                    <br />
                    整合了
                  </span>
                  <span class="text_40">业界领先</span>
                  <span class="paragraph_9">
                    的通用大模型及行业大模型。
                    <br />
                  </span>
                </div>
                <img
                  class="image_4"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPnga394389a50c427d3aac2718029e5e6837bf64f0b49c1098b0ed0fe784659378a.png"
                  alt="大模型融合体系"
                />
                <span class="paragraph_10">模型如下：</span>
                <div class="box_37 flex-row justify-between">
                  <div class="text-wrapper_4 flex-col"><span class="text_8">DeepSeek</span></div>
                  <div class="text-wrapper_5 flex-col"><span class="text_9">Kimi</span></div>
                  <div class="text-wrapper_6 flex-col"><span class="text_10">Qwen</span></div>
                </div>
                <div class="box_38 flex-row justify-between">
                  <div class="text-wrapper_7 flex-col"><span class="text_11">Doubao</span></div>
                  <div class="text-wrapper_8 flex-col"><span class="text_12">智谱</span></div>
                  <div class="text-wrapper_9 flex-col"><span class="text_13">文心一言</span></div>
                </div>
                <div class="box_39 flex-row justify-between">
                  <div class="box_13 flex-col">
                    <div class="text-wrapper_10">
                      <span class="text_14">专业领域大模型</span>
                      <span class="text_15">(千问法律)</span>
                    </div>
                  </div>
                  <div class="text-wrapper_11 flex-col"><span class="text_16">讯飞星火</span></div>
                </div>
              </div>
              <div class="group_31 flex-col">
                <div class="box_29 flex-col">
                  <img
                    class="demo-image"
                    referrerpolicy="no-referrer"
                    src="/pages/assets/img/damoxing.png"
                    alt="大模型演示界面"
                  />
                </div>
              </div>
            </div>

            <!-- 知识库内容 -->
            <div v-show="activeTab === 'knowledge'" class="group_29 flex-row">
              <div class="group_30 flex-col">
                <span class="text_39">知识库</span>
                <div class="text-wrapper_20">
                  <span class="text_40">支持</span>
                  <span class="paragraph_9">文档、问答、表格、网站</span>
                  <span class="paragraph_9">
                    等多元数据格式的无缝接入
                    <br />
                    实现全类型知识资产的一站式管理
                  </span>
                </div>
                <img
                  class="image_4"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPnga394389a50c427d3aac2718029e5e6837bf64f0b49c1098b0ed0fe784659378a.png"
                  alt="知识库管理系统"
                />
                <span class="paragraph_10">
                  针对非结构化文档，系统采用先进的智能分段算法，
                  <br />
                  基于语义分析和标点特征自动优化文本结构，
                  <br />
                  将冗长内容智能拆分为逻辑清晰的段落单元，
                  <br />
                  确保知识提取的精准性和检索效率的最大化。
                </span>
              </div>
              <div class="group_31 flex-col">
                <div class="box_29 flex-col">
                  <img
                    class="demo-image"
                    referrerpolicy="no-referrer"
                    src="/pages/assets/img/zhishiku.png"
                    alt="知识库演示界面"
                  />
                </div>
              </div>
            </div>

            <!-- 数据库内容 -->
            <div v-show="activeTab === 'database'" class="group_29 flex-row">
              <div class="group_30 flex-col">
                <span class="text_39">数据库</span>
                <div class="text-wrapper_20">
                  <span class="paragraph_9">数据库让大模型具备了</span>
                  <span class="text_40">直接访问结构化数据</span>
                  <span class="paragraph_9">
                    的能力，
                    <br />
                    既支持连接外部远程数据库，
                    <br />
                    也提供平台内置的托管数据库。
                  </span>
                </div>
                <img
                  class="image_4"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPnga394389a50c427d3aac2718029e5e6837bf64f0b49c1098b0ed0fe784659378a.png"
                  alt="数据库系统"
                />
                <span class="paragraph_10">
                  用户完全可以通过自然对话的方式，
                  <br />
                  轻松实现数据查询、新增、编辑和删除等操作，
                  <br />
                  真正做到了"用对话管理数据"的智能体验。
                </span>
              </div>
              <div class="group_31 flex-col">
                <div class="box_29 flex-col">
                  <img
                    class="demo-image"
                    referrerpolicy="no-referrer"
                    src="/pages/assets/img/shujuku.png"
                    alt="数据库演示界面"
                  />
                </div>
              </div>
            </div>

            <!-- 插件内容 -->
            <div v-show="activeTab === 'plugin'" class="group_29 flex-row">
              <div class="group_30 flex-col">
                <span class="text_39">插件</span>
                <div class="text-wrapper_20">
                  <span class="paragraph_9">插件是对大模型能力的</span>
                  <span class="text_40">强力扩展</span>
                  <span class="paragraph_9">
                    <br />
                    通过开放API接口实现与外部系统的智能交互
                  </span>
                </div>
                <img
                  class="image_4"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPnga394389a50c427d3aac2718029e5e6837bf64f0b49c1098b0ed0fe784659378a.png"
                  alt="插件系统"
                />
                <span class="paragraph_10">
                  平台提供丰富的插件生态，包括：
                  <br />
                  文生图、图生图、语音识别、图像识别、AI搜索等核心功能模块，以及新闻早报、天气查询、物流查询等特色插件，全方位满足各类业务场景需求。
                  <br />
                </span>
              </div>
              <div class="group_31 flex-col">
                <div class="box_29 flex-col">
                  <img
                    class="demo-image"
                    referrerpolicy="no-referrer"
                    src="/pages/assets/img/chajian.png"
                    alt="插件演示界面"
                  />
                </div>
              </div>
            </div>

            <!-- 工作流内容 -->
            <div v-show="activeTab === 'workflow'" class="group_29 flex-row">
              <div class="group_30 flex-col">
                <span class="text_39">工作流</span>
                <div class="text-wrapper_20">
                  <span class="paragraph_9">
                    工作流重新定义了智能体构建方式
                    <br />
                    通过
                  </span>
                  <span class="text_40">可视化编排</span>
                  <span class="text_41">实现AI能力的</span>
                  <span class="text_42">自由组合与深度协同</span>
                </div>
                <img
                  class="image_4"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPnga394389a50c427d3aac2718029e5e6837bf64f0b49c1098b0ed0fe784659378a.png"
                  alt="工作流系统"
                />
                <span class="paragraph_10">
                  支持将应用模块、知识库系统、功能插件、意图识别引擎、定时任务以及多渠道发送能力进行智能串联，&nbsp;打造面向复杂业务流程的AI解决方案。
                  <br />
                  工作流具备强大的交叉编排能力，&nbsp;可实现多应用联动、多知识库协同、多数据库交互，&nbsp;让AI智能体真正掌握端到端的流程化任务执行能力。
                  <br />
                </span>
              </div>
              <div class="group_31 flex-col">
                <div class="box_29 flex-col">
                  <img
                    class="demo-image"
                    referrerpolicy="no-referrer"
                    src="/pages/assets/img/gongzuoliu.png"
                    alt="工作流演示界面"
                  />
                </div>
              </div>
            </div>

            <!-- 渠道接入内容 -->
            <div v-show="activeTab === 'channel'" class="group_29 flex-row">
              <div class="group_30 flex-col">
                <span class="text_39">渠道接入</span>
                <div class="text-wrapper_20">
                  <span class="paragraph_9">
                    全渠道接入方案实现了AI能力的无缝部署
                    <br />
                    支持主流平台的一键快速接入
                  </span>
                </div>
                <img
                  class="image_4"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPnga394389a50c427d3aac2718029e5e6837bf64f0b49c1098b0ed0fe784659378a.png"
                  alt="渠道接入系统"
                />
                <span class="paragraph_10">
                  包括微信公众号、企业微信、个人微信、钉钉、飞书等办公协作平台。
                  <br />
                  同时提供灵活的集成方式，
                  <br />
                  既可通过标准化封装快速嵌入您的Web网站，
                  <br />
                  也能通过开放API与自研系统或第三方平台深度集成，
                  <br />
                  真正实现AI服务在任何场景下的即插即用。&nbsp;
                  <br />
                </span>
              </div>
              <div class="group_31 flex-col">
                <div class="box_29 flex-col">
                  <img
                    class="demo-image"
                    referrerpolicy="no-referrer"
                    src="/pages/assets/img/qudao.png"
                    alt="渠道接入演示界面"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="applications" class="box_15 flex-col">
      <div class="text-group_29 flex-col justify-between">
        <span class="text_38">丰富的应用场景和解决方案</span>
        <span class="text_39">满足多种业务需求</span>
      </div>
      <div class="text-wrapper_21 flex-row justify-between">
        <span
          class="scenario-nav-item"
          :class="{ 'active': activeScenario === 'service' }"
          @click="setActiveScenario('service')"
        >智能客服</span>
        <span
          class="scenario-nav-item"
          :class="{ 'active': activeScenario === 'marketing' }"
          @click="setActiveScenario('marketing')"
        >私域营销</span>
        <span
          class="scenario-nav-item"
          :class="{ 'active': activeScenario === 'data' }"
          @click="setActiveScenario('data')"
        >数据助手</span>
        <span
          class="scenario-nav-item"
          :class="{ 'active': activeScenario === 'training' }"
          @click="setActiveScenario('training')"
        >智能培训</span>
        <span
          class="scenario-nav-item"
          :class="{ 'active': activeScenario === 'diagnosis' }"
          @click="setActiveScenario('diagnosis')"
        >辅助诊疗</span>
        <!-- <span
          class="scenario-nav-item"
          :class="{ 'active': activeScenario === 'community' }"
          @click="setActiveScenario('community')"
        >社群营销</span> -->
      </div>
      <!-- <div class="scenario-underline" :style="{ left: getUnderlinePosition() }"></div> -->

      <!-- 智能客服场景 -->
      <div class="box_23 flex-row justify-between" v-show="activeScenario === 'service'">
        <div class="group_12 flex-col">
          <div class="group_20 flex-row justify-between">
            <div class="text-wrapper_22 flex-col justify-between">
              <span class="text_46">智能客服</span>
              <span class="paragraph_11">
                基于日常客户问答咨询的积累<br />
                将高频问题整理归档至知识库系统
              </span>
            </div>
            <div class="image-wrapper_1 flex-col">
              <img
                class="image_10"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng87302804804bde3ae337bc90cb08fc21e122416bb73ba1e7e202476b24730346.png"
                alt="智能客服图标"
              />
            </div>
          </div>
          <img
            class="image_11"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
            alt="场景说明"
          />
          <span class="paragraph_12">
            通过与企业系统对接，将智能体集成至企业商城平台后，<br />
            实现实时获取咨询客户的身份信息、<br />
            订单状态及物流详情等业务数据；<br />
            分析客户咨询内容并自动匹配知识库进行回复；<br />
            当问题需人工介入时，<br />
            智能体将自动在企业工单系统中生成待办任务，<br />
            实现服务流程闭环。
          </span>
        </div>
        <div class="group_14 flex-col">
          <img
            class="demo-image"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng0960d50b72a74c57fcfb8cb73537a2a4d7f94d6eb95f1b84085374dc0f89468d.png"
            alt="智能客服应用场景演示"
          />
        </div>
      </div>

      <!-- 私域营销场景 -->
      <div class="box_23 flex-row justify-between" v-show="activeScenario === 'marketing'">
        <div class="group_12 flex-col">
          <div class="group_20 flex-row justify-between">
            <div class="text-wrapper_22 flex-col justify-between">
              <span class="text_46">私域营销</span>
              <span class="paragraph_11">
                AI可担任产品导购角色进行仿真沟通，当客户有购买意向时可自动转人工并提醒。
              </span>
            </div>
            <div class="image-wrapper_1 flex-col">
              <img
                class="image_10"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng8416936dee0c9ddc153042a2fb9fa695cf139a03af28eb2e39b497771992ef6d.png"
                alt="私域营销图标"
              />
            </div>
          </div>
          <img
            class="image_11"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
            alt="场景说明"
          />
          <span class="paragraph_12">
            在私域营销场景中，可以把智能体接入企业微信，基于工作流的渠道消息发送能力，可配置定时触达任务，客户回复后 AI 接管对话。
          </span>
        </div>
        <div class="group_14 flex-col">
          <img
            class="demo-image"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng00cd2391c35f5c71bd0d912ea5896413c1a12799fc8f3f138bf08fd59fe30e94.png"
            alt="私域营销应用场景演示"
          />
        </div>
      </div>

      <!-- 数据助手场景 -->
      <div class="box_23 flex-row justify-between" v-show="activeScenario === 'data'">
        <div class="group_12 flex-col">
          <div class="group_20 flex-row justify-between">
            <div class="text-wrapper_22 flex-col justify-between">
              <span class="text_46">数据助手</span>
              <span class="paragraph_11">
                用对话交互取代流程和界面交互 高效实现 "数据在手边，一句话可查"
              </span>
            </div>
            <div class="image-wrapper_1 flex-col">
              <img
                class="image_10"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng94ac18f79fb3f8e7cba704fbb52043c05df74695e2a85614ea3cbcc069539657.png"
                alt="数据助手图标"
              />
            </div>
          </div>
          <img
            class="image_11"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
            alt="场景说明"
          />
          <span class="paragraph_12">
            在企业数据应用场景中，打通企业的数据库，实现对话式数据查询、数据分析、数据报告下载等功能。
          </span>
        </div>
        <div class="group_14 flex-col">
          <img
            class="demo-image"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng2fdf9b3bc26566cc3c246d6d3a7417ced197a71f9086e814804310473cb04183.png"
            alt="数据助手应用场景演示"
          />
        </div>
      </div>

      <!-- 智能培训场景 -->
      <div class="box_23 flex-row justify-between" v-show="activeScenario === 'training'">
        <div class="group_12 flex-col">
          <div class="group_20 flex-row justify-between">
            <div class="text-wrapper_22 flex-col justify-between">
              <span class="text_46">智能培训</span>
              <span class="paragraph_11">
                药企用AI技术打造智能培训助手，专门帮慢病管理专员提升专业能力
              </span>
            </div>
            <div class="image-wrapper_1 flex-col">
              <img
                class="image_10"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng05dd9f342f3f60d662ade1f7c426538c7eec2b07f5e1dcf9c13f4facd6121b7d.png"
                alt="智能培训图标"
              />
            </div>
          </div>
          <img
            class="image_11"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
            alt="场景说明"
          />
          <span class="paragraph_12">
            专员可以自由选择糖尿病、高血压等8种常见慢病类型，还能分「初级-中级-高级」调整难度，智能助手会生成各种真实病人案例，模拟从问诊到开药的全过程，最后生成总结报告，形成「模拟训练-弱点分析-定向提升」的完整学习闭环。
          </span>
        </div>
        <div class="group_14 flex-col">
          <img
            class="demo-image"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng2f10e8205f35a423bf6698dcd1bb200e8573c21d9415e48bc8127eb439df0192.png"
            alt="智能培训应用场景演示"
          />
        </div>
      </div>

      <!-- 辅助诊疗场景 -->
      <div class="box_23 flex-row justify-between" v-show="activeScenario === 'diagnosis'">
        <div class="group_12 flex-col">
          <div class="group_20 flex-row justify-between">
            <div class="text-wrapper_22 flex-col justify-between">
              <span class="text_46">辅助诊疗</span>
              <span class="paragraph_11">
                医疗企业自主研发的中医AI辅诊应用端，通过API调用实现智能辩证开方。
              </span>
            </div>
            <div class="image-wrapper_1 flex-col">
              <img
                class="image_10"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng4e27d3cf4cf73e9212295d16d88a5c48430367d1517f23050c2a0b711621d41b.png"
                alt="辅助诊疗图标"
              />
            </div>
          </div>
          <img
            class="image_11"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
            alt="场景说明"
          />
          <span class="paragraph_12">
            系统调用平台图像识别+大模型能力，精准辨别患者舌象图片特征，输出患者舌诊信息，并结合患者主诉症状，调用大模型进行八纲辨证，最终生成个性化处方，并附带煎服禁忌与复诊评估提示。
          </span>
        </div>
        <div class="group_14 flex-col">
          <img
            class="demo-image"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng82928afcd1995f80e9b55eb84978fc16a44d76fd3bdb2b8a20427144b486cd8c.png"
            alt="辅助诊疗应用场景演示"
          />
        </div>
      </div>

      <!-- 社群营销场景
      <div class="box_23 flex-row justify-between" v-show="activeScenario === 'community'">
        <div class="group_12 flex-col">
          <div class="group_20 flex-row justify-between">
            <div class="text-wrapper_22 flex-col justify-between">
              <span class="text_46">社群营销</span>
              <span class="paragraph_11">
                基于社群场景的智能营销解决方案，提升社群活跃度和转化率。
              </span>
            </div>
            <div class="image-wrapper_1 flex-col">
              <img
                class="image_10"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng82c1901d30abd09f888b668414459a53656f6b6c2b682950ff50eb3b22baed98.png"
                alt="社群营销图标"
              />
            </div>
          </div>
          <img
            class="image_11"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
            alt="场景说明"
          />
          <span class="paragraph_12">
            通过智能体在社群中进行自动化营销，识别用户需求，提供个性化服务，实现社群价值最大化。
          </span>
        </div>
        <div class="group_14 flex-col">
          <img
            class="demo-image"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/97c52acfdd66430698907d71bc78a1d7_mergeImage.png"
            alt="社群营销应用场景演示"
          />
        </div>
      </div> -->
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'

// SEO元数据设置
useHead({
  title: '元智启 - 企业级AI应用配置平台 | 零代码搭建智能体',
  meta: [
    { name: 'description', content: '元智启是四川慧医云科技打造的企业级AI应用零代码配置平台。支持低成本、无门槛搭建智能客服、私域营销、数据助手等AI智能体，集成多个大模型，提供知识库、数据库、插件、工作流等完整解决方案。' },
    { name: 'keywords', content: '元智启,AI应用配置平台,零代码AI,智能体搭建,企业AI解决方案,智能客服,私域营销,数据助手,大模型,知识库,工作流' },
    { name: 'author', content: '四川慧医云科技有限公司' },
    { name: 'robots', content: 'index, follow' },
    { name: 'googlebot', content: 'index, follow' },
    { name: 'baiduspider', content: 'index, follow' },

    // 页面分类和语言
    { name: 'category', content: 'AI软件' },
    { name: 'language', content: 'zh-CN' },
    { name: 'geo.region', content: 'CN-SC' },
    { name: 'geo.placename', content: '成都' },

    // 移动端优化
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
    { name: 'format-detection', content: 'telephone=no' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
    { name: 'theme-color', content: '#1976d2' },

    // Open Graph标签
    { property: 'og:title', content: '元智启 - 企业级AI应用配置平台 | 零代码搭建智能体' },
    { property: 'og:description', content: '元智启是四川慧医云科技打造的企业级AI应用零代码配置平台。支持低成本、无门槛搭建智能客服、私域营销、数据助手等AI智能体，集成多个大模型，提供知识库、数据库、插件、工作流等完整解决方案。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://www.huiyiyun.com/products/yuanzhiqi' },
    { property: 'og:image', content: 'https://www.huiyiyun.com/og-image-yuanzhiqi.jpg' },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:alt', content: '元智启AI应用配置平台产品界面展示' },
    { property: 'og:site_name', content: '慧医云科技' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '元智启 - 企业级AI应用配置平台' },
    { name: 'twitter:description', content: '零代码搭建AI智能体，低成本、无门槛的企业AI解决方案' },
    { name: 'twitter:image', content: 'https://www.huiyiyun.com/og-image-yuanzhiqi.jpg' },
    { name: 'twitter:image:alt', content: '元智启AI应用配置平台产品界面展示' },

    // 其他SEO优化标签
    { name: 'coverage', content: 'Worldwide' },
    { name: 'distribution', content: 'Global' },
    { name: 'rating', content: 'General' },
    { name: 'revisit-after', content: '7 days' }
  ],
  link: [
    { rel: 'canonical', href: 'https://www.huiyiyun.com/products/yuanzhiqi' },
    { rel: 'alternate', href: 'https://www.huiyiyun.com/products/yuanzhiqi', hreflang: 'zh-CN' },
    { rel: 'preload', href: '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png', as: 'image' }
  ]
})

// 结构化数据
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: '元智启',
        description: '企业级AI应用零代码配置平台，支持快速搭建智能客服、私域营销、数据助手等AI智能体',
        brand: {
          '@type': 'Organization',
          name: '四川慧医云科技有限公司',
          url: 'https://www.huiyiyun.com'
        },
        manufacturer: {
          '@type': 'Organization',
          name: '四川慧医云科技有限公司',
          url: 'https://www.huiyiyun.com'
        },
        category: 'AI软件',
        url: 'https://www.huiyiyun.com/products/yuanzhiqi',
        image: [
          'https://www.huiyiyun.com/og-image-yuanzhiqi.jpg',
          '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png'
        ],
        offers: {
          '@type': 'Offer',
          availability: 'https://schema.org/InStock',
          price: '0',
          priceCurrency: 'CNY',
          priceValidUntil: '2025-12-31',
          url: 'https://www.huiyiyun.com/products/yuanzhiqi'
        },
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: '4.8',
          bestRating: '5',
          worstRating: '1',
          ratingCount: '156'
        },
        additionalProperty: [
          {
            '@type': 'PropertyValue',
            name: '零代码配置',
            value: true
          },
          {
            '@type': 'PropertyValue',
            name: '多模型集成',
            value: true
          },
          {
            '@type': 'PropertyValue',
            name: '知识库管理',
            value: true
          },
          {
            '@type': 'PropertyValue',
            name: '工作流编排',
            value: true
          },
          {
            '@type': 'PropertyValue',
            name: '全渠道接入',
            value: true
          }
        ]
      })
    }
  ]
})

const activeTab = ref('model')
const activeScenario = ref('service')

const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

const setActiveTab = (tab) => {
  activeTab.value = tab
}

const setActiveScenario = (scenario) => {
  activeScenario.value = scenario
}

// 计算下划线位置
const getUnderlinePosition = () => {
  const scenarios = ['service', 'marketing', 'data', 'training', 'diagnosis']
  const index = scenarios.indexOf(activeScenario.value)
  const itemWidth = 1.92 // rem
  const itemMargin = 1.494 // rem
  const startOffset = 16.107 // rem

  return `${startOffset + index * (itemWidth + itemMargin)}rem`
}

// 跳转到应用广场
const openAppStore = () => {
  console.log('点击应用广场')
  window.open('http://www.yuanzhiqi.com:3002/login', '_blank')
}


</script>
<style scoped lang="less" src="./assets/yuanzhiqi.rem.less" />
