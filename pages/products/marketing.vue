<template>
  <div class="page flex-col">
    <header class="box_2 flex-col">
      <img
        class="image_2"
        referrerpolicy="no-referrer"
        src="/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png"
        alt="AI智能营销作战中心产品标志 - 慧医云科技智能营销解决方案"
        width="200"
        height="150"
        loading="lazy"
      />
      <h1 class="text_7">AI智能营销作战中心</h1>
      <p class="text_8">用AI驱动的动态作战沙盘</p>
      <div class="group_2 flex-col"></div>
    </header>
    <div class="box_3 flex-col justify-end">
      <nav class="block_3 flex-row" role="navigation" aria-label="页面导航">
        <div
          class="text-wrapper_1 flex-col"
          :class="{ active: activeSection === 'introduction' }"
          @click="scrollToSection('introduction')"
          role="button"
          tabindex="0"
          @keydown.enter="scrollToSection('introduction')"
          @keydown.space="scrollToSection('introduction')"
        >
          <span class="text_9">产品介绍</span>
        </div>
        <div
          class="text-wrapper_2 flex-col"
          :class="{ active: activeSection === 'advantages' }"
          @click="scrollToSection('advantages')"
          role="button"
          tabindex="0"
          @keydown.enter="scrollToSection('advantages')"
          @keydown.space="scrollToSection('advantages')"
        >
          <span class="text_10">核心优势</span>
        </div>
        <div
          class="text-wrapper_3 flex-col"
          :class="{ active: activeSection === 'capabilities' }"
          @click="scrollToSection('capabilities')"
          role="button"
          tabindex="0"
          @keydown.enter="scrollToSection('capabilities')"
          @keydown.space="scrollToSection('capabilities')"
        >
          <span class="text_11">产品能力</span>
        </div>
        <div
          class="text-wrapper_4 flex-col"
          :class="{ active: activeSection === 'applications' }"
          @click="scrollToSection('applications')"
          role="button"
          tabindex="0"
          @keydown.enter="scrollToSection('applications')"
          @keydown.space="scrollToSection('applications')"
        >
          <span class="text_12">应用场景</span>
        </div>
      </nav>
      <section id="introduction" class="text-group_9 flex-col justify-between">
        <h2 class="text_13">产品介绍</h2>
        <p class="text_14">AI智能营销作战中心，AI智能时代的行业智慧深度应用者</p>
      </section>
      <div class="text-wrapper_5">
        <span class="text_15">AI智能营销作战中心是一张用</span>
        <span class="text_16">AI驱动的动态作战沙盘</span>
        <span class="paragraph_1">
          ，将散乱的市场数据、客户需求、销售动线整合为"可点击、可追踪、可指挥"&nbsp;的智能地图。
          <br />
          <br />
          通过AI可视化的方式将
        </span>
        <span class="text_17">营销策略、市场占有、任务管理、客户画像</span>
        <span class="text_18">呈现给企业，让每个业务动作都精准命中靶心，助力企业营销管理及市场开发。</span>
      </div>
      <div class="block_4 flex-col">
        <div class="text-wrapper_6">
          <span class="text_19">慧医云拥有</span>
          <span class="text_20">全国140万+</span>
          <span class="text_21">终端药店、诊所、连锁、医院等客户精准定位信息及画像信息。</span>
        </div>
        <div class="block_15 flex-row justify-between">
          <div class="text-wrapper_7 flex-col">
            <p class="paragraph_2">
              透过智能作战地图，可精准掌握企业各地市客户的开发占有情况、各营销部门的客户分布情况、各类型客户的合作情况，各客户的画像情况。
              <br />
            </p>
          </div>
          <div class="text-wrapper_8 flex-col">
            <p class="paragraph_3">
              透过智能作战地图，可精准掌握企业各地市客户的开发占有情况、各营销部门的客户分布情况、各类型客户的合作情况，各客户的画像情况。
              <br />
            </p>
          </div>
        </div>
      </div>
      <div class="block_5 flex-col"><div class="group_3 flex-col"></div></div>
      <img
        class="image_3"
        referrerpolicy="no-referrer"
        src="/pages/assets/img/SketchPng8ba2ae971fa70400bddee5230f5889858b50f9003e0ce54089843db7565907fd.png"
        alt="AI智能营销作战中心功能展示图 - 动态作战沙盘界面"
        loading="lazy"
      />
    </div>
    <section id="capabilities" class="box_5 flex-col">
      <div class="group_14 flex-row">
        <div class="text-group_10 flex-col justify-between">
          <h2 class="text_22">核心功能</h2>
          <h3 class="text_23">AI智能营销作战中心</h3>
        </div>
      </div>
      <div class="text-wrapper_16 flex-row" aria-label="功能编号">
        <span class="text_24">01</span>
        <span class="text_25">02</span>
        <span class="text_26">03</span>
        <span class="text_27">04</span>
      </div>
      <div class="text-wrapper_17 flex-row justify-between" aria-label="功能编号">
        <span class="text_28">05</span>
        <span class="text_29">06</span>
        <span class="text_30">07</span>
        <span class="text_31">08</span>
      </div>
      <article class="block_7 flex-col">
        <h4 class="text_32">140万+终端客户</h4>
        <img
          class="image_4"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng8aa2a124f98f3500fba3d68ae817fea588b5b5f89ba505e9a79a0209b314515d.png"
          alt="140万+终端客户功能图标"
          loading="lazy"
        />
        <p class="text_33">
          慧医云拥有全国140万+终端客户基础信息，包括客户的精准定位、客户类型、客户首营资质、客户购买产品品类及数量、客户下单习惯等，智能营销作战地图集成该数据。
        </p>
      </article>
      <article class="block_8 flex-col">
        <h4 class="text_34">拜访管理</h4>
        <img
          class="image_5"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng8aa2a124f98f3500fba3d68ae817fea588b5b5f89ba505e9a79a0209b314515d.png"
          alt="拜访管理功能图标"
          loading="lazy"
        />
        <p class="text_35">
          通过AI进行拜访任务下发、路径规划、拜访信息反馈，利用AI语音识别能力，解决传统终端业务员文化水平不一致导致拜访管理无法推进问题，提高拜访效率掌握客户动态。
        </p>
      </article>
      <article class="block_9 flex-col">
        <h4 class="text_36">终端客户散点分布</h4>
        <img
          class="image_6"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng8aa2a124f98f3500fba3d68ae817fea588b5b5f89ba505e9a79a0209b314515d.png"
          alt="终端客户散点分布功能图标"
          loading="lazy"
        />
        <p class="text_37">
          通过乡镇及街道客户地址精准定位，快速获取客户是否已合作，同时在地图上标记客户位置，可仅查看合作客户、未合作客户。对于未合作客户按其画像定义优质等级，精准定位立刻掌握。
        </p>
      </article>
      <article class="block_10 flex-col">
        <h4 class="text_38">信息收集</h4>
        <img
          class="image_7"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng8aa2a124f98f3500fba3d68ae817fea588b5b5f89ba505e9a79a0209b314515d.png"
          alt="信息收集功能图标"
          loading="lazy"
        />
        <p class="text_39">
          企业制定客户信息收集要求，拜访客户后通过AI对话方式收集并整理客户信息，如客户经营面积、月营业额、店员数量、常售品种、客户喜好等，帮助企业更精准了解客户，做好画像分析。
        </p>
      </article>
      <article class="block_11 flex-col">
        <h4 class="text_40">区域热力分布</h4>
        <img
          class="image_8"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng8aa2a124f98f3500fba3d68ae817fea588b5b5f89ba505e9a79a0209b314515d.png"
          alt="区域热力分布功能图标"
          loading="lazy"
        />
        <p class="text_41">
          智能营销作战地图按国家、省、市、区进行多级区域热力展示，透过热力图快速掌握各区域市场占有情况，并按营销部门、客户类型及区域对市场开发占有进行排名展示。
        </p>
      </article>
      <article class="block_12 flex-col">
        <h4 class="text_42">拉单管理</h4>
        <img
          class="image_9"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng8aa2a124f98f3500fba3d68ae817fea588b5b5f89ba505e9a79a0209b314515d.png"
          alt="拉单管理功能图标"
          loading="lazy"
        />
        <p class="text_43">
          通过AI进行拉单管理，及时反馈并收集客户未成单原因，帮助进行客户的画像分析，制定下一步的跟单要求。
        </p>
      </article>
      <article class="block_13 flex-col">
        <h4 class="text_44">客户画像</h4>
        <img
          class="image_10"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng8aa2a124f98f3500fba3d68ae817fea588b5b5f89ba505e9a79a0209b314515d.png"
          alt="客户画像功能图标"
          loading="lazy"
        />
        <p class="text_45">
          基于客户标签方式，可查看客户的基础信息及销售相关信息，包括：客户名称、类型、地址、合作与否、联系人、联系电话、营业额、产品购买力、下单周期等
        </p>
      </article>
      <article class="block_14 flex-col">
        <h4 class="text_46">数据分级授权</h4>
        <img
          class="image_11"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng8aa2a124f98f3500fba3d68ae817fea588b5b5f89ba505e9a79a0209b314515d.png"
          alt="数据分级授权功能图标"
          loading="lazy"
        />
        <p class="text_47">分部门按组织管理层级进行数据授权，对应层级查看对应数据结果，防止数据泄露。</p>
      </article>
    </section>
    <section id="applications" class="box_6 flex-col">
      <div class="text-group_11 flex-col justify-between">
        <h2 class="text_48">产品价值及应用场景</h2>
        <h3 class="text_49">AI智能营销作战中心</h3>
      </div>
      <div class="grid_2 flex-row">
        <article class="group_4 flex-col">
          <div class="text-wrapper_11 flex-col"><h4 class="text_50">市场洞察</h4></div>
          <div class="section_3 flex-row justify-between">
            <p class="text_51">
              快速获悉各客户类型市场覆盖情况，了解各省、各市、各区县客户已开发情况及剩余可开发空间，实时跟进客户开发进展，精准找到开发覆盖不足区域，制定开发目标及针对性营销策略。
            </p>
            <img
              class="image_12"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngc16af0032d0fc4ef7093116f609a168d8d9d4cfacd23939cbff1da99842022e7.png"
              alt="市场洞察功能展示图"
              loading="lazy"
            />
          </div>
        </article>
        <article class="group_5 flex-col">
          <div class="text-wrapper_12 flex-col"><h4 class="text_52">客户分析</h4></div>
          <div class="group_15 flex-row justify-between">
            <p class="text_53">
              通过客户画像掌握客户基本信息及下单行为偏好，了解客户常购品种及购买能力，精准分析客户，找到客户突破口，采取差异化开发策略。
            </p>
            <img
              class="image_13"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngfddaab53cd32752fad72b851e35d25bee7bf38355a883256afee92a5d56f2d32.png"
              alt="客户分析功能展示图"
              loading="lazy"
            />
          </div>
        </article>
        <article class="group_7 flex-col">
          <div class="text-wrapper_13 flex-col"><h4 class="text_54">精准目标客户规划</h4></div>
          <div class="group_16 flex-row justify-between">
            <p class="text_55">
              精准标记客户地理位置，快速找到已合作及未合作客户，分析覆盖区域和可开发空间。对未合作客户进行分类分级，省区对业务员进行指挥作战，找到精准目标客户，规划客户开发路径。
            </p>
            <div class="group_8 flex-col"></div>
          </div>
        </article>
        <article class="group_9 flex-col">
          <div class="text-wrapper_14 flex-col"><h4 class="text_56">指挥作战</h4></div>
          <div class="box_14 flex-row justify-between">
            <p class="text_57">
              根据目标客户，制定相应的拜访任务或拉单任务，要求业务员收集并反馈客户的拜访过程及结果，对客户的喜好、意见等进行意见收集，让省区管理居前线，听见炮火声音，精准指挥。
            </p>
            <div class="box_8 flex-col"></div>
          </div>
        </article>
      </div>
    </section>
    <section class="box_11 flex-col" aria-label="产品特色功能展示">
      <div class="list_2 flex-row">
        <article
          class="list-items_1 flex-col"
          :style="{ background: item.lanhuBg0 }"
          v-for="(item, index) in loopData0"
          :key="index"
        >
          <div class="image-wrapper_1 flex-col">
            <img
              class="label_1"
              referrerpolicy="no-referrer"
              :src="item.lanhuimage0"
              :alt="`${item.lanhutext0}功能图标`"
              loading="lazy"
            />
          </div>
          <h5 class="text_60" :style="{ color: item.lanhufontColor0 }" v-html="item.lanhutext0"></h5>
          <div class="text-wrapper_15 flex-col" :style="{ background: item.lanhuBg2 }">
            <span class="text_61" :style="{ color: item.lanhufontColor1 }" v-html="item.lanhutext1"></span>
          </div>
          <p class="text_62" :style="{ color: item.lanhufontColor2 }" v-html="item.lanhutext2"></p>
          <div class="group_13 flex-row" :style="{ background: item.lanhuBg3 }">
            <div class="image-text_10 flex-row justify-between">
              <img
                class="image_15"
                referrerpolicy="no-referrer"
                :src="item.lanhuimage1"
                :alt="`${item.lanhutext0}应用场景图标`"
                loading="lazy"
              />
              <div class="text-group_8" :style="{ color: item.lanhufontColor3 }" v-html="item.lanhutext3"></div>
            </div>
          </div>
        </article>
      </div>
    </section>
  </div>
</template>
<script setup>
// SEO元数据设置
useHead({
  title: 'AI智能营销作战中心 - 慧医云科技 | AI驱动的智能营销解决方案',
  meta: [
    { name: 'description', content: 'AI智能营销作战中心是慧医云科技打造的AI驱动动态作战沙盘，整合140万+终端客户数据，提供市场洞察、客户分析、精准目标规划、指挥作战等核心功能，助力企业营销管理及市场开发，实现精准营销和智能决策。' },
    { name: 'keywords', content: 'AI智能营销,营销作战中心,动态作战沙盘,市场洞察,客户分析,客户画像,拜访管理,营销管理,市场开发,慧医云科技,智能营销解决方案,AI营销' },
    { name: 'author', content: '四川慧医云科技有限公司' },
    { name: 'robots', content: 'index,follow' },
    { name: 'googlebot', content: 'index,follow' },
    { name: 'baiduspider', content: 'index,follow' },

    // Open Graph标签
    { property: 'og:title', content: 'AI智能营销作战中心 - 慧医云科技 | AI驱动的智能营销解决方案' },
    { property: 'og:description', content: 'AI智能营销作战中心是AI驱动的动态作战沙盘，整合140万+终端客户数据，提供市场洞察、客户分析、精准目标规划等核心功能，助力企业实现精准营销和智能决策。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://www.huiyiyunai.com/products/marketing' },
    { property: 'og:image', content: '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png' },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:alt', content: 'AI智能营销作战中心产品标志 - 慧医云科技智能营销解决方案' },
    { property: 'og:site_name', content: '慧医云科技' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'AI智能营销作战中心 - 慧医云科技' },
    { name: 'twitter:description', content: 'AI驱动的动态作战沙盘，整合140万+终端客户数据，助力企业实现精准营销' },
    { name: 'twitter:image', content: '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png' },
    { name: 'twitter:image:alt', content: 'AI智能营销作战中心产品标志' },

    // 移动端优化
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
    { name: 'format-detection', content: 'telephone=no' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
    { name: 'theme-color', content: '#1976d2' },

    // 多语言和地区
    { name: 'language', content: 'zh-CN' },
    { name: 'geo.region', content: 'CN-SC' },
    { name: 'geo.placename', content: '成都' },

    // 其他SEO标签
    { name: 'category', content: '营销AI' },
    { name: 'coverage', content: 'Worldwide' },
    { name: 'distribution', content: 'Global' },
    { name: 'rating', content: 'General' }
  ],
  link: [
    { rel: 'canonical', href: 'https://www.huiyiyunai.com/products/marketing' },
    { rel: 'alternate', href: 'https://www.huiyiyunai.com/products/marketing', hreflang: 'zh-CN' },
    { rel: 'preload', href: '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png', as: 'image' }
  ]
})

// 结构化数据
const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'Product',
  name: 'AI智能营销作战中心',
  description: 'AI驱动的动态作战沙盘，整合市场数据、客户需求、销售动线为智能地图',
  brand: {
    '@type': 'Organization',
    name: '四川慧医云科技有限公司',
    url: 'https://www.huiyiyunai.com'
  },
  manufacturer: {
    '@type': 'Organization',
    name: '四川慧医云科技有限公司',
    url: 'https://www.huiyiyunai.com',
    address: {
      '@type': 'PostalAddress',
      streetAddress: '高攀路26号-1',
      addressLocality: '成都市',
      addressRegion: '四川省',
      addressCountry: 'CN'
    }
  },
  category: '营销AI软件',
  image: [
    '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png',
    '/pages/assets/img/SketchPng8ba2ae971fa70400bddee5230f5889858b50f9003e0ce54089843db7565907fd.png'
  ],
  offers: {
    '@type': 'Offer',
    availability: 'https://schema.org/InStock',
    priceValidUntil: '2025-12-31',
    url: 'https://www.huiyiyunai.com/products/marketing'
  },
  aggregateRating: {
    '@type': 'AggregateRating',
    ratingValue: '4.8',
    bestRating: '5',
    worstRating: '1',
    ratingCount: '95'
  },
  features: [
    '140万+终端客户数据库',
    'AI驱动的动态作战沙盘',
    '拜访管理和路径规划',
    '客户画像精准分析',
    '区域热力分布展示',
    '智能指挥作战系统',
    '数据分级授权管理',
    '市场洞察和客户分析'
  ],
  applicationCategory: '营销管理',
  operatingSystem: '跨平台',
  additionalProperty: [
    {
      '@type': 'PropertyValue',
      name: '终端客户数据库',
      value: '140万+'
    },
    {
      '@type': 'PropertyValue',
      name: '覆盖范围',
      value: '全国'
    },
    {
      '@type': 'PropertyValue',
      name: '客户类型',
      value: '药店、诊所、连锁、医院'
    },
    {
      '@type': 'PropertyValue',
      name: '核心功能',
      value: '8大核心功能模块'
    },
    {
      '@type': 'PropertyValue',
      name: '应用场景',
      value: '市场洞察、客户分析、目标规划、指挥作战'
    }
  ],
  serviceType: [
    '市场洞察',
    '客户分析',
    '精准目标客户规划',
    '指挥作战'
  ]
}

useHead({
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify(structuredData)
    }
  ]
})

// 页面元数据
definePageMeta({
  title: 'AI智能营销作战中心',
  description: 'AI驱动的智能营销解决方案',
  layout: 'default'
})

// 响应式数据
const activeSection = ref('')
const loopData0 = ref([
  {
    lanhuBg0: 'url(https://lanhu-oss-2537-2.lanhuapp.com/SketchPng0879e35a540441abaa4898fbdb4c9f25c991c306e40a9b8f714d3f837f7d68e8) ',
    lanhuimage0: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPng6a74782cd86439b274320ce896bb479b5ec17d127af9005e83184553ee928c09',
    lanhutext0: '战略雷达',
    lanhufontColor0: 'rgba(255,255,255,1)',
    lanhuBg2: 'rgba(255,255,255,0.900000)',
    lanhutext1: '决策支持',
    lanhufontColor1: 'rgba(33,135,250,1)',
    lanhutext2: '像军事卫星扫描战场，让管理者在决策时不再盲从或凭经验、感觉，一目了然发现市场空间及机会。',
    lanhufontColor2: 'rgba(255,255,255,1)',
    lanhuBg3: 'rgba(255,255,255,0.150000)',
    lanhuimage1: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPng62be80367b3e2444b1e2d59e0412dee91e1edd917ff8a5aba3353d7355928f09',
    lanhutext3: '知道客户在哪？<br/>知道战场在哪？<br/>知道业务员应该在哪？',
    lanhufontColor3: 'rgba(255,255,255,1)'
  },
  {
    lanhuBg0: 'url(https://lanhu-oss-2537-2.lanhuapp.com/SketchPngd1772a10e20fb6d47c29dfef2a12b8997e000c0996e24f7f72fc9249efdc68c5) ',
    lanhuimage0: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPng90969100711cbfa20b4bd8ebd1571a53791ae8eba0ff3d2ed8f4dcc6b8b49ce8',
    lanhutext0: '智能弹药补充',
    lanhufontColor0: 'rgba(33,135,250,1)',
    lanhuBg2: 'rgba(33,135,250,0.900000)',
    lanhutext1: '资源精准投放',
    lanhufontColor1: 'rgba(255,255,255,1)',
    lanhutext2: '通过客户画像精准分析客户，知道哪些是优质应合作客户，给予精准的资源投放。',
    lanhufontColor2: 'rgba(0,0,0,1)',
    lanhuBg3: 'rgba(33,135,250,0.050000)',
    lanhuimage1: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPngdc9756a7e97e48ed5dc8a849f80f5ea0b41739f94b7d5b27eb7369704b84f485',
    lanhutext3: '知道谁是我们的目标客户？<br/>知道资源应该给谁？<br/>知道应该派谁去？',
    lanhufontColor3: 'rgba(33,135,250,1)'
  },
  {
    lanhuBg0: 'url(https://lanhu-oss-2537-2.lanhuapp.com/SketchPngd1772a10e20fb6d47c29dfef2a12b8997e000c0996e24f7f72fc9249efdc68c5) ',
    lanhuimage0: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPng476d6f062741f1f26ae6ebce9a68624f9d60566c78178b9fe704061904d7fd75',
    lanhutext0: '实时指挥作战',
    lanhufontColor0: 'rgba(33,135,250,1)',
    lanhuBg2: 'rgba(33,135,250,0.900000)',
    lanhutext1: '过程管控',
    lanhufontColor1: 'rgba(255,255,255,1)',
    lanhutext2: '正确的决策并不一定带来好的战果，指挥中心需要在听得到炮火声音的地方。',
    lanhufontColor2: 'rgba(0,0,0,1)',
    lanhuBg3: 'rgba(33,135,250,0.050000)',
    lanhuimage1: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPngdc9756a7e97e48ed5dc8a849f80f5ea0b41739f94b7d5b27eb7369704b84f485',
    lanhutext3: '知道怎么打？用什么方式打？<br/>知道阵地拿下没？<br/>知道拿不下是什么原因？',
    lanhufontColor3: 'rgba(33,135,250,1)'
  }
])

// 生命周期钩子
onMounted(() => {
  // 监听滚动事件来更新激活的导航项
  window.addEventListener('scroll', handleScroll)
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll)
})

// 方法
const handleScroll = () => {
  const sections = [
    { id: 'introduction', nav: 'introduction' },
    { id: 'capabilities', nav: 'advantages' }, // capabilities对应advantages导航
    { id: 'applications', nav: 'applications' }
  ]
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop

  for (let i = sections.length - 1; i >= 0; i--) {
    const section = document.getElementById(sections[i].id)
    if (section && scrollTop >= section.offsetTop - 100) {
      activeSection.value = sections[i].nav
      break
    }
  }
}

const scrollToSection = (sectionId) => {
  // 设置当前活动的导航项
  activeSection.value = sectionId

  // 将advantages映射到capabilities，因为实际内容是核心功能
  let targetId = sectionId
  if (sectionId === 'advantages') {
    targetId = 'capabilities'
  }

  const element = document.getElementById(targetId)
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}
</script>
<style scoped lang="less" src="./assets/marketing.rem.less" />
