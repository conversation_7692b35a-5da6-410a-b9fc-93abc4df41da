<template>
  <div class="page flex-col">
    <div class="group_22 flex-col">
      <div class="box_6 flex-col">
        <img
          class="image_2"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png"
          alt="AI患助产品标志 - 慧医云科技智能患者助手"
          width="200"
          height="150"
          loading="lazy"
        />
        <div class="text-wrapper_1">
          <span class="text_7">ai患助</span>
          <span class="paragraph_1"><br /></span>
          <span class="paragraph_2">
            以AI技术重构全流程就医体验
            <br />
            让患者回归健康管理核心
          </span>
        </div>
      </div>
      <div class="box_7 flex-col">
        <nav class="group_1 flex-row" role="navigation" aria-label="页面导航">
          <div
            class="text-wrapper_2 flex-col"
            :class="{ active: activeTab === 'about' }"
            @click="scrollToSection('about')"
            role="button"
            tabindex="0"
            @keydown.enter="scrollToSection('about')"
            @keydown.space="scrollToSection('about')"
          >
            <span class="text_8">关于ai患助</span>
          </div>
          <div
            class="text-wrapper_3 flex-col"
            :class="{ active: activeTab === 'features' }"
            @click="scrollToSection('features')"
            role="button"
            tabindex="0"
            @keydown.enter="scrollToSection('features')"
            @keydown.space="scrollToSection('features')"
          >
            <span class="text_9">产品功能</span>
          </div>
          <div
            class="text-wrapper_4 flex-col"
            :class="{ active: activeTab === 'highlights' }"
            @click="scrollToSection('highlights')"
            role="button"
            tabindex="0"
            @keydown.enter="scrollToSection('highlights')"
            @keydown.space="scrollToSection('highlights')"
          >
            <span class="text_10">产品亮点</span>
          </div>
          <div
            class="text-wrapper_5 flex-col"
            :class="{ active: activeTab === 'applications' }"
            @click="scrollToSection('applications')"
            role="button"
            tabindex="0"
            @keydown.enter="scrollToSection('applications')"
            @keydown.space="scrollToSection('applications')"
          >
            <span class="text_11">应用场景</span>
          </div>
        </nav>
        <section id="about-section" class="text-group_21 flex-col justify-between">
          <h2 class="text_12">关于AI患助</h2>
          <p class="text_13">
            以AI技术重构全流程就医体验，让患者回归健康管理核心；贯穿诊前、诊中、诊后全周期，通过三大核心模块化解医疗痛点
          </p>
        </section>
        <div class="group_23 flex-row justify-between">
          <div class="group_3 flex-col">
            <h3 class="text_14">诊前</h3>
            <div class="group_4 flex-col"></div>
            <div class="text-wrapper_6">
              <span class="text_15">
                智能导诊运用NLP技术分析症状描述，结合医生专长与号源动态，实现95%科室匹配准确率，
              </span>
              <span class="text_16">破解"挂错号"难题</span>
            </div>
          </div>
          <div class="group_5 flex-col">
            <h3 class="text_17">诊中</h3>
            <div class="group_6 flex-col"></div>
            <div class="text-wrapper_7">
              <span class="text_18">智能预诊通过多轮对话生成结构化病历，提前捕捉80%主诉信息，使医生</span>
              <span class="text_19">问诊效率提升60%</span>
            </div>
          </div>
          <div class="group_7 flex-col">
            <h3 class="text_20">诊后</h3>
            <div class="group_8 flex-col"></div>
            <div class="text-wrapper_8">
              <span class="text_21">
                智能随访搭建院外康复桥梁，AI自动生成个性化用药提醒与复诊计划，结合异常指标预警机制，使
              </span>
              <span class="text_22">慢病达标率提升40%</span>
            </div>
          </div>
        </div>
        <div class="text-wrapper_9 flex-col">
          <p class="paragraph_3">
            AI代劳80%常规随访，释放65%人力成本
            <br />
            构建起医患协同的智能医疗生态，真正实现"精准就医少折腾，全程管理更安心"的价值主张
            <br />
            <br />
          </p>
        </div>
      </div>
      <section id="features-section" class="box_8 flex-col">
        <h2 class="text_23">产品功能</h2>
        <nav class="text-wrapper_26 flex-row justify-between" role="tablist" aria-label="产品功能导航">
          <button
            class="paragraph_4"
            :class="{ active: activeFeature === 'smart-triage' }"
            @click="setActiveFeature('smart-triage')"
            role="tab"
            :aria-selected="activeFeature === 'smart-triage'"
            aria-controls="smart-triage-panel"
          >
            智能分诊
            <br />
            精准匹配科室​
          </button>
          <button
            class="paragraph_5"
            :class="{ active: activeFeature === 'ai-self-check' }"
            @click="setActiveFeature('ai-self-check')"
            role="tab"
            :aria-selected="activeFeature === 'ai-self-check'"
            aria-controls="ai-self-check-panel"
          >
            AI辅助自查
            <br />
            病情预判​​
          </button>
          <button
            class="paragraph_6"
            :class="{ active: activeFeature === 'precise-guide' }"
            @click="setActiveFeature('precise-guide')"
            role="tab"
            :aria-selected="activeFeature === 'precise-guide'"
            aria-controls="precise-guide-panel"
          >
            精准导医
            <br />
            医生智能匹配​
          </button>
          <button
            class="paragraph_7"
            :class="{ active: activeFeature === 'smart-pre-diagnosis' }"
            @click="setActiveFeature('smart-pre-diagnosis')"
            role="tab"
            :aria-selected="activeFeature === 'smart-pre-diagnosis'"
            aria-controls="smart-pre-diagnosis-panel"
          >
            智能预诊
            <br />
            结构化病历生成
          </button>
          <button
            class="paragraph_8"
            :class="{ active: activeFeature === 'full-follow-up' }"
            @click="setActiveFeature('full-follow-up')"
            role="tab"
            :aria-selected="activeFeature === 'full-follow-up'"
            aria-controls="full-follow-up-panel"
          >
            全病程随访
            <br />
            院外健康管家​
          </button>
        </nav>
        <div class="section_1 flex-col"></div>

        <!-- 智能分诊模块 -->
        <div v-if="activeFeature === 'smart-triage'" class="box_21 flex-row justify-between smart-triage" id="smart-triage-panel" role="tabpanel" aria-labelledby="smart-triage-tab">
          <div class="box_9 flex-col">
            <div class="text-wrapper_11">
              <span class="text_24">智能分诊</span>
              <span class="text_25">·精准匹配科室​</span>
            </div>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
              alt="智能分诊功能展示图 - AI患助精准匹配科室功能"
              loading="lazy"
            />
            <p class="paragraph_9">基于医疗级语音识别与深度学习技术，实时捕捉医患对话内容：<br><br>
·自动提取主诉、病史、诊断等关键信息；<br>
·12秒内生成符合临床规范的结构化电子病历；<br>
·并一键同步至医院电子病历系统；<br><br></p>
          </div>
          <div class="box_10 flex-col"></div>
        </div>

        <!-- AI辅助自查模块 -->
        <div v-if="activeFeature === 'ai-self-check'" class="box_21 flex-row justify-between ai-self-check" id="ai-self-check-panel" role="tabpanel" aria-labelledby="ai-self-check-tab">
          <div class="box_9 flex-col">
            <div class="text-wrapper_11">
              <span class="text_24">AI辅助自查</span>
              <span class="text_25">·病情预判​​</span>
            </div>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
              alt="AI辅助自查功能展示图 - AI患助病情预判功能"
              loading="lazy"
            />
            <p class="paragraph_9">智能解析患者输入的图文症状，结合权威医学知识库生成疑似疾病清单，<br>
提供病情分级预警与紧急就医建议，同步推荐适配科室，<br>
帮助60%表达不清的患者提前梳理病史，<br>
缩短门诊4.6分钟问诊中的基础信息采集时间。</p>
          </div>
          <div class="box_10 flex-col"></div>
        </div>

        <!-- 精准导医模块 -->
        <div v-if="activeFeature === 'precise-guide'" class="box_21 flex-row justify-between precise-guide" id="precise-guide-panel" role="tabpanel" aria-labelledby="precise-guide-tab">
          <div class="box_9 flex-col">
            <div class="text-wrapper_11">
              <span class="text_24">精准导医</span>
              <span class="text_25">·医生智能匹配​</span>
            </div>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
              alt="精准导医功能展示图 - AI患助医生智能匹配功能"
              loading="lazy"
            />
            <p class="paragraph_9">根据病情分析结果，对接5000+医生专长数据库（涵盖手术成功率、病种研究深度等20项维度），<br>
为患者推荐最契合的专家资源，<br>
减少38%因专业错配导致的重复就诊，<br>
专家号源浪费率从23%降至5%以下。</p>
          </div>
          <div class="box_10 flex-col"></div>
        </div>

        <!-- 智能预诊模块 -->
        <div v-if="activeFeature === 'smart-pre-diagnosis'" class="box_21 flex-row justify-between smart-pre-diagnosis" id="smart-pre-diagnosis-panel" role="tabpanel" aria-labelledby="smart-pre-diagnosis-tab">
          <div class="box_9 flex-col">
            <div class="text-wrapper_11">
              <span class="text_24">智能预诊</span>
              <span class="text_25">·结构化病历生成</span>
            </div>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPng0a1dcfae6159c1c2dcd8a85670b9d50bd1b0fc079fb7a7c2a33bfd33b3a8cdf9.png"
              alt="智能预诊功能展示图 - AI患助结构化病历生成功能"
              loading="lazy"
            />
            <p class="paragraph_9">通过语音/文字多轮对话主动引导患者陈述病史，自动提取过敏史、用药记录等15类核心数据，生成含疼痛分级量表、症状演变时间轴的结构化预诊报告，<br>
使医生提前掌握80%主诉信息，<br>
门诊沟通效率提升60%。</p>
          </div>
          <div class="box_10 flex-col"></div>
        </div>

        <!-- 全病程随访模块 -->
        <div v-if="activeFeature === 'full-follow-up'" class="box_21 flex-row justify-between full-follow-up" id="full-follow-up-panel" role="tabpanel" aria-labelledby="full-follow-up-tab">
          <div class="box_9 flex-col">
            <div class="text-wrapper_11">
              <span class="text_24">全病程随访</span>
              <span class="text_25">·院外健康管家​</span>
            </div>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
              alt="全病程随访功能展示图 - AI患助院外健康管家功能"
              loading="lazy"
            />
            <p class="paragraph_9">为术后及慢病患者定制用药提醒、复诊预警计划，<br>
AI自动追踪异常数据实时触发预警并推送应急指导，<br>
使53%的失访患者回归系统管理，用药依从性提升40%，<br>
医院随访人力成本节省65%。</p>
          </div>
          <div class="box_10 flex-col"></div>
        </div>
      </section>
      <section id="applications-section" class="box_11 flex-col">
        <div class="text-wrapper_27 flex-row"><h2 class="text_26">应用场景</h2></div>
        <div class="section_9 flex-row justify-between">
          <div class="group_10 flex-col">
            <div class="text-group_22 flex-col justify-between">
              <h3 class="text_27">ai患助</h3>
              <p class="paragraph_10">
                让患者更专注健康
                <br />
                而不是就医流程
              </p>
            </div>
            <div class="box_12 flex-row">
              <div class="box_13 flex-col"></div>
              <div class="text-wrapper_13">
                <span class="text_28">诊前</span>
                <span class="paragraph_11">
                  <br />
                  智能导诊
                </span>
              </div>
            </div>
            <div class="box_14 flex-col">
              <div class="text-wrapper_14">
                <span class="text_29">诊中</span>
                <span class="paragraph_12">
                  <br />
                  智能预诊
                </span>
              </div>
            </div>
            <div class="box_15 flex-col">
              <div class="text-wrapper_15">
                <span class="text_30">诊后</span>
                <span class="paragraph_13">
                  <br />
                  智能随访
                </span>
              </div>
            </div>
          </div>
          <div class="group_11 flex-col"></div>
        </div>
        <div class="image-wrapper_1 flex-col">
          <img
            class="image_4"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng0a1dcfae6159c1c2dcd8a85670b9d50bd1b0fc079fb7a7c2a33bfd33b3a8cdf9.png"
            alt="AI患助应用场景展示图 - 诊前诊中诊后全流程服务"
            loading="lazy"
          />
        </div>
      </section>
      <section id="highlights-section" class="box_16 flex-col">
        <h2 class="text_31">产品亮点</h2>
        <div class="grid_2 flex-row">
          <article class="group_12 flex-col">
            <div class="section_3 flex-row">
              <h3 class="text_32">01&nbsp;、精准导诊</h3>
              <div class="image-text_12 flex-row justify-between">
                <img
                  class="label_1"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPng6cac227565145373364562d12698d0a7863740f26bcb81922ce62c113cd1a49e.png"
                  alt="精准导诊特点标签图标"
                  loading="lazy"
                />
                <span class="text-group_3">告别"挂错科"焦虑</span>
              </div>
            </div>
            <div class="block_3 flex-row justify-between">
              <div class="image-text_13 flex-row justify-between">
                <img
                  class="image_5"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPng206ac683f184d1a9e6a5247bf427f85693012c6c465a3a8c269a11a3b204dd6f.png"
                  alt="精准导诊功能图标"
                  loading="lazy"
                />
                <div class="text-group_4">
                  智能分析症状描述，匹配最佳诊疗路径
                  <br />
                  结合医生专长与号源，推荐最优科室/医生
                  <br />
                  减少患者盲目选择，提升效率与精准度
                </div>
              </div>
              <img
                class="image_6"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng2c11508ba82fadb3bc126272ddc213f6d53055e0554a65ca31ccbfd7d052161a.png"
                alt="精准导诊应用示例图"
                loading="lazy"
              />
            </div>
          </article>
          <article class="group_13 flex-col">
            <div class="group_14 flex-row">
              <h3 class="text_33">02&nbsp;、高效预诊</h3>
              <div class="image-text_14 flex-row justify-between">
                <img
                  class="label_2"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPng6cac227565145373364562d12698d0a7863740f26bcb81922ce62c113cd1a49e.png"
                  alt="高效预诊特点标签图标"
                  loading="lazy"
                />
                <span class="text-group_5">解决反复陈述、信息遗漏</span>
              </div>
            </div>
            <div class="box_22 flex-row justify-between">
              <div class="image-text_15 flex-row justify-between">
                <img
                  class="image_7"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPng206ac683f184d1a9e6a5247bf427f85693012c6c465a3a8c269a11a3b204dd6f.png"
                  alt="高效预诊功能图标"
                  loading="lazy"
                />
                <div class="text-group_6">
                  多轮智能对话主动引导患者描述症状细节
                  <br />
                  自动生成结构化预问诊报告，同步至医生端系统
                  <br />
                  避免口头重复，确保关键主诉信息完整传递
                </div>
              </div>
              <img
                class="image_8"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng79ff76d1a5cf752282a200ecfa55cdccebaa4730cb139ac85cec16c1bec226ca.png"
                alt="高效预诊应用示例图"
                loading="lazy"
              />
            </div>
          </article>
          <article class="group_16 flex-col">
            <div class="text-wrapper_16 flex-col"><h3 class="text_34">03、提升医患沟通质量</h3></div>
            <div class="group_24 flex-row justify-between">
              <div class="image-text_16 flex-row justify-between">
                <img
                  class="image_9"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPng206ac683f184d1a9e6a5247bf427f85693012c6c465a3a8c269a11a3b204dd6f.png"
                  alt="医患沟通质量提升功能图标"
                  loading="lazy"
                />
                <div class="text-group_7">
                  预诊报告自动提取核心指标&nbsp;(如疼痛程度、持续时间）
                  <br />
                  提前掌握患者80%主诉信息，聚焦深度问诊
                  <br />
                  减少"边问边记"干扰，更聚焦患者实际需求
                </div>
              </div>
              <div class="group_17 flex-col"></div>
            </div>
          </article>
          <article class="group_18 flex-col">
            <div class="section_5 flex-row">
              <h3 class="text_35">04、全周期康复管理</h3>
              <div class="image-text_17 flex-row justify-between">
                <img
                  class="label_3"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPng6cac227565145373364562d12698d0a7863740f26bcb81922ce62c113cd1a49e.png"
                  alt="全周期康复管理特点标签图标"
                  loading="lazy"
                />
                <span class="text-group_8">告别"出院即失联"​</span>
              </div>
            </div>
            <div class="box_23 flex-row justify-between">
              <div class="text-wrapper_17 flex-col"><h4 class="text_36">患者获益​</h4></div>
              <div class="text-wrapper_18 flex-col"><h4 class="text_37">医院增效</h4></div>
            </div>
            <div class="box_24 flex-row justify-between">
              <div class="image-text_18 flex-row justify-between">
                <img
                  class="image_10"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPng206ac683f184d1a9e6a5247bf427f85693012c6c465a3a8c269a11a3b204dd6f.png"
                  alt="全周期康复管理功能图标"
                  loading="lazy"
                />
                <div class="text-group_9">
                  AI自动生成个性化随访计划（用药提醒、复诊节点、康复训练）
                  <br />
                  智能预警异常指标（如血压超标、伤口感染症状），主动推送应急指导
                  <br />
                  用药依从性提升：语音/图文多模态提醒，支持用药记录一键反馈
                </div>
              </div>
              <div class="box_18 flex-col"></div>
            </div>
          </article>
        </div>
      </section>
      <section class="box_19 flex-row" aria-label="产品效果数据统计">
        <div class="text-group_23 flex-col justify-between">
          <div class="text-wrapper_19">
            <span class="text_38">90</span>
            <span class="text_39">%</span>
          </div>
          <p class="paragraph_14">
            节省患者预诊
            <br />
            信息录入时间
          </p>
        </div>
        <div class="text-group_24 flex-col justify-between">
          <div class="text-wrapper_20">
            <span class="text_40">95</span>
            <span class="text_41">%</span>
          </div>
          <p class="paragraph_15">
            科室匹配
            <br />
            准确率提升
          </p>
        </div>
        <div class="text-group_25 flex-col justify-between">
          <div class="text-wrapper_21">
            <span class="text_42">40</span>
            <span class="text_43">%</span>
          </div>
          <p class="paragraph_16">
            患者候诊
            <br />
            等待时间缩短
          </p>
        </div>
        <div class="text-group_26 flex-col justify-between">
          <div class="text-wrapper_22">
            <span class="text_44">60</span>
            <span class="text_45">%</span>
          </div>
          <p class="paragraph_17">
            主诉信息
            <br />
            完整度提升
          </p>
        </div>
        <div class="text-group_27 flex-col justify-between">
          <div class="text-wrapper_23">
            <span class="text_46">40</span>
            <span class="text_47">%</span>
          </div>
          <p class="paragraph_18">
            慢性病患者院外
            <br />
            指标达标率提高
          </p>
        </div>
        <div class="text-group_28 flex-col justify-between">
          <div class="text-wrapper_24">
            <span class="text_48">65</span>
            <span class="text_49">%</span>
          </div>
          <p class="paragraph_19">
            医院随访人力
            <br />
            成本节约65%
          </p>
        </div>
        <div class="text-group_29 flex-col justify-between">
          <div class="text-wrapper_25">
            <span class="text_50">90</span>
            <span class="text_51">%</span>
          </div>
          <p class="paragraph_20">
            复诊预约率
            <br />
            提升
          </p>
        </div>
      </section>
    </div>
  </div>
</template>
<script setup>
// SEO元数据设置
useHead({
  title: 'AI患助 - 慧医云科技 | 全流程就医体验重构专家',
  meta: [
    { name: 'description', content: 'AI患助是慧医云科技推出的全流程就医体验重构产品。以AI技术贯穿诊前、诊中、诊后全周期，通过智能导诊、智能预诊、智能随访三大核心模块，实现95%科室匹配准确率，问诊效率提升60%，慢病达标率提升40%。' },
    { name: 'keywords', content: 'AI患助,智能导诊,智能预诊,智能随访,全流程就医,患者体验,医疗AI,慧医云科技,就医效率,医患沟通,院外随访,康复管理' },
    { name: 'author', content: '四川慧医云科技有限公司' },
    { name: 'robots', content: 'index,follow' },
    { name: 'googlebot', content: 'index,follow' },
    { name: 'baiduspider', content: 'index,follow' },

    // Open Graph标签
    { property: 'og:title', content: 'AI患助 - 慧医云科技 | 全流程就医体验重构专家' },
    { property: 'og:description', content: 'AI患助以AI技术重构全流程就医体验，贯穿诊前、诊中、诊后全周期，实现95%科室匹配准确率，问诊效率提升60%，让患者回归健康管理核心。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://www.huiyiyunai.com/products/aihuanzhu' },
    { property: 'og:image', content: '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png' },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:alt', content: 'AI患助产品标志 - 慧医云科技智能患者助手' },
    { property: 'og:site_name', content: '慧医云科技' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'AI患助 - 慧医云科技 | 全流程就医体验重构专家' },
    { name: 'twitter:description', content: 'AI患助以AI技术重构全流程就医体验，实现95%科室匹配准确率，问诊效率提升60%' },
    { name: 'twitter:image', content: '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png' },
    { name: 'twitter:image:alt', content: 'AI患助产品标志 - 慧医云科技智能患者助手' },

    // 移动端优化
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
    { name: 'format-detection', content: 'telephone=no' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
    { name: 'theme-color', content: '#1976d2' },

    // 多语言和地区
    { name: 'language', content: 'zh-CN' },
    { name: 'geo.region', content: 'CN-SC' },
    { name: 'geo.placename', content: '成都' },

    // 其他SEO标签
    { name: 'category', content: '医疗AI' },
    { name: 'coverage', content: 'Worldwide' },
    { name: 'distribution', content: 'Global' },
    { name: 'rating', content: 'General' }
  ],
  link: [
    { rel: 'canonical', href: 'https://www.huiyiyunai.com/products/aihuanzhu' },
    { rel: 'alternate', href: 'https://www.huiyiyunai.com/products/aihuanzhu', hreflang: 'zh-CN' },
    { rel: 'preload', href: '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png', as: 'image' }
  ]
})

// 结构化数据
const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'Product',
  name: 'AI患助',
  description: '以AI技术重构全流程就医体验的智能医疗产品，贯穿诊前、诊中、诊后全周期',
  brand: {
    '@type': 'Organization',
    name: '四川慧医云科技有限公司',
    url: 'https://www.huiyiyunai.com'
  },
  manufacturer: {
    '@type': 'Organization',
    name: '四川慧医云科技有限公司',
    url: 'https://www.huiyiyunai.com',
    address: {
      '@type': 'PostalAddress',
      streetAddress: '高攀路26号-1',
      addressLocality: '成都市',
      addressRegion: '四川省',
      addressCountry: 'CN'
    }
  },
  category: '医疗AI软件',
  image: [
    '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png',
    '/pages/assets/img/SketchPng0a1dcfae6159c1c2dcd8a85670b9d50bd1b0fc079fb7a7c2a33bfd33b3a8cdf9.png'
  ],
  offers: {
    '@type': 'Offer',
    availability: 'https://schema.org/InStock',
    priceValidUntil: '2025-12-31',
    url: 'https://www.huiyiyunai.com/products/aihuanzhu'
  },
  aggregateRating: {
    '@type': 'AggregateRating',
    ratingValue: '4.9',
    bestRating: '5',
    worstRating: '1',
    ratingCount: '128'
  },
  features: [
    '智能导诊 - 95%科室匹配准确率',
    '智能预诊 - 问诊效率提升60%',
    '智能随访 - 慢病达标率提升40%',
    '全流程覆盖 - 诊前诊中诊后',
    'AI技术驱动 - NLP和深度学习'
  ],
  applicationCategory: '医疗健康',
  operatingSystem: '跨平台',
  additionalProperty: [
    {
      '@type': 'PropertyValue',
      name: '科室匹配准确率',
      value: '95%'
    },
    {
      '@type': 'PropertyValue',
      name: '问诊效率提升',
      value: '60%'
    },
    {
      '@type': 'PropertyValue',
      name: '慢病达标率提升',
      value: '40%'
    },
    {
      '@type': 'PropertyValue',
      name: '预诊信息录入时间节省',
      value: '90%'
    },
    {
      '@type': 'PropertyValue',
      name: '随访人力成本节约',
      value: '65%'
    }
  ]
}

useHead({
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify(structuredData)
    }
  ]
})

// 页面元数据
definePageMeta({
  title: 'AI患助',
  description: '全流程就医体验重构专家',
  layout: 'default'
})

// ... existing code ...
const activeTab = ref('')
const activeFeature = ref('smart-triage')

onMounted(() => {
  // 监听滚动事件，根据当前滚动位置更新活动tab
  window.addEventListener('scroll', handleScroll)
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll)
})

const scrollToSection = (section) => {
  activeTab.value = section
  const element = document.getElementById(section + '-section')
  if (element) {
    const offsetTop = element.offsetTop - 100 // 减去导航栏高度
    window.scrollTo({
      top: offsetTop,
      behavior: 'smooth'
    })
  }
}

const handleScroll = () => {
  const sections = ['about', 'features', 'applications', 'highlights']
  const scrollPos = window.scrollY + 200

  for (let i = sections.length - 1; i >= 0; i--) {
    const element = document.getElementById(sections[i] + '-section')
    if (element && element.offsetTop <= scrollPos) {
      activeTab.value = sections[i]
      break
    }
  }
}

const setActiveFeature = (feature) => {
  activeFeature.value = feature
}
</script>
<style scoped lang="less" src="./assets/aihuanzhu.rem.less" />
