// 重置语义化标签的默认样式
h1, h2, h3, h4, h5, h6, p {
  margin: 0;
  padding: 0;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  text-align: inherit;
  font-family: inherit;
}

button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  text-align: inherit;
  cursor: pointer;
}

strong {
  font-weight: inherit;
}

.page {
  background-color: rgba(243, 245, 249, 1);
  position: relative;
  width: 51.2rem;
  overflow: hidden;
  margin-top: 2.187rem; // 为固定头部留出空间
    .box_19 {
      width: 40.534rem;
      height: 0.64rem;
      margin: 0.774rem 0 0 5.334rem;
      .image_1 {
        width: 4.267rem;
        height: 0.64rem;
      }
      .text_1 {
        width: 0.907rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 15.867rem;
      }
      .text_2 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 2.08rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.08rem 0 0 0.107rem;
      }
      .text_3 {
        width: 3.174rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_4 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_5 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_6 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
    }
    .box_20 {
      width: 1.814rem;
      height: 0.08rem;
      margin: 0.24rem 0 0.454rem 28.427rem;
      .block_1 {
        background-color: rgba(33, 135, 250, 1);
        width: 1.814rem;
        height: 0.08rem;
      }
    }
  }
  .box_21 {
    position: relative;
    width: 51.2rem;
    height: 115.68rem;
    .group_3 {
      background-image: url(/pages/assets/img/b72e649a13fa48e5b6b486d92ec9feb2_mergeImage.png);
      background-repeat: no-repeat;
      background-size: cover;
      width: 51.2rem;
      height: 12.8rem;
      .image_2 {
        width: 51.2rem;
        height: 0.027rem;
      }
      .text-wrapper_1 {
        width: 20rem;
        height: 3.734rem;
        overflow-wrap: break-word;
        font-size: 0;
        text-transform: uppercase;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        line-height: 1.867rem;
        margin: 3.067rem 0 0 6.96rem;
        .paragraph_1 {
          width: 20rem;
          height: 1.734rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 1.333rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.867rem;
        }
        .text_7 {
          width: 20rem;
          height: 3.734rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 1.333rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.867rem;
        }
      }
      .text_8 {
        width: 14.72rem;
        height: 0.64rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.64rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.64rem;
        margin: 0.547rem 0 4.587rem 6.96rem;
      }
    }
    .group_4 {
      background-color: rgba(243, 245, 249, 1);
      width: 51.2rem;
      height: 20.587rem;
    }
    .group_5 {
      width: 51.2rem;
      height: 21.76rem;
      justify-content: flex-center;
      margin: 7.307rem 0 53.227rem 0;
      .text_9 {
        width: 5.12rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
        margin: 2.667rem 0 0 23.04rem;
      }
      .text-wrapper_23 {
        width: 26.694rem;
        height: 0.667rem;
        margin: 1.6rem 0 0 12.267rem;
        .text_10 {
          width: 3.84rem;
          height: 0.667rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 0.667rem;
          cursor: pointer;
          transition: all 0.3s ease;
          &:hover {
            color: rgba(33, 135, 250, 1);
            transform: scale(1.05);
          }
          &.active {
            color: rgba(33, 135, 250, 1);
            font-weight: 600;
          }
        }
        .text_11 {
          width: 4.32rem;
          height: 0.667rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.667rem;
          margin-left: 1.28rem;
          cursor: pointer;
          transition: all 0.3s ease;
          &:hover {
            color: rgba(33, 135, 250, 1);
            transform: scale(1.05);
          }
          &.active {
            color: rgba(33, 135, 250, 1);
            font-weight: 600;
          }
        }
        .text_12 {
          width: 3.814rem;
          height: 0.667rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.667rem;
          margin-left: 1.28rem;
          cursor: pointer;
          transition: all 0.3s ease;
          &:hover {
            color: rgba(33, 135, 250, 1);
            transform: scale(1.05);
          }
          &.active {
            color: rgba(33, 135, 250, 1);
            font-weight: 600;
          }
        }
        .text_13 {
          width: 4.8rem;
          height: 0.667rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.667rem;
          margin-left: 1.28rem;
          cursor: pointer;
          transition: all 0.3s ease;
          &:hover {
            color: rgba(33, 135, 250, 1);
            transform: scale(1.05);
          }
          &.active {
            color: rgba(33, 135, 250, 1);
            font-weight: 600;
          }
        }
        .text_14 {
          width: 4.8rem;
          height: 0.667rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.667rem;
          margin-left: 1.28rem;
          cursor: pointer;
          transition: all 0.3s ease;
          &:hover {
            color: rgba(33, 135, 250, 1);
            transform: scale(1.05);
          }
          &.active {
            color: rgba(33, 135, 250, 1);
            font-weight: 600;
          }
        }
      }
            .section_3 {
        background-color: rgba(33, 135, 250, 1);
        width: 1.92rem;
        height: 0.08rem;
        margin: 0.534rem 0 0 13.014rem;
        transition: all 0.3s ease;
        opacity: 1; // 默认显示第一个下划线

        &.underline-voice {
          opacity: 1;
          margin-left: 13.014rem;
        }

        &.underline-surgery {
          opacity: 1;
          margin-left: 18.587rem;
        }

        &.underline-rounds {
          opacity: 1;
          margin-left: 23.947rem;
        }

        &.underline-security {
          opacity: 1;
          margin-left: 29.52rem;
        }

        &.underline-multiterm {
          opacity: 1;
          margin-left: 35.6rem;
        }
      }
      .box_22 {
        width: 37.334rem;
        height: 10.667rem;
        margin: 1.6rem 0 2.667rem 6.907rem;
        .box_1 {
          background-color: rgba(255, 255, 255, 1);
          width: 18.694rem;
          height: 10.667rem;
          .text_15 {
            width: 6.827rem;
            height: 0.854rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.853rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.854rem;
            margin: 1.334rem 0 0 2.134rem;
          }
          .image_3 {
            width: 11.84rem;
            height: 0.027rem;
            margin: 1.067rem 0 0 2.134rem;
          }
          .paragraph_2 {
            width: 11.947rem;
            height: 4.827rem;
            overflow-wrap: break-word;
            color: rgba(68, 68, 68, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: left;
            line-height: 0.907rem;
            margin: 1.04rem 0 1.52rem 2.134rem;
          }
        }
        .box_2 {
          width: 18.64rem;
          height: 10.667rem;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          transition: all 0.3s ease;
        }
      }
    }
    .group_6 {
      position: absolute;
      left: 0;
      top: 12.8rem;
      width: 51.2rem;
      height: 25.494rem;
      .group_7 {
        width: 13.307rem;
        height: 1.867rem;
        background: url(/pages/assets/img/SketchPngaed1f01b3562df462a7ddb4de25d22f4a9f15653f937bde966abb5fca1717500.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin: 1.334rem 0 0 18.96rem;
        .text-wrapper_3 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          border-radius: 26px;
          width: 3.174rem;
          margin: 0.32rem 0 0 0.427rem;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);

            .text_16 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          &.active {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
          }
          &.active .text_16 {
            color: rgba(255, 255, 255, 1);
            font-weight: 600;
          }
          .text_16 {
            width: 2.107rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
        .text-wrapper_4 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.774rem;
          margin: 0.32rem 0 0 0.32rem;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 26px;

          &:hover {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);

            .text_17 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          &.active {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
          }
          &.active .text_17 {
            color: rgba(255, 255, 255, 1);
            font-weight: 600;
          }
          .text_17 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
        .text-wrapper_5 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.774rem;
          margin: 0.32rem 0 0 0.32rem;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 26px;

          &:hover {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);

            .text_18 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          &.active {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
          }
          &.active .text_18 {
            color: rgba(255, 255, 255, 1);
            font-weight: 600;
          }
          .text_18 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
        .text-wrapper_6 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.774rem;
          margin: 0.32rem 0.427rem 0 0.32rem;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 26px;

          &:hover {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);

            .text_19 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          &.active {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
          }
          &.active .text_19 {
            color: rgba(255, 255, 255, 1);
            font-weight: 600;
          }
          .text_19 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
      }
      .text-group_12 {
        width: 23.414rem;
        height: 2.56rem;
        margin: 2.667rem 0 0 13.894rem;
        .text_20 {
          width: 6.347rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 1.28rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          white-space: nowrap;
          line-height: 1.28rem;
          margin-left: 8.534rem;
        }
        .text_21 {
          width: 23.414rem;
          height: 0.747rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.533rem;
          text-transform: uppercase;
          font-weight: NaN;
          text-align: right;
          white-space: nowrap;
          line-height: 0.747rem;
          margin-top: 0.534rem;
        }
      }
      .group_27 {
        width: 34.667rem;
        height: 12.8rem;
        margin: 1.6rem 0 2.667rem 8.24rem;
        .box_3 {
          width: 10.667rem;
          height: 12.8rem;
          background: url(/pages/assets/img/SketchPngdb704a57217c9839419996e5f6c9a299fdd69b6f1ba69f823f04ebcf8afc972c.png)
            100% no-repeat;
          background-size: 100% 100%;
          .image_4 {
            width: 5.067rem;
            height: 5.067rem;
            margin: 1.334rem 0 0 2.8rem;
          }
          .text-wrapper_7 {
            width: 8.8rem;
            height: 4.534rem;
            overflow-wrap: break-word;
            font-size: 0;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: justify;
            line-height: 1.067rem;
            margin: 1.067rem 0 0.8rem 0.934rem;
            .text_22 {
              width: 8.8rem;
              height: 4.534rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
            .text_23 {
              width: 8.8rem;
              height: 4.534rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              text-decoration: underline;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
            .paragraph_3 {
              width: 8.8rem;
              height: 4.534rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
          }
        }
        .box_4 {
          width: 10.667rem;
          height: 12.8rem;
          background: url(/pages/assets/img/SketchPng764d348289f138d96368d982e24de99dca28406c9e738e4e71864ce1bb42b459.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 1.334rem;
          .image_5 {
            width: 5.067rem;
            height: 5.067rem;
            margin: 1.334rem 0 0 2.8rem;
          }
          .text-wrapper_8 {
            width: 8.8rem;
            height: 4.267rem;
            overflow-wrap: break-word;
            font-size: 0;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: justify;
            line-height: 1.067rem;
            margin: 1.067rem 0 1.067rem 0.934rem;
            .text_24 {
              width: 8.8rem;
              height: 4.267rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
            .paragraph_4 {
              width: 8.8rem;
              height: 4.267rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              text-decoration: underline;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
          }
        }
        .box_5 {
          width: 10.667rem;
          height: 12.8rem;
          background: url(/pages/assets/img/SketchPng666fe350b741467219347ab9915d4959fa2f70903e830aadd8a8f0b02afce31f.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 1.334rem;
          .image_6 {
            width: 5.067rem;
            height: 5.067rem;
            margin: 1.334rem 0 0 2.8rem;
          }
          .paragraph_5 {
            width: 8.8rem;
            height: 4.267rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: justify;
            line-height: 1.067rem;
            margin: 1.067rem 0 1.067rem 0.934rem;
          }
        }
      }
    }
    .text-wrapper_9 {
      height: 5.867rem;
      background: url(/pages/assets/img/SketchPng0f90dfe39f18209f2a52c759e9bc2ab9c22f79375c501c260660074e2c89d93d.png)
        100% no-repeat;
      background-size: 100% 100%;
      width: 37.334rem;
      position: absolute;
      left: 6.907rem;
      top: 34.827rem;
      .paragraph_6 {
        width: 21.654rem;
        height: 2.294rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.693rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        line-height: 1.147rem;
        margin: 2.187rem 0 0 7.84rem;
      }
    }
    .group_9 {
      background-color: rgba(255, 255, 255, 1);
      height: 24.534rem;
      width: 51.2rem;
      position: absolute;
      left: 0;
      top: 91.36rem;
      justify-content: flex-center;
      .text-wrapper_24 {
        width: 5.12rem;
        height: 1.28rem;
        margin: 2.667rem 0 0 23.04rem;
        .text_25 {
          width: 5.12rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 1.28rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          white-space: nowrap;
          line-height: 1.28rem;
        }
      }
      .group_28 {
        width: 37.36rem;
        height: 15.254rem;
        margin: 2.667rem 0 2.667rem 6.907rem;
        .box_6 {
          position: relative;
          width: 8.8rem;
          height: 15.227rem;
          background: url(/pages/assets/img/SketchPng66a0ec8be2e0416cb259a3c0c0455698a9e13a1ff664b076d9b7c6e9ccc0af7b.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_13 {
            width: 4.267rem;
            height: 3.414rem;
            margin: 1.067rem 0 0 1.067rem;
            .text_26 {
              width: 2.214rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.746rem;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .paragraph_7 {
              width: 4.267rem;
              height: 1.707rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.533rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 0.854rem;
              margin-top: 0.64rem;
            }
          }
          .block_2 {
            background-color: rgba(33, 135, 250, 1);
            width: 6.667rem;
            height: 2.134rem;
            margin: 1.067rem 0 0 1.067rem;
            .section_6 {
              width: 1.387rem;
              height: 0.64rem;
              background: url(/pages/assets/img/SketchPng52525bf7010f048888ae7b887f4c649ab2257a4619f627014ce736aceeac0873.png)
                100% no-repeat;
              background-size: 100% 100%;
              margin: 0.747rem 0 0 0.747rem;
            }
            .text_27 {
              width: 2.56rem;
              height: 0.854rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.64rem;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.854rem;
              margin: 0.64rem 1.174rem 0 0.8rem;
            }
          }
          .text-wrapper_11 {
            background-color: rgba(255, 255, 255, 0.1);
            height: 2.134rem;
            width: 6.667rem;
            margin: 0.854rem 0 0 1.067rem;
            .text_28 {
              width: 2.56rem;
              height: 0.854rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.64rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.854rem;
              margin: 0.64rem 0 0 2.934rem;
            }
          }
          .text-wrapper_12 {
            background-color: rgba(255, 255, 255, 0.1);
            height: 2.134rem;
            width: 6.667rem;
            margin: 0.854rem 0 1.574rem 1.067rem;
            .text_29 {
              width: 2.56rem;
              height: 0.854rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.64rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.854rem;
              margin: 0.64rem 0 0 2.934rem;
            }
          }
          .image_7 {
            position: absolute;
            left: 1.067rem;
            top: 8.4rem;
            width: 6.667rem;
            height: 5.12rem;
          }
        }
        .box_7 {
          height: 15.254rem;
          background: url(/pages/assets/img/SketchPng0313393c8edb391c8b76b60aa67eb6e89f873d484af028ad2fbc82a210198b8e.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: -0.026rem;
          width: 28.587rem;
          .image-wrapper_1 {
            background-color: rgba(0, 0, 0, 0.4);
            height: 15.254rem;
            width: 28.587rem;
            .image_8 {
              width: 2.667rem;
              height: 2.667rem;
              margin: 6.294rem 0 0 12.96rem;
            }
          }
        }
      }
    }
    .group_10 {
      position: absolute;
      left: -0.026rem;
      top: 62.454rem;
      width: 51.2rem;
      height: 23.2rem;
      background: url(/pages/assets/img/SketchPngf92c3c48c853400933b44e58bd65890f34d970480e436f907c7471dc0da0e396.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text-group_14 {
        width: 13.76rem;
        height: 2.454rem;
        margin: 2.667rem 0 0 18.72rem;
        .text_30 {
          width: 5.12rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 1.28rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          white-space: nowrap;
          line-height: 1.28rem;
          margin-left: 4.32rem;
        }
        .text_31 {
          width: 13.76rem;
          height: 0.534rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          text-transform: uppercase;
          font-weight: NaN;
          text-align: right;
          white-space: nowrap;
          line-height: 0.534rem;
          margin-top: 0.64rem;
        }
      }
      .grid_2 {
        width: 39.334rem;
        height: 13.814rem;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        margin: 2.134rem 0 2.134rem 6.934rem;
        .group_11 {
          height: 6.374rem;
          background: url(/pages/assets/img/SketchPngdd1731993b237d9fb5f642ead42baacfdfe738b202f9849a21a3b574b6fcd027.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 11.734rem;
          position: relative;
          margin: 0 1.067rem 1.067rem 0;
          .text-wrapper_25 {
            width: 5.76rem;
            height: 0.88rem;
            margin: 1.067rem 0 0 0.64rem;
            .text_32 {
              width: 5.76rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
          }
          .block_7 {
            width: 10.587rem;
            height: 3.387rem;
            margin: 0.64rem 0 0.4rem 0.64rem;
            .image-text_19 {
              position: relative;
              width: 8.267rem;
              height: 2.72rem;
              .image-text_20 {
                position: relative;
                width: 8.267rem;
                height: 2.72rem;
                .image-text_21 {
                  width: 8.267rem;
                  height: 2.72rem;
                  .block_3 {
                    background-color: rgba(33, 135, 250, 1);
                    border-radius: 50%;
                    width: 0.214rem;
                    height: 0.214rem;
                    margin-top: 2.16rem;
                  }
                  .paragraph_8 {
                    width: 7.68rem;
                    height: 2.72rem;
                    overflow-wrap: break-word;
                    color: rgba(102, 102, 102, 1);
                    font-size: 0.426rem;
                    font-weight: NaN;
                    text-align: left;
                    line-height: 0.907rem;
                  }
                }
                .block_4 {
                  background-color: rgba(33, 135, 250, 1);
                  border-radius: 50%;
                  position: absolute;
                  left: 0;
                  top: 0.347rem;
                  width: 0.214rem;
                  height: 0.214rem;
                }
              }
              .block_5 {
                background-color: rgba(33, 135, 250, 1);
                border-radius: 50%;
                position: absolute;
                left: 0;
                top: 1.254rem;
                width: 0.214rem;
                height: 0.214rem;
              }
            }
            .image_9 {
              width: 1.68rem;
              height: 1.867rem;
              margin-top: 1.52rem;
            }
          }
          .group_13 {
            border-radius: 8px;
            position: absolute;
            left: 9.067rem;
            top: 3.707rem;
            width: 2.667rem;
            height: 2.667rem;
          }
        }
        .group_14 {
          height: 6.374rem;
          background: url(/pages/assets/img/SketchPngdd1731993b237d9fb5f642ead42baacfdfe738b202f9849a21a3b574b6fcd027.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 11.734rem;
          position: relative;
          margin: 0 1.067rem 1.067rem 0;
          .text-wrapper_26 {
            width: 5.12rem;
            height: 0.88rem;
            margin: 1.067rem 0 0 0.64rem;
            .text_33 {
              width: 5.12rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
          }
          .group_29 {
            width: 10.454rem;
            height: 3.12rem;
            margin: 0.64rem 0 0.667rem 0.64rem;
            .image-text_22 {
              position: relative;
              width: 8.054rem;
              height: 1.814rem;
              .image-text_23 {
                width: 8.054rem;
                height: 1.814rem;
                .group_15 {
                  background-color: rgba(33, 135, 250, 1);
                  border-radius: 50%;
                  width: 0.214rem;
                  height: 0.214rem;
                  margin-top: 0.347rem;
                }
                .paragraph_9 {
                  width: 7.467rem;
                  height: 1.814rem;
                  overflow-wrap: break-word;
                  color: rgba(102, 102, 102, 1);
                  font-size: 0.426rem;
                  font-weight: NaN;
                  text-align: left;
                  line-height: 0.907rem;
                }
              }
              .box_9 {
                background-color: rgba(33, 135, 250, 1);
                border-radius: 50%;
                position: absolute;
                left: 0;
                top: 1.254rem;
                width: 0.214rem;
                height: 0.214rem;
              }
            }
            .image_10 {
              width: 1.84rem;
              height: 1.894rem;
              margin-top: 1.227rem;
            }
          }
          .box_10 {
            border-radius: 8px;
            position: absolute;
            left: 8.854rem;
            top: 3.494rem;
            width: 2.667rem;
            height: 2.667rem;
          }
        }
        .group_16 {
          height: 6.374rem;
          background: url(/pages/assets/img/SketchPngdd1731993b237d9fb5f642ead42baacfdfe738b202f9849a21a3b574b6fcd027.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 11.734rem;
          position: relative;
          margin: 0 1.067rem 1.067rem 0;
          .text-wrapper_27 {
            width: 5.12rem;
            height: 0.88rem;
            margin: 1.067rem 0 0 0.64rem;
            .text_34 {
              width: 5.12rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
          }
          .section_11 {
            width: 10.507rem;
            height: 3.227rem;
            margin: 0.64rem 0 0.56rem 0.64rem;
            .image-text_24 {
              position: relative;
              width: 7.787rem;
              height: 1.814rem;
              .image-text_25 {
                width: 7.787rem;
                height: 1.814rem;
                .group_17 {
                  background-color: rgba(33, 135, 250, 1);
                  border-radius: 50%;
                  width: 0.214rem;
                  height: 0.214rem;
                  margin-top: 0.347rem;
                }
                .paragraph_10 {
                  width: 7.2rem;
                  height: 1.814rem;
                  overflow-wrap: break-word;
                  color: rgba(102, 102, 102, 1);
                  font-size: 0.426rem;
                  font-weight: NaN;
                  text-align: left;
                  line-height: 0.907rem;
                }
              }
              .box_12 {
                background-color: rgba(33, 135, 250, 1);
                border-radius: 50%;
                position: absolute;
                left: 0;
                top: 1.254rem;
                width: 0.214rem;
                height: 0.214rem;
              }
            }
            .image_11 {
              width: 1.92rem;
              height: 1.84rem;
              margin-top: 1.387rem;
            }
          }
          .box_13 {
            border-radius: 8px;
            position: absolute;
            left: 8.854rem;
            top: 3.494rem;
            width: 2.667rem;
            height: 2.667rem;
          }
        }
        .group_18 {
          height: 6.374rem;
          background: url(/pages/assets/img/SketchPngdd1731993b237d9fb5f642ead42baacfdfe738b202f9849a21a3b574b6fcd027.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 11.734rem;
          margin: 0 1.067rem 1.067rem 0;
          .text-wrapper_28 {
            width: 5.76rem;
            height: 0.88rem;
            margin: 1.067rem 0 0 0.64rem;
            .text_35 {
              width: 5.76rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
          }
          .section_12 {
            width: 10.88rem;
            height: 3.574rem;
            margin: 0.64rem 0 0.214rem 0.64rem;
            .image-text_26 {
              position: relative;
              width: 5.254rem;
              height: 2.72rem;
              .image-text_27 {
                position: relative;
                width: 5.254rem;
                height: 2.72rem;
                .image-text_28 {
                  width: 5.254rem;
                  height: 2.72rem;
                  .group_19 {
                    background-color: rgba(33, 135, 250, 1);
                    border-radius: 50%;
                    width: 0.214rem;
                    height: 0.214rem;
                    margin-top: 2.16rem;
                  }
                  .paragraph_11 {
                    width: 4.667rem;
                    height: 2.72rem;
                    overflow-wrap: break-word;
                    color: rgba(102, 102, 102, 1);
                    font-size: 0.426rem;
                    font-weight: NaN;
                    text-align: left;
                    line-height: 0.907rem;
                  }
                }
                .box_15 {
                  background-color: rgba(33, 135, 250, 1);
                  border-radius: 50%;
                  position: absolute;
                  left: 0;
                  top: 0.347rem;
                  width: 0.214rem;
                  height: 0.214rem;
                }
              }
              .block_6 {
                background-color: rgba(33, 135, 250, 1);
                border-radius: 50%;
                position: absolute;
                left: 0;
                top: 1.254rem;
                width: 0.214rem;
                height: 0.214rem;
              }
            }
            .image-wrapper_2 {
              border-radius: 8px;
              height: 2.667rem;
              margin-top: 0.907rem;
              width: 2.667rem;
              .image_12 {
                width: 2.187rem;
                height: 2rem;
                margin: 0.347rem 0 0 0.24rem;
              }
            }
          }
        }
        .group_20 {
          height: 6.374rem;
          background: url(/pages/assets/img/SketchPngdd1731993b237d9fb5f642ead42baacfdfe738b202f9849a21a3b574b6fcd027.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 11.734rem;
          margin: 0 1.067rem 1.067rem 0;
          .text-wrapper_29 {
            width: 5.12rem;
            height: 0.88rem;
            margin: 1.067rem 0 0 0.64rem;
            .text_36 {
              width: 5.12rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
          }
          .block_8 {
            width: 10.534rem;
            height: 3.334rem;
            margin: 0.64rem 0 0.454rem 0.64rem;
            .image-text_29 {
              position: relative;
              width: 6.56rem;
              height: 1.814rem;
              .image-text_30 {
                width: 6.56rem;
                height: 1.814rem;
                .box_16 {
                  background-color: rgba(33, 135, 250, 1);
                  border-radius: 50%;
                  width: 0.214rem;
                  height: 0.214rem;
                  margin-top: 0.347rem;
                }
                .paragraph_12 {
                  width: 5.974rem;
                  height: 1.814rem;
                  overflow-wrap: break-word;
                  color: rgba(102, 102, 102, 1);
                  font-size: 0.426rem;
                  font-weight: NaN;
                  text-align: left;
                  line-height: 0.907rem;
                }
              }
              .section_8 {
                background-color: rgba(33, 135, 250, 1);
                border-radius: 50%;
                position: absolute;
                left: 0;
                top: 1.254rem;
                width: 0.214rem;
                height: 0.214rem;
              }
            }
            .image_13 {
              width: 1.894rem;
              height: 2.107rem;
              margin-top: 1.227rem;
            }
          }
        }
        .group_21 {
          height: 6.374rem;
          background: url(/pages/assets/img/SketchPngdd1731993b237d9fb5f642ead42baacfdfe738b202f9849a21a3b574b6fcd027.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 11.734rem;
          margin: 0 0 1.067rem 0;
          .text-wrapper_30 {
            width: 6.347rem;
            height: 0.88rem;
            margin: 1.067rem 0 0 0.64rem;
            .text_37 {
              width: 6.347rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
          }
          .box_23 {
            width: 10.694rem;
            height: 3.387rem;
            margin: 0.64rem 0 0.4rem 0.64rem;
            .image-text_31 {
              position: relative;
              width: 5.707rem;
              height: 1.814rem;
              .image-text_32 {
                width: 5.707rem;
                height: 1.814rem;
                .group_23 {
                  background-color: rgba(33, 135, 250, 1);
                  border-radius: 50%;
                  width: 0.214rem;
                  height: 0.214rem;
                  margin-top: 0.347rem;
                }
                .paragraph_13 {
                  width: 5.12rem;
                  height: 1.814rem;
                  overflow-wrap: break-word;
                  color: rgba(102, 102, 102, 1);
                  font-size: 0.426rem;
                  font-weight: NaN;
                  text-align: left;
                  line-height: 0.907rem;
                }
              }
              .box_17 {
                background-color: rgba(33, 135, 250, 1);
                border-radius: 50%;
                position: absolute;
                left: 0;
                top: 1.254rem;
                width: 0.214rem;
                height: 0.214rem;
              }
            }
            .image_14 {
              width: 2.294rem;
              height: 2.294rem;
              margin-top: 1.094rem;
            }
          }
        }
      }
    }
    .group_24 {
      background-color: rgba(36, 55, 76, 1);
      position: absolute;
      left: -0.026rem;
      top: 85.654rem;
      width: 51.2rem;
      height: 5.707rem;
      .text-group_15 {
        width: 3.2rem;
        height: 3.307rem;
        margin: 1.067rem 0 0 12.134rem;
        .text-wrapper_19 {
          width: 1.494rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 0.854rem;
          .text_38 {
            width: 1.494rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_39 {
            width: 1.494rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .text_40 {
          width: 3.2rem;
          height: 0.747rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
      .text-group_16 {
        width: 4.267rem;
        height: 3.307rem;
        margin: 1.067rem 0 0 4rem;
        .text-wrapper_20 {
          width: 2.374rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 1.014rem;
          .text_41 {
            width: 2.374rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_42 {
            width: 2.374rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .text_43 {
          width: 4.267rem;
          height: 0.747rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
      .text-group_17 {
        width: 3.2rem;
        height: 3.307rem;
        margin: 1.067rem 0 0 4rem;
        .text-wrapper_21 {
          width: 2.454rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 0.534rem;
          .text_44 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_45 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .text_46 {
          width: 3.2rem;
          height: 0.747rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
      .text-group_18 {
        width: 4.267rem;
        height: 3.307rem;
        margin: 1.067rem 12.134rem 0 4rem;
        .text-wrapper_22 {
          width: 2.374rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 1.014rem;
          .text_47 {
            width: 2.374rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_48 {
            width: 2.374rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .text_49 {
          width: 4.267rem;
          height: 0.747rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
    }
  }
  .group_25 {
    background-color: rgba(17, 26, 52, 1);
    width: 51.2rem;
    height: 11.04rem;
    margin: -0.027rem 0 0.24rem 0;
    .block_9 {
      width: 31.787rem;
      height: 3.44rem;
      margin: 1.36rem 0 0 9.6rem;
      .box_24 {
        width: 7.44rem;
        height: 2.934rem;
        margin-top: 0.507rem;
        .image-text_33 {
          width: 4.374rem;
          height: 0.374rem;
          .thumbnail_5 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_8 {
            width: 3.734rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
        .image-text_34 {
          width: 5.707rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_6 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_9 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
            .text_50 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
            .text_51 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
          }
        }
        .image-text_35 {
          width: 7.44rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_7 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_10 {
            width: 6.8rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
      }
      .image-text_36 {
        width: 3.36rem;
        height: 3.44rem;
        .box_18 {
          border-radius: 8px;
          background-image: url(/pages/assets/img/b1e50ec4b96a46deb4f05f0773f8f35f_mergeImage.png);
          background-repeat: no-repeat;
          background-size: cover;
          width: 2.667rem;
          height: 2.667rem;
          margin-left: 0.347rem;
        }
        .text-group_11 {
          width: 3.36rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
          margin-top: 0.4rem;
        }
      }
    }
    .section_10 {
      width: 51.2rem;
      height: 4.054rem;
      background: url(/pages/assets/img/SketchPng5c8ba53f71bd92f957537da4d03c76390cd3535fdd5fe926d77e0f7c541f1f80.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 1.387rem 0 0.8rem 0;
      .image_15 {
        width: 4.267rem;
        height: 0.64rem;
        margin: 0.854rem 0 0 9.6rem;
      }
      .paragraph_14 {
        width: 7.227rem;
        height: 1.494rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.426rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        line-height: 0.747rem;
        margin: 0.587rem 0 0.48rem 9.6rem;
      }
    }
  }

