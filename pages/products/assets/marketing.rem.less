.page {
  background-color: rgba(243, 245, 249, 1);
  position: relative;
  width: 100%;
  max-width: 51.2rem;
  margin: 0 auto;
  padding-top: 2.187rem;
  .box_1 {
    background-color: rgba(255, 255, 255, 1);
    height: 2.187rem;
    width: 51.2rem;
    .box_12 {
      width: 40.534rem;
      height: 0.64rem;
      margin: 0.774rem 0 0 5.334rem;
      .image_1 {
        width: 4.267rem;
        height: 0.64rem;
      }
      .text_1 {
        width: 0.907rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 15.867rem;
      }
      .text_2 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 2.08rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.08rem 0 0 0.107rem;
      }
      .text_3 {
        width: 3.174rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_4 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_5 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_6 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
    }
    .box_13 {
      width: 1.814rem;
      height: 0.08rem;
      margin: 0.24rem 0 0.454rem 28.427rem;
      .group_1 {
        background-color: rgba(33, 135, 250, 1);
        width: 1.814rem;
        height: 0.08rem;
      }
    }
  }
  .box_2 {
    background-image: url(/pages/assets/img/911ca43367df49b7904707d3106bc8f6_mergeImage.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    width: 51.2rem;
    height: 12.8rem;
    .image_2 {
      width: 51.2rem;
      height: 0.027rem;
    }
    .text_7 {
      width: 11.92rem;
      height: 1.334rem;
      overflow-wrap: break-word;
      color: rgba(33, 135, 250, 1);
      font-size: 1.333rem;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 1.334rem;
      margin: 4.347rem 0 0 6.96rem;
    }
    .text_8 {
      width: 7.014rem;
      height: 0.64rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 0.64rem;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 0.64rem;
      margin: 0.96rem 0 5.494rem 6.96rem;
    }
    .group_2 {
      position: absolute;
      left: 42.454rem;
      top: 0.027rem;
      width: 8.747rem;
      height: 12.8rem;
      background: url(/pages/assets/img/SketchPng9383157a6b177c56e14fb12f50d7b1ffc736a8ee8a5c454d89141f0a87dfc475.png)
        100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .box_3 {
    background-color: rgba(243, 245, 249, 1);
    position: relative;
    width: 51.2rem;
    height: 28.4rem;
    .block_3 {
      width: 13.734rem;
      height: 1.867rem;
      background: url(/pages/assets/img/SketchPng0e0d33df318eb84b68c25378364b3f9cd446e8a819351c4506f5e0e2ef97abbb.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 1.334rem 0 0 18.747rem;
      .text-wrapper_1, .text-wrapper_2, .text-wrapper_3, .text-wrapper_4 {
        height: 1.227rem;
        background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 2.774rem;
        margin: 0.32rem 0 0 0.32rem;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 26px;

        &:hover {
          background-color: rgba(33, 135, 250, 1);
          background-image: none;
          transform: scale(1.05);
          box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);

          span {
            color: rgba(255, 255, 255, 1);
            font-weight: 600;
          }
        }

        &.active {
          background: url(/pages/assets/img/SketchPng9ad2efdfc829d369484054f79f449a7841e17ea1afdc3244a08add216d921126.png)
            100% no-repeat;
          background-size: 100% 100%;
        }

        span {
          width: 1.707rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.426rem;
          text-transform: uppercase;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.587rem;
          margin: 0.32rem 0 0 0.534rem;
          transition: color 0.3s ease;
        }

        &.active span {
          color: rgba(255, 255, 255, 1);
          font-family: PingFangSC-Semibold;
          font-weight: 600;
        }
      }

      .text-wrapper_1 {
        margin: 0.32rem 0 0 0.427rem;
      }

      .text-wrapper_4 {
        margin: 0.32rem 1.254rem 0 0.32rem;
      }
    }
    .text-group_9 {
      width: 13.227rem;
      height: 2.454rem;
      margin: 2.667rem 0 0 18.987rem;
      .text_13 {
        width: 5.12rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 1.28rem;
        text-transform: uppercase;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
        margin-left: 4.054rem;
      }
      .text_14 {
        width: 13.227rem;
        height: 0.534rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.533rem;
        text-transform: uppercase;
        font-weight: NaN;
        text-align: right;
        white-space: nowrap;
        line-height: 0.534rem;
        margin-top: 0.64rem;
      }
    }
    .text-wrapper_5 {
      width: 17.76rem;
      height: 5.067rem;
      overflow-wrap: break-word;
      font-size: 0;
      font-weight: NaN;
      text-align: justify;
      line-height: 1.014rem;
      margin: 4.24rem 0 0 25.414rem;
      .text_15 {
        width: 17.76rem;
        height: 5.067rem;
        overflow-wrap: break-word;
        color: rgba(1, 1, 1, 1);
        font-size: 0.533rem;
        font-weight: NaN;
        text-align: left;
        line-height: 1.014rem;
      }
      .text_16 {
        width: 17.76rem;
        height: 5.067rem;
        overflow-wrap: break-word;
        color: rgba(33, 135, 250, 1);
        font-size: 0.533rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        line-height: 1.014rem;
      }
      .paragraph_1 {
        width: 17.76rem;
        height: 5.067rem;
        overflow-wrap: break-word;
        color: rgba(1, 1, 1, 1);
        font-size: 0.533rem;
        font-weight: NaN;
        text-align: left;
        line-height: 1.014rem;
      }
      .text_17 {
        width: 17.76rem;
        height: 5.067rem;
        overflow-wrap: break-word;
        color: rgba(33, 135, 250, 1);
        font-size: 0.533rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        line-height: 1.014rem;
      }
      .text_18 {
        width: 17.76rem;
        height: 5.067rem;
        overflow-wrap: break-word;
        color: rgba(1, 1, 1, 1);
        font-size: 0.533rem;
        font-weight: NaN;
        text-align: left;
        line-height: 1.014rem;
      }
    }
    .block_4 {
      background-image: url(/pages/assets/img/da510eaad7b84b01bd27ad40c2c68ddd_mergeImage.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 37.334rem;
      height: 8.32rem;
      margin: 2.454rem 0 0 6.907rem;
      .text-wrapper_6 {
        width: 19.2rem;
        height: 0.907rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.907rem;
        margin: 2.454rem 0 0 1.334rem;
        .text_19 {
          width: 19.2rem;
          height: 0.907rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.48rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.907rem;
        }
        .text_20 {
          width: 19.2rem;
          height: 0.907rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 0.64rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 0.907rem;
        }
        .text_21 {
          width: 19.2rem;
          height: 0.907rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.48rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.907rem;
        }
      }
      .block_15 {
        width: 34.667rem;
        height: 2.827rem;
        margin: 0.8rem 0 1.334rem 1.334rem;
        .text-wrapper_7 {
          height: 2.827rem;
          background: url(/pages/assets/img/SketchPngecbf7fdfca6541f929e7d0e44f763fced38e6d6e7b4c87fde22f86bff267cc94.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 16.854rem;
          .paragraph_2 {
            width: 15.814rem;
            height: 1.494rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.72rem;
            margin: 0.667rem 0 0 0.534rem;
          }
        }
        .text-wrapper_8 {
          height: 2.827rem;
          background: url(/pages/assets/img/SketchPngecbf7fdfca6541f929e7d0e44f763fced38e6d6e7b4c87fde22f86bff267cc94.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 16.854rem;
          .paragraph_3 {
            width: 15.814rem;
            height: 1.494rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.72rem;
            margin: 0.667rem 0 0 0.534rem;
          }
        }
      }
    }
    .block_5 {
      height: 9.12rem;
      background: url(/pages/assets/img/SketchPng55427167b0afdab9e9c5de34fea93deaf9da3e7b0f0c2e1ec9ad16c7174a8521.png)
        100% no-repeat;
      background-size: 100% 100%;
      width: 17.68rem;
      position: absolute;
      left: 7.2rem;
      top: 11.627rem;
      .group_3 {
        border-radius: 2px;
        background-image: url(/pages/assets/img/45a1e3b02ca84813930b19d9c2582e06_mergeImage.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 13.787rem;
        height: 7.76rem;
        border: 2px solid rgba(255, 255, 255, 1);
        margin: 0.347rem 0 0 1.947rem;
      }
    }
    .image_3 {
      position: absolute;
      left: 40.64rem;
      top: 19.44rem;
      width: 2.534rem;
      height: 2.187rem;
    }
  }
  .box_5 {
    background-color: rgba(255, 255, 255, 1);
    height: 22.827rem;
    width: 51.2rem;
    position: relative;
    .group_14 {
      width: 5.12rem;
      height: 2.454rem;
      margin: 2.667rem 0 0 23.04rem;
      .text-group_10 {
        width: 5.12rem;
        height: 2.454rem;
        .text_22 {
          width: 5.12rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 1.28rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          white-space: nowrap;
          line-height: 1.28rem;
        }
        .text_23 {
          width: 4.747rem;
          height: 0.534rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: right;
          white-space: nowrap;
          line-height: 0.534rem;
          margin: 0.64rem 0 0 0.187rem;
        }
      }
    }
    .text-wrapper_16 {
      width: 30.134rem;
      height: 1.787rem;
      margin: 2.134rem 0 0 13.52rem;
      .text_24 {
        background-image: linear-gradient(
          165deg,
          rgba(236, 243, 252, 1) 0,
          rgba(255, 255, 255, 1) 100%
        );
        width: 1.307rem;
        height: 1.787rem;
        -webkit-text-stroke: 1px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: justify;
        white-space: nowrap;
        line-height: 1.787rem;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .text_25 {
        background-image: linear-gradient(
          165deg,
          rgba(236, 243, 252, 1) 0,
          rgba(255, 255, 255, 1) 100%
        );
        width: 1.547rem;
        height: 1.787rem;
        -webkit-text-stroke: 1px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: justify;
        white-space: nowrap;
        line-height: 1.787rem;
        margin-left: 8.187rem;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .text_26 {
        background-image: linear-gradient(
          165deg,
          rgba(236, 243, 252, 1) 0,
          rgba(255, 255, 255, 1) 100%
        );
        width: 1.547rem;
        height: 1.787rem;
        -webkit-text-stroke: 1px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: justify;
        white-space: nowrap;
        line-height: 1.787rem;
        margin-left: 8rem;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .text_27 {
        background-image: linear-gradient(
          165deg,
          rgba(236, 243, 252, 1) 0,
          rgba(255, 255, 255, 1) 100%
        );
        width: 1.547rem;
        height: 1.787rem;
        -webkit-text-stroke: 1px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: justify;
        white-space: nowrap;
        line-height: 1.787rem;
        margin-left: 8rem;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .text-wrapper_17 {
      width: 30.134rem;
      height: 1.787rem;
      margin: 5.067rem 0 6.934rem 13.52rem;
      .text_28 {
        background-image: linear-gradient(
          165deg,
          rgba(236, 243, 252, 1) 0,
          rgba(255, 255, 255, 1) 100%
        );
        width: 1.547rem;
        height: 1.787rem;
        -webkit-text-stroke: 1px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: justify;
        white-space: nowrap;
        line-height: 1.787rem;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .text_29 {
        background-image: linear-gradient(
          165deg,
          rgba(236, 243, 252, 1) 0,
          rgba(255, 255, 255, 1) 100%
        );
        width: 1.547rem;
        height: 1.787rem;
        -webkit-text-stroke: 1px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: justify;
        white-space: nowrap;
        line-height: 1.787rem;
        margin-left: 7.947rem;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .text_30 {
        background-image: linear-gradient(
          165deg,
          rgba(236, 243, 252, 1) 0,
          rgba(255, 255, 255, 1) 100%
        );
        width: 1.494rem;
        height: 1.787rem;
        -webkit-text-stroke: 1px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: justify;
        white-space: nowrap;
        line-height: 1.787rem;
        margin-left: 8rem;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .text_31 {
        background-image: linear-gradient(
          165deg,
          rgba(236, 243, 252, 1) 0,
          rgba(255, 255, 255, 1) 100%
        );
        width: 1.547rem;
        height: 1.787rem;
        -webkit-text-stroke: 1px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: justify;
        white-space: nowrap;
        line-height: 1.787rem;
        margin-left: 8.054rem;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .block_7 {
      position: absolute;
      left: 6.907rem;
      top: 7.44rem;
      width: 8.747rem;
      height: 5.867rem;
      background: url(/pages/assets/img/SketchPng6b362270885d8222b61706716cf82dcf3a783d0d33b67a6402c0c037dbaac3bd.png) -0.027rem -0.027rem
        no-repeat;
      background-size: 8.8rem 5.92rem;
      .text_32 {
        width: 4.64rem;
        height: 0.88rem;
        overflow-wrap: break-word;
        color: rgba(54, 146, 250, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.88rem;
        margin: 0.64rem 0 0 0.427rem;
      }
      .image_4 {
        width: 5.254rem;
        height: 0.027rem;
        margin: 0.507rem 0 0 0.427rem;
      }
      .text_33 {
        width: 7.894rem;
        height: 2.347rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: justify;
        line-height: 0.587rem;
        margin: 0.64rem 0 0.827rem 0.427rem;
      }
    }
    .block_8 {
      position: absolute;
      left: 6.907rem;
      top: 14.294rem;
      width: 8.747rem;
      height: 5.867rem;
      background: url(/pages/assets/img/SketchPng6b362270885d8222b61706716cf82dcf3a783d0d33b67a6402c0c037dbaac3bd.png) -0.027rem -0.027rem
        no-repeat;
      background-size: 8.8rem 5.92rem;
      .text_34 {
        width: 2.56rem;
        height: 0.88rem;
        overflow-wrap: break-word;
        color: rgba(54, 146, 250, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.88rem;
        margin: 0.64rem 0 0 0.427rem;
      }
      .image_5 {
        width: 5.254rem;
        height: 0.027rem;
        margin: 0.534rem 0 0 0.427rem;
      }
      .text_35 {
        width: 7.894rem;
        height: 2.347rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: justify;
        line-height: 0.587rem;
        margin: 0.614rem 0 0.827rem 0.427rem;
      }
    }
    .block_9 {
      position: absolute;
      left: 26rem;
      top: 7.44rem;
      width: 8.747rem;
      height: 5.867rem;
      background: url(/pages/assets/img/SketchPng6b362270885d8222b61706716cf82dcf3a783d0d33b67a6402c0c037dbaac3bd.png) -0.027rem -0.027rem
        no-repeat;
      background-size: 8.8rem 5.92rem;
      .text_36 {
        width: 5.12rem;
        height: 0.88rem;
        overflow-wrap: break-word;
        color: rgba(54, 146, 250, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.88rem;
        margin: 0.64rem 0 0 0.427rem;
      }
      .image_6 {
        width: 5.254rem;
        height: 0.027rem;
        margin: 0.507rem 0 0 0.427rem;
      }
      .text_37 {
        width: 7.894rem;
        height: 2.347rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: justify;
        line-height: 0.587rem;
        margin: 0.64rem 0 0.827rem 0.427rem;
      }
    }
    .block_10 {
      position: absolute;
      left: 26rem;
      top: 14.294rem;
      width: 8.747rem;
      height: 5.867rem;
      background: url(/pages/assets/img/SketchPng6b362270885d8222b61706716cf82dcf3a783d0d33b67a6402c0c037dbaac3bd.png) -0.027rem -0.027rem
        no-repeat;
      background-size: 8.8rem 5.92rem;
      .text_38 {
        width: 2.56rem;
        height: 0.88rem;
        overflow-wrap: break-word;
        color: rgba(54, 146, 250, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.88rem;
        margin: 0.64rem 0 0 0.427rem;
      }
      .image_7 {
        width: 5.254rem;
        height: 0.027rem;
        margin: 0.534rem 0 0 0.427rem;
      }
      .text_39 {
        width: 7.894rem;
        height: 2.347rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: justify;
        line-height: 0.587rem;
        margin: 0.614rem 0 0.827rem 0.427rem;
      }
    }
    .block_11 {
      position: absolute;
      left: 16.454rem;
      top: 7.44rem;
      width: 8.747rem;
      height: 5.867rem;
      background: url(/pages/assets/img/SketchPng6b362270885d8222b61706716cf82dcf3a783d0d33b67a6402c0c037dbaac3bd.png) -0.027rem -0.027rem
        no-repeat;
      background-size: 8.8rem 5.92rem;
      .text_40 {
        width: 3.84rem;
        height: 0.88rem;
        overflow-wrap: break-word;
        color: rgba(54, 146, 250, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.88rem;
        margin: 0.64rem 0 0 0.427rem;
      }
      .image_8 {
        width: 5.254rem;
        height: 0.027rem;
        margin: 0.507rem 0 0 0.427rem;
      }
      .text_41 {
        width: 7.894rem;
        height: 2.347rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: justify;
        line-height: 0.587rem;
        margin: 0.64rem 0 0.827rem 0.427rem;
      }
    }
    .block_12 {
      position: absolute;
      left: 16.454rem;
      top: 14.294rem;
      width: 8.747rem;
      height: 5.867rem;
      background: url(/pages/assets/img/SketchPng6b362270885d8222b61706716cf82dcf3a783d0d33b67a6402c0c037dbaac3bd.png) -0.027rem -0.027rem
        no-repeat;
      background-size: 8.8rem 5.92rem;
      .text_42 {
        width: 2.56rem;
        height: 0.88rem;
        overflow-wrap: break-word;
        color: rgba(54, 146, 250, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.88rem;
        margin: 0.64rem 0 0 0.427rem;
      }
      .image_9 {
        width: 5.254rem;
        height: 0.027rem;
        margin: 0.534rem 0 0 0.427rem;
      }
      .text_43 {
        width: 7.894rem;
        height: 1.76rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: justify;
        line-height: 0.587rem;
        margin: 0.614rem 0 1.414rem 0.427rem;
      }
    }
    .block_13 {
      position: absolute;
      left: 35.547rem;
      top: 7.44rem;
      width: 8.747rem;
      height: 5.867rem;
      background: url(/pages/assets/img/SketchPng6b362270885d8222b61706716cf82dcf3a783d0d33b67a6402c0c037dbaac3bd.png) -0.027rem -0.027rem
        no-repeat;
      background-size: 8.8rem 5.92rem;
      .text_44 {
        width: 2.56rem;
        height: 0.88rem;
        overflow-wrap: break-word;
        color: rgba(54, 146, 250, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.88rem;
        margin: 0.64rem 0 0 0.427rem;
      }
      .image_10 {
        width: 5.254rem;
        height: 0.027rem;
        margin: 0.507rem 0 0 0.427rem;
      }
      .text_45 {
        width: 7.894rem;
        height: 2.347rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: justify;
        line-height: 0.587rem;
        margin: 0.64rem 0 0.827rem 0.427rem;
      }
    }
    .block_14 {
      position: absolute;
      left: 35.547rem;
      top: 14.294rem;
      width: 8.747rem;
      height: 5.867rem;
      background: url(/pages/assets/img/SketchPng6b362270885d8222b61706716cf82dcf3a783d0d33b67a6402c0c037dbaac3bd.png) -0.027rem -0.027rem
        no-repeat;
      background-size: 8.8rem 5.92rem;
      .text_46 {
        width: 3.84rem;
        height: 0.88rem;
        overflow-wrap: break-word;
        color: rgba(54, 146, 250, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.88rem;
        margin: 0.64rem 0 0 0.427rem;
      }
      .image_11 {
        width: 5.254rem;
        height: 0.027rem;
        margin: 0.534rem 0 0 0.427rem;
      }
      .text_47 {
        width: 7.894rem;
        height: 1.174rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: justify;
        line-height: 0.587rem;
        margin: 0.614rem 0 2rem 0.427rem;
      }
    }
  }
  .box_6 {
    width: 51.2rem;
    .text-group_11 {
      width: 11.52rem;
      height: 2.454rem;
      margin: 2.667rem 0 0 19.84rem;
      .text_48 {
        width: 11.52rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
      }
      .text_49 {
        width: 4.747rem;
        height: 0.534rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.533rem;
        font-weight: NaN;
        text-align: right;
        white-space: nowrap;
        line-height: 0.534rem;
        margin: 0.64rem 0 0 3.387rem;
      }
    }
    .grid_2 {
      width: 39.334rem;
      height: 17.067rem;
      flex-wrap: wrap;
      margin: 1.6rem 0 3.387rem 6.907rem;
      .group_4 {
        width: 18.134rem;
        height: 8rem;
        background: url(/pages/assets/img/SketchPng960b4e8ad89bccae79afc6cf4c865d0e3c40103840576648798c9c87320df7ee.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin: 0 1.067rem 1.067rem 0;
        .text-wrapper_11 {
          padding: 0.8rem;
          .text_50 {
            color: rgba(51, 51, 51, 1);
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
          }
        }
        .section_3 {
          padding: 0 0.8rem;
          .text_51 {
            color: rgba(102, 102, 102, 1);
            font-size: 0.373rem;
            line-height: 0.587rem;
            flex: 1;
          }
          .image_12 {
            width: 3rem;
            height: 3rem;
            margin-left: 0.5rem;
          }
        }
      }
      .group_5 {
        width: 18.134rem;
        height: 8rem;
        background: url(/pages/assets/img/SketchPng960b4e8ad89bccae79afc6cf4c865d0e3c40103840576648798c9c87320df7ee.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin: 0 0 1.067rem 0;
        .text-wrapper_12 {
          padding: 0.8rem;
          .text_52 {
            color: rgba(51, 51, 51, 1);
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
          }
        }
        .group_15 {
          padding: 0 0.8rem;
          .text_53 {
            color: rgba(102, 102, 102, 1);
            font-size: 0.373rem;
            line-height: 0.587rem;
            flex: 1;
          }
          .image_13 {
            width: 3rem;
            height: 3rem;
            margin-left: 0.5rem;
          }
        }
      }
      .group_7 {
        width: 18.134rem;
        height: 8rem;
        background: url(/pages/assets/img/SketchPng960b4e8ad89bccae79afc6cf4c865d0e3c40103840576648798c9c87320df7ee.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin: 0 1.067rem 1.067rem 0;
        .text-wrapper_13 {
          height: 1.44rem;
          background: url(/pages/assets/img/SketchPngf956e0497e03f3b1624b84312c0505517e93db86a26878ddc6c6ae238c66c4b7.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 16rem;
          margin: 0.854rem 0 0 1.067rem;
          .text_54 {
            width: 5.12rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
            margin: 0.4rem 0 0 0.427rem;
          }
        }
        .group_16 {
          width: 16.427rem;
          height: 4rem;
          margin: 1.067rem 0 0.64rem 1.067rem;
          .text_55 {
            width: 12.427rem;
            height: 2.667rem;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.934rem;
          }
          .group_8 {
            border-radius: 8px;
            background-image: url(/pages/assets/img/75460abda7db40ae8da27445ef16e9c4_mergeImage.png);
            background-repeat: no-repeat;
            background-size: cover;
            width: 3.2rem;
            height: 3.2rem;
            margin-top: 0.8rem;
          }
        }
      }
      .group_9 {
        width: 18.134rem;
        height: 8rem;
        background: url(/pages/assets/img/SketchPng960b4e8ad89bccae79afc6cf4c865d0e3c40103840576648798c9c87320df7ee.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin-bottom: 1.067rem;
        .text-wrapper_14 {
          height: 1.44rem;
          background: url(/pages/assets/img/SketchPng82c1901d30abd09f888b668414459a53656f6b6c2b682950ff50eb3b22baed98.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 16rem;
          margin: 0.854rem 0 0 1.067rem;
          .text_56 {
            width: 2.56rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
            margin: 0.4rem 0 0 0.427rem;
          }
        }
        .box_14 {
          width: 16.427rem;
          height: 4rem;
          margin: 1.067rem 0 0.64rem 1.067rem;
          .text_57 {
            width: 12.427rem;
            height: 2.667rem;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.934rem;
          }
          .box_8 {
            border-radius: 8px;
            background-image: url(/pages/assets/img/93ee3770fc0843c085f0bef46c19c8f1_mergeImage.png);
            background-repeat: no-repeat;
            background-size: cover;
            width: 3.2rem;
            height: 3.2rem;
            margin-top: 0.8rem;
          }
        }
      }
    }
  }
  .box_9 {
    background-color: rgba(17, 26, 52, 1);
    width: 51.2rem;
    height: 11.04rem;
    margin-top: 14.88rem;
    .block_16 {
      width: 31.787rem;
      height: 3.44rem;
      margin: 1.36rem 0 0 9.6rem;
      .box_15 {
        width: 7.44rem;
        height: 2.934rem;
        margin-top: 0.507rem;
        .image-text_6 {
          width: 4.374rem;
          height: 0.374rem;
          .thumbnail_5 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_4 {
            width: 3.734rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
        .image-text_7 {
          width: 5.707rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_6 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_5 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
            .text_58 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
            .text_59 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
          }
        }
        .image-text_8 {
          width: 7.44rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_7 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_6 {
            width: 6.8rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
      }
      .image-text_9 {
        width: 3.36rem;
        height: 3.44rem;
        .group_11 {
          border-radius: 8px;
          background-image: url(/pages/assets/img/8eb348c0aa4a408da9f2d0deba2ea7de_mergeImage.png);
          background-repeat: no-repeat;
          background-size: cover;
          width: 2.667rem;
          height: 2.667rem;
          margin-left: 0.347rem;
        }
        .text-group_7 {
          width: 3.36rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
          margin-top: 0.4rem;
        }
      }
    }
    .group_12 {
      width: 51.2rem;
      height: 4.054rem;
      background: url(/pages/assets/img/SketchPng5c8ba53f71bd92f957537da4d03c76390cd3535fdd5fe926d77e0f7c541f1f80.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 1.387rem 0 0.8rem 0;
      .image_14 {
        width: 4.267rem;
        height: 0.64rem;
        margin: 0.854rem 0 0 9.6rem;
      }
      .paragraph_4 {
        width: 7.227rem;
        height: 1.494rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.426rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        line-height: 0.747rem;
        margin: 0.587rem 0 0.48rem 9.6rem;
      }
    }
  }
  .box_11 {
    background-image: url(/pages/assets/img/c5ca74b5af474eccac864e3a11f2c9b6_mergeImage.png);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    min-height: 21.6rem;
    width: 100%;
    max-width: 51.2rem;
    margin: 0 auto;
    position: relative;

    .list_2 {
      width: 100%;
      max-width: 37.334rem;
      height: auto;
      min-height: 14.934rem;
      justify-content: space-between;
      margin: 3.2rem auto 0 auto;
      padding: 0 2rem;
      .list-items_1 {
        width: 11.734rem;
        min-height: 14.934rem;
        height: auto;
      background-size: cover;
        margin-right: 1.067rem;
        flex-shrink: 0;

        &:last-child {
          margin-right: 0;
        }
        .image-wrapper_1 {
          height: 2.667rem;
          background: url(/pages/assets/img/SketchPng74ddbfcbae1722561bec997878c9a9280e703bcb3d137157251fb3beb764dfcc.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.667rem;
          margin: 1.067rem auto 0 auto;
          .label_1 {
            width: 1.227rem;
            height: 1.227rem;
            margin: 0.694rem auto;
          }
        }
        .text_60 {
          width: 100%;
          height: 1.067rem;
          overflow-wrap: break-word;
          font-size: 0.746rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: center;
          white-space: nowrap;
          line-height: 1.067rem;
          margin: 0.854rem 0 0 0;
        }
        .text-wrapper_15 {
          border-radius: 28px;
          height: 1.28rem;
          width: 4.8rem;
          margin: 0.427rem auto 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
          .text_61 {
            width: 100%;
            height: 0.747rem;
            overflow-wrap: break-word;
            font-size: 0.533rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            line-height: 0.747rem;
          }
        }
        .text_62 {
          width: 10.027rem;
          height: 1.547rem;
          overflow-wrap: break-word;
          font-size: 0.426rem;
          font-weight: NaN;
          text-align: left;
          line-height: 0.774rem;
          margin: 0.854rem 0 0 0.854rem;
        }
        .group_13 {
          width: 10.027rem;
          height: 3.867rem;
          margin: 0.587rem 0 0.72rem 0.854rem;
          .image-text_10 {
            width: 5.947rem;
            height: 2.8rem;
            margin: 0.534rem 0 0 0.64rem;
            .image_15 {
              width: 0.614rem;
              height: 2.267rem;
              margin-top: 0.294rem;
            }
            .text-group_8 {
              width: 6.24rem;
              height: 2.8rem;
              overflow-wrap: break-word;
              font-size: 0.48rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              line-height: 0.934rem;
            }
          }
        }
      }
    }
  }
}

// 响应式媒体查询 - 解决不同分辨率下的错位问题
@media screen and (max-width: 1200px) {
  .page {
    .box_11 {
      .list_2 {
        padding: 0 1rem;

        .list-items_1 {
          width: calc(33.333% - 0.711rem);
          margin-right: 1.067rem;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .page {
    .box_11 {
      padding: 0 1rem;

      .list_2 {
        flex-direction: column;
        align-items: center;
        padding: 0;

        .list-items_1 {
          width: 100%;
          max-width: 20rem;
          margin-right: 0;
          margin-bottom: 2rem;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

@media screen and (min-width: 1400px) {
  .page {
    .box_11 {
      .list_2 {
        padding: 0 4rem;
      }
    }
  }
}
