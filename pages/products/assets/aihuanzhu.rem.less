.page {
  background-color: rgba(243, 245, 249, 1);
  position: relative;
  width: 51.2rem;
  overflow: hidden;
  padding-top: 2.187rem; // 为顶部导航栏留出空间
  .group_22 {
    position: relative;
    width: 51.2rem;
    height: 115.68rem;
    .box_6 {
      background-image: url(/pages/assets/img/9b1ebdcfc3744cb9a0f27ba120c5a40f_mergeImage.png);
      background-repeat: no-repeat;
      background-size: cover;
      width: 51.2rem;
      height: 12.8rem;
      .image_2 {
        width: 51.2rem;
        height: 0.027rem;
      }
      .text-wrapper_1 {
        width: 17.254rem;
        height: 5.92rem;
        overflow-wrap: break-word;
        font-size: 0;
        text-transform: uppercase;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        line-height: 1.974rem;
        margin: 2.64rem 0 4.214rem 6.96rem;
        .text_7 {
          width: 17.254rem;
          height: 5.92rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 1.066rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.974rem;
        }
        .paragraph_1 {
          width: 17.254rem;
          height: 5.92rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 1.333rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.974rem;
        }
        .paragraph_2 {
          width: 17.254rem;
          height: 5.92rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 1.333rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.974rem;
        }
      }
    }
    .box_7 {
      background-color: rgba(255, 255, 255, 1);
      position: relative;
      width: 51.2rem;
      height: 22.027rem;
              .group_1 {
        width: 13.307rem;
        height: 1.867rem;
        background: url(/pages/assets/img/SketchPngaed1f01b3562df462a7ddb4de25d22f4a9f15653f937bde966abb5fca1717500.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin: 1.334rem 0 0 18.96rem;
        .text-wrapper_2 {
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          border-radius: 26px;
          height: 1.227rem;
          width: 3.174rem;
          margin: 0.32rem 0 0 0.427rem;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);

            .text_8 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          &.active {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);

            .text_8 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          .text_8 {
            width: 2.107rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
                  .text-wrapper_3 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.774rem;
          margin: 0.32rem 0 0 0.32rem;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 26px;

          &:hover {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);

            .text_9 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          &.active {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);

            .text_9 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          .text_9 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
        .text-wrapper_4 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.774rem;
          margin: 0.32rem 0 0 0.32rem;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 26px;

          &:hover {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);

            .text_10 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          &.active {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);

            .text_10 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          .text_10 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
        .text-wrapper_5 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.774rem;
          margin: 0.32rem 0.427rem 0 0.32rem;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 26px;

          &:hover {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);

            .text_11 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          &.active {
            background-color: rgba(33, 135, 250, 1);
            background-image: none;
            transform: scale(1.05);

            .text_11 {
              color: rgba(255, 255, 255, 1);
              font-weight: 600;
            }
          }

          .text_11 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
      }
      .text-group_21 {
        width: 23.014rem;
        height: 2.507rem;
        margin: 2.667rem 0 0 14.08rem;
        .text_12 {
          width: 6.347rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 1.28rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          white-space: nowrap;
          line-height: 1.28rem;
          margin-left: 8.347rem;
        }
        .text_13 {
          width: 23.014rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.426rem;
          text-transform: uppercase;
          font-weight: NaN;
          text-align: right;
          white-space: nowrap;
          line-height: 0.587rem;
          margin-top: 0.64rem;
        }
      }
      .group_23 {
        width: 34.667rem;
        height: 10.134rem;
        margin: 1.654rem 0 1.867rem 8.24rem;
        .group_3 {
          width: 10.667rem;
          height: 10.134rem;
          background: url(/pages/assets/img/SketchPngd5650d9d0491a64e16de52973ad77ef41506cc1a793318c9eba26759eb7707fc.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text_14 {
            width: 1.707rem;
            height: 1.2rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.853rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: right;
            white-space: nowrap;
            line-height: 1.2rem;
            margin: 1.6rem 0 0 0.934rem;
          }
          .group_4 {
            background-color: rgba(33, 135, 250, 1);
            width: 2.134rem;
            height: 0.08rem;
            margin: 0.534rem 0 0 0.934rem;
          }
          .text-wrapper_6 {
            width: 8.8rem;
            height: 3.2rem;
            overflow-wrap: break-word;
            font-size: 0;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: justify;
            line-height: 1.067rem;
            margin: 1.6rem 0 1.92rem 0.934rem;
            .text_15 {
              width: 8.8rem;
              height: 3.2rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
            .text_16 {
              width: 8.8rem;
              height: 3.2rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
          }
        }
        .group_5 {
          width: 10.667rem;
          height: 10.134rem;
          background: url(/pages/assets/img/SketchPngc01b4195d6d782d95e9700c0c765810b6c86f6fe526b119551f3162fc8db7273.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 1.334rem;
          .text_17 {
            width: 1.707rem;
            height: 1.2rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.853rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: right;
            white-space: nowrap;
            line-height: 1.2rem;
            margin: 1.6rem 0 0 0.934rem;
          }
          .group_6 {
            background-color: rgba(33, 135, 250, 1);
            width: 2.134rem;
            height: 0.08rem;
            margin: 0.534rem 0 0 0.934rem;
          }
          .text-wrapper_7 {
            width: 8.8rem;
            height: 3.2rem;
            overflow-wrap: break-word;
            font-size: 0;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: justify;
            line-height: 1.067rem;
            margin: 1.6rem 0 1.92rem 0.934rem;
            .text_18 {
              width: 8.8rem;
              height: 3.2rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
            .text_19 {
              width: 8.8rem;
              height: 3.2rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
          }
        }
        .group_7 {
          width: 10.667rem;
          height: 10.134rem;
          background: url(/pages/assets/img/SketchPng328dd67769ca441debd7f4db7e013d4cd23478543b3016c96537170743969a40.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 1.334rem;
          .text_20 {
            width: 1.707rem;
            height: 1.2rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.853rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: right;
            white-space: nowrap;
            line-height: 1.2rem;
            margin: 1.6rem 0 0 0.934rem;
          }
          .group_8 {
            background-color: rgba(33, 135, 250, 1);
            width: 2.134rem;
            height: 0.08rem;
            margin: 0.534rem 0 0 0.934rem;
          }
          .text-wrapper_8 {
            width: 8.8rem;
            height: 3.2rem;
            overflow-wrap: break-word;
            font-size: 0;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: justify;
            line-height: 1.067rem;
            margin: 1.6rem 0 1.92rem 0.934rem;
            .text_21 {
              width: 8.8rem;
              height: 3.2rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
            .text_22 {
              width: 8.8rem;
              height: 3.2rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.48rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 1.067rem;
            }
          }
        }
      }
      .text-wrapper_9 {
        height: 5.867rem;
        background: url(/pages/assets/img/SketchPng0f90dfe39f18209f2a52c759e9bc2ab9c22f79375c501c260660074e2c89d93d.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 37.334rem;
        position: absolute;
        left: 6.907rem;
        top: 18.96rem;
        .paragraph_3 {
          width: 27.734rem;
          height: 2.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.693rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          line-height: 1.147rem;
          margin: 2.187rem 0 0 4.8rem;
        }
      }
    }
    .box_8 {
      width: 51.2rem;
      height: 22.267rem;
      margin-top: 2.8rem;
      .text_23 {
        width: 5.12rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
        margin: 2.667rem 0 0 23.04rem;
      }
      .text-wrapper_26 {
        width: 22.934rem;
        height: 1.6rem;
        margin: 1.547rem 0 0 14.134rem;
                                .paragraph_4, .paragraph_5, .paragraph_6, .paragraph_7, .paragraph_8 {
          width: auto;
          min-width: 2.88rem;
          height: 1.6rem;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 0.8rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          padding: 0.2rem 0.4rem;

          &:not(:first-child) {
            margin-left: 2.134rem;
          }

          &.active {
            color: rgba(33, 135, 250, 1);
            font-weight: 600;
          }

          &:hover {
            color: rgba(33, 135, 250, 1);
          }
        }

      }
      .section_1 {
        display: none;
      }
      .box_21 {
        width: 37.334rem;
        height: 10.667rem;
        margin: 1.6rem 0 2.4rem 6.907rem;
        .box_9 {
          background-color: rgba(255, 255, 255, 1);
          width: 18.694rem;
          height: 10.667rem;
          .text-wrapper_11 {
            width: 8.96rem;
            height: 0.854rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.854rem;
            margin: 1.334rem 0 0 1.867rem;
            .text_24 {
              width: 8.96rem;
              height: 0.854rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.853rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.854rem;
            }
            .text_25 {
              width: 8.96rem;
              height: 0.854rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.853rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.854rem;
            }
          }
          .image_3 {
            width: 11.84rem;
            height: 0.027rem;
            margin: 1.067rem 0 0 1.867rem;
          }
          .paragraph_9 {
            width: 10.454rem;
            height: 4.827rem;
            overflow-wrap: break-word;
            color: rgba(68, 68, 68, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            line-height: 0.907rem;
            margin: 1.067rem 0 1.494rem 1.867rem;
          }
        }
        .box_10 {
          width: 18.64rem;
          height: 10.667rem;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
        }


      }

      // 为不同功能模块设置背景图片
      .smart-triage .box_10 {
        background-image: url(/pages/assets/img/d8e7f4811ed5467bb7f2a2db03454f68_mergeImage.png);
      }

      .ai-self-check .box_10 {
        background-image: url(/pages/assets/img/509010d512234eb88c5ea69f36e75fb5_mergeImage.png);
      }

      .precise-guide .box_10 {
        background-image: url(/pages/assets/img/b08597492e534371b3fbc930e538619d_mergeImage.png);
      }

      .smart-pre-diagnosis .box_10 {
        background-image: url(/pages/assets/img/9f73670c01d546f9af7af4b2f95ef9e4_mergeImage.png);
      }

      .full-follow-up .box_10 {
        background-image: url(/pages/assets/img/21ed6cbe326e48439fdf324ec87d8141_mergeImage.png);
      }
    }
    .box_11 {
      background-color: rgba(255, 255, 255, 1);
      height: 24.534rem;
      width: 51.2rem;
      position: relative;
      justify-content: flex-center;
      margin: 30.987rem 0 0.267rem 0;
      .text-wrapper_27 {
        width: 5.12rem;
        height: 1.28rem;
        margin: 2.667rem 0 0 23.04rem;
        .text_26 {
          width: 5.12rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 1.28rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          white-space: nowrap;
          line-height: 1.28rem;
        }
      }
      .section_9 {
        width: 37.334rem;
        height: 15.227rem;
        margin: 2.667rem 0 2.694rem 6.907rem;
        .group_10 {
          width: 8.8rem;
          height: 15.227rem;
          background: url(/pages/assets/img/SketchPng66a0ec8be2e0416cb259a3c0c0455698a9e13a1ff664b076d9b7c6e9ccc0af7b.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_22 {
            width: 4.267rem;
            height: 3.414rem;
            margin: 1.067rem 0 0 1.067rem;
            .text_27 {
              width: 2.214rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.746rem;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .paragraph_10 {
              width: 4.267rem;
              height: 1.707rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.533rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 0.854rem;
              margin-top: 0.64rem;
            }
          }
          .box_12 {
            background-color: rgba(33, 135, 250, 1);
            width: 6.667rem;
            height: 2.134rem;
            margin: 1.067rem 0 0 1.067rem;
            .box_13 {
              width: 1.387rem;
              height: 0.64rem;
              background: url(/pages/assets/img/SketchPng52525bf7010f048888ae7b887f4c649ab2257a4619f627014ce736aceeac0873.png)
                100% no-repeat;
              background-size: 100% 100%;
              margin: 0.747rem 0 0 0.747rem;
            }
            .text-wrapper_13 {
              width: 2.56rem;
              height: 1.467rem;
              overflow-wrap: break-word;
              font-size: 0;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              line-height: 0.587rem;
              margin: 0.347rem 1.174rem 0 0.8rem;
              .text_28 {
                width: 2.56rem;
                height: 1.467rem;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.426rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                line-height: 0.587rem;
              }
              .paragraph_11 {
                width: 2.56rem;
                height: 1.467rem;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.64rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                line-height: 0.587rem;
              }
            }
          }
          .box_14 {
            background-color: rgba(255, 255, 255, 0.1);
            height: 2.134rem;
            width: 6.667rem;
            margin: 0.854rem 0 0 1.067rem;
            .text-wrapper_14 {
              width: 2.56rem;
              height: 1.467rem;
              overflow-wrap: break-word;
              font-size: 0;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 0.587rem;
              margin: 0.347rem 0 0 2.934rem;
              .text_29 {
                width: 2.56rem;
                height: 1.467rem;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.426rem;
                text-transform: uppercase;
                font-weight: NaN;
                text-align: left;
                line-height: 0.587rem;
              }
              .paragraph_12 {
                width: 2.56rem;
                height: 1.467rem;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.64rem;
                text-transform: uppercase;
                font-weight: NaN;
                text-align: left;
                line-height: 0.587rem;
              }
            }
          }
          .box_15 {
            background-color: rgba(255, 255, 255, 0.1);
            height: 2.134rem;
            width: 6.667rem;
            margin: 0.854rem 0 1.574rem 1.067rem;
            .text-wrapper_15 {
              width: 2.56rem;
              height: 1.467rem;
              overflow-wrap: break-word;
              font-size: 0;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              line-height: 0.587rem;
              margin: 0.347rem 0 0 2.934rem;
              .text_30 {
                width: 2.56rem;
                height: 1.467rem;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.426rem;
                text-transform: uppercase;
                font-weight: NaN;
                text-align: left;
                line-height: 0.587rem;
              }
              .paragraph_13 {
                width: 2.56rem;
                height: 1.467rem;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.64rem;
                text-transform: uppercase;
                font-weight: NaN;
                text-align: left;
                line-height: 0.587rem;
              }
            }
          }
        }
        .group_11 {
          background-image: url(/pages/assets/img/7434f474a18348f787e8c23142223592_mergeImage.png);
          background-repeat: no-repeat;
          background-size: cover;
          width: 28.534rem;
          height: 15.227rem;
        }
      }
      .image-wrapper_1 {
        background-color: rgba(0, 0, 0, 0.4);
        height: 15.254rem;
        width: 28.587rem;
        position: absolute;
        left: 15.707rem;
        top: 6.614rem;
        .image_4 {
          width: 2.667rem;
          height: 2.667rem;
          margin: 6.294rem 0 0 12.934rem;
        }
      }
    }
    .box_16 {
      position: absolute;
      left: 0;
      top: 59.84rem;
      width: 51.2rem;
      height: 25.334rem;
      background: url(/pages/assets/img/SketchPngd3b69e3f5bdef24cee0d6c1d6f709fd5f68429e7098d45a70e30e861ba594c79.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text_31 {
        width: 5.12rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
        margin: 2.667rem 0 0 23.014rem;
      }
      .grid_2 {
        width: 39.334rem;
        height: 17.387rem;
        flex-wrap: wrap;
        margin: 2.134rem 0 1.867rem 6.907rem;
        .group_12 {
          width: 18.134rem;
          height: 8.16rem;
          background: url(/pages/assets/img/SketchPngfec43d850dfd393ae6e0a8324600f57bae756821ecf1524b3027b55cf5d8fe2a.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin: 0 1.067rem 1.067rem 0;
          .section_3 {
            width: 16rem;
            height: 1.44rem;
            background: url(/pages/assets/img/SketchPng1ba97e6ff3801777ce092b82ad366a026b0e39f160a373a90f36b32343c89b8c.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin: 0.854rem 0 0 1.067rem;
            .text_32 {
              width: 4.08rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
              margin: 0.4rem 0 0 0.32rem;
            }
            .image-text_12 {
              width: 5.014rem;
              height: 0.694rem;
              margin: 0.374rem 5.12rem 0 1.467rem;
              .label_1 {
                width: 0.747rem;
                height: 0.694rem;
              }
              .text-group_3 {
                width: 3.84rem;
                height: 0.667rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.48rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.667rem;
                margin-top: 0.027rem;
              }
            }
          }
          .block_3 {
            width: 16.534rem;
            height: 4.267rem;
            margin: 1.067rem 0 0.534rem 1.067rem;
            .image-text_13 {
              width: 7.547rem;
              height: 3.04rem;
              .image_5 {
                width: 0.214rem;
                height: 2.187rem;
                margin-top: 0.427rem;
              }
              .text-group_4 {
                width: 6.907rem;
                height: 3.04rem;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: left;
                line-height: 1.014rem;
              }
            }
            .image_6 {
              width: 3.734rem;
              height: 3.734rem;
              margin-top: 0.534rem;
            }
          }
        }
        .group_13 {
          width: 18.134rem;
          height: 8.16rem;
          background: url(/pages/assets/img/SketchPngfec43d850dfd393ae6e0a8324600f57bae756821ecf1524b3027b55cf5d8fe2a.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-bottom: 1.067rem;
          .group_14 {
            width: 16rem;
            height: 1.44rem;
            background: url(/pages/assets/img/SketchPng1ba97e6ff3801777ce092b82ad366a026b0e39f160a373a90f36b32343c89b8c.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin: 0.854rem 0 0 1.067rem;
            .text_33 {
              width: 4.187rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
              margin: 0.4rem 0 0 0.32rem;
            }
            .image-text_14 {
              width: 6.454rem;
              height: 0.694rem;
              margin: 0.374rem 3.68rem 0 1.36rem;
              .label_2 {
                width: 0.747rem;
                height: 0.694rem;
              }
              .text-group_5 {
                width: 5.28rem;
                height: 0.667rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.48rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.667rem;
                margin-top: 0.027rem;
              }
            }
          }
          .box_22 {
            width: 16.374rem;
            height: 4.267rem;
            margin: 1.067rem 0 0.534rem 1.067rem;
            .image-text_15 {
              width: 8.48rem;
              height: 3.04rem;
              .image_7 {
                width: 0.214rem;
                height: 2.187rem;
                margin-top: 0.427rem;
              }
              .text-group_6 {
                width: 7.84rem;
                height: 3.04rem;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: left;
                line-height: 1.014rem;
              }
            }
            .image_8 {
              width: 3.387rem;
              height: 3.734rem;
              margin-top: 0.534rem;
            }
          }
        }
        .group_16 {
          width: 18.134rem;
          height: 8.16rem;
          background: url(/pages/assets/img/SketchPngfec43d850dfd393ae6e0a8324600f57bae756821ecf1524b3027b55cf5d8fe2a.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin: 0 1.067rem 1.067rem 0;
          .text-wrapper_16 {
            height: 1.44rem;
            background: url(/pages/assets/img/SketchPng7c03bf2a0841e7bb78b8bb09b9aeaa05a5328ec940b5033476d16d758cb73564.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 16rem;
            margin: 0.854rem 0 0 1.067rem;
            .text_34 {
              width: 6.534rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
              margin: 0.4rem 0 0 0.32rem;
            }
          }
          .group_24 {
            width: 16.534rem;
            height: 4.267rem;
            margin: 1.067rem 0 0.534rem 1.067rem;
            .image-text_16 {
              width: 9.494rem;
              height: 3.04rem;
              .image_9 {
                width: 0.214rem;
                height: 2.187rem;
                margin-top: 0.427rem;
              }
              .text-group_7 {
                width: 8.854rem;
                height: 3.04rem;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: left;
                line-height: 1.014rem;
              }
            }
            .group_17 {
              border-radius: 8px;
              background-image: url(/pages/assets/img/0bf83a134c3e4e2bb6974a8e9891b4ca_mergeImage.png);
              background-repeat: no-repeat;
              background-size: cover;
              width: 3.734rem;
              height: 3.734rem;
              margin-top: 0.534rem;
            }
          }
        }
        .group_18 {
          width: 18.134rem;
          height: 8.16rem;
          background: url(/pages/assets/img/SketchPngfec43d850dfd393ae6e0a8324600f57bae756821ecf1524b3027b55cf5d8fe2a.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-bottom: 1.067rem;
          .section_5 {
            width: 16rem;
            height: 1.44rem;
            background: url(/pages/assets/img/SketchPng0f4cb3e384b65c013c63ded35b5d8550e1e330a5a721510c916413f9579a5a0b.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin: 0.854rem 0 0 1.067rem;
            .text_35 {
              width: 5.894rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
              margin: 0.4rem 0 0 0.32rem;
            }
            .image-text_17 {
              width: 5.12rem;
              height: 0.694rem;
              margin: 0.374rem 3.307rem 0 1.36rem;
              .label_3 {
                width: 0.747rem;
                height: 0.694rem;
              }
              .text-group_8 {
                width: 3.947rem;
                height: 0.667rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.48rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.667rem;
                margin-top: 0.027rem;
              }
            }
          }
          .box_23 {
            width: 4.054rem;
            height: 0.8rem;
            margin: 0.534rem 0 0 1.067rem;
            .text-wrapper_17 {
              height: 0.8rem;
              background: url(/pages/assets/img/SketchPngf81be4cdd4f448e19ae5196598712c1f4692978e3453ccd39bca7919cb2e6930.png)
                100% no-repeat;
              background-size: 100% 100%;
              width: 1.92rem;
              .text_36 {
                width: 1.494rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.214rem 0 0 0.214rem;
              }
            }
            .text-wrapper_18 {
              height: 0.8rem;
              background: url(/pages/assets/img/SketchPngdfdd8d1f926ce658e351fc2b3b93bca0478821aa3859fbbba2d60909176ca1ab.png)
                100% no-repeat;
              background-size: 100% 100%;
              width: 1.92rem;
              .text_37 {
                width: 1.494rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.214rem 0 0 0.214rem;
              }
            }
          }
          .box_24 {
            width: 16.534rem;
            height: 3.734rem;
            margin: 0.267rem 0 0.534rem 1.067rem;
            .image-text_18 {
              width: 12.214rem;
              height: 3.04rem;
              margin-top: 0.16rem;
              .image_10 {
                width: 0.214rem;
                height: 2.187rem;
                margin-top: 0.427rem;
              }
              .text-group_9 {
                width: 11.574rem;
                height: 3.04rem;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: left;
                line-height: 1.014rem;
              }
            }
            .box_18 {
              background-image: url(/pages/assets/img/a510dd66899945c18b57b987bb2db643_mergeImage.png);
              background-repeat: no-repeat;
              background-size: cover;
              width: 3.734rem;
              height: 3.734rem;
            }
          }
        }
      }
    }
    .box_19 {
      background-color: rgba(36, 55, 76, 1);
      position: absolute;
      left: -0.026rem;
      top: 85.174rem;
      width: 51.2rem;
      height: 5.707rem;
      .text-group_23 {
        width: 3.2rem;
        height: 4.054rem;
        margin: 0.827rem 0 0 6.934rem;
        .text-wrapper_19 {
          width: 2.454rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 0.48rem;
          .text_38 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_39 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .paragraph_14 {
          width: 3.2rem;
          height: 1.494rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: right;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
      .text-group_24 {
        width: 2.667rem;
        height: 4.054rem;
        margin: 0.827rem 0 0 2.64rem;
        .text-wrapper_20 {
          width: 2.454rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 0.214rem;
          .text_40 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_41 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .paragraph_15 {
          width: 2.667rem;
          height: 1.494rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: center;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
      .text-group_25 {
        width: 3.2rem;
        height: 4.054rem;
        margin: 0.827rem 0 0 2.667rem;
        .text-wrapper_21 {
          width: 2.454rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 0.48rem;
          .text_42 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_43 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .paragraph_16 {
          width: 3.2rem;
          height: 1.494rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: center;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
      .text-group_26 {
        width: 2.667rem;
        height: 4.054rem;
        margin: 0.827rem 0 0 2.64rem;
        .text-wrapper_22 {
          width: 2.454rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 0.214rem;
          .text_44 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_45 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .paragraph_17 {
          width: 2.667rem;
          height: 1.494rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: center;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
      .text-group_27 {
        width: 3.734rem;
        height: 4.054rem;
        margin: 0.827rem 0 0 2.64rem;
        .text-wrapper_23 {
          width: 2.454rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 0.747rem;
          .text_46 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_47 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .paragraph_18 {
          width: 3.734rem;
          height: 1.494rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: center;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
      .text-group_28 {
        width: 3.307rem;
        height: 4.054rem;
        margin: 0.827rem 0 0 2.667rem;
        .text-wrapper_24 {
          width: 2.454rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 0.534rem;
          .text_48 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_49 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .paragraph_19 {
          width: 3.307rem;
          height: 1.494rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: center;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
      .text-group_29 {
        width: 2.667rem;
        height: 4.054rem;
        margin: 0.827rem 6.934rem 0 2.64rem;
        .text-wrapper_25 {
          width: 2.454rem;
          height: 2.24rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 2.24rem;
          margin-left: 0.214rem;
          .text_50 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 1.6rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 2.24rem;
          }
          .text_51 {
            width: 2.454rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 2.24rem;
          }
        }
        .paragraph_20 {
          width: 2.667rem;
          height: 1.494rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.533rem;
          font-weight: NaN;
          text-align: center;
          line-height: 0.747rem;
          margin-top: 0.32rem;
        }
      }
    }
  }
  .box_20 {
    background-color: rgba(17, 26, 52, 1);
    width: 51.2rem;
    height: 11.04rem;
    margin: -0.027rem 0 0.24rem 0;
    .group_25 {
      width: 31.787rem;
      height: 3.44rem;
      margin: 1.36rem 0 0 9.6rem;
      .group_26 {
        width: 7.44rem;
        height: 2.934rem;
        margin-top: 0.507rem;
        .image-text_19 {
          width: 4.374rem;
          height: 0.374rem;
          .thumbnail_5 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_17 {
            width: 3.734rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
        .image-text_20 {
          width: 5.707rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_6 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_18 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
            .text_52 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
            .text_53 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
          }
        }
        .image-text_21 {
          width: 7.44rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_7 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_19 {
            width: 6.8rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
      }
      .image-text_22 {
        width: 3.36rem;
        height: 3.44rem;
        .section_8 {
          border-radius: 8px;
          background-image: url(/pages/assets/img/b545a63873144462b5f2d2e10eb5499d_mergeImage.png);
          background-repeat: no-repeat;
          background-size: cover;
          width: 2.667rem;
          height: 2.667rem;
          margin-left: 0.347rem;
        }
        .text-group_20 {
          width: 3.36rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
          margin-top: 0.4rem;
        }
      }
    }
    .block_2 {
      width: 51.2rem;
      height: 4.054rem;
      background: url(/pages/assets/img/SketchPng5c8ba53f71bd92f957537da4d03c76390cd3535fdd5fe926d77e0f7c541f1f80.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 1.387rem 0 0.8rem 0;
      .image_11 {
        width: 4.267rem;
        height: 0.64rem;
        margin: 0.854rem 0 0 9.6rem;
      }
      .paragraph_21 {
        width: 7.227rem;
        height: 1.494rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.426rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        line-height: 0.747rem;
        margin: 0.587rem 0 0.48rem 9.6rem;
      }
    }
  }
}

// 导航按钮活动状态样式
.text-wrapper_2.active,
.text-wrapper_3.active,
.text-wrapper_4.active,
.text-wrapper_5.active {
  background-color: rgba(33, 135, 250, 1) !important;
  background-image: none !important;

  span {
    color: rgba(255, 255, 255, 1) !important;
    font-family: PingFangSC-Semibold !important;
    font-weight: 600 !important;
  }
}

// 悬停效果
.text-wrapper_2:hover,
.text-wrapper_3:hover,
.text-wrapper_4:hover,
.text-wrapper_5:hover {
  background-color: rgba(33, 135, 250, 0.8);

  span {
    color: rgba(255, 255, 255, 1);
  }
}


