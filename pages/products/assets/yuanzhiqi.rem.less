.page {
  background-color: rgba(243, 245, 249, 1);
  position: relative;
  width: 51.2rem;
  overflow: hidden;
  padding-top: 2.187rem;
  scroll-behavior: smooth;

  // 为锚点目标添加滚动偏移，避免被顶部导航遮挡
  #about,
  #advantages,
  #capabilities,
  #applications {
    scroll-margin-top: 2rem;
  }

  // 产品能力导航切换样式，统一所有选项卡的处理方式
  .box_19 {
    width: 4.854rem;
    height: 12.267rem;
    margin-top: 0.427rem;

    .section_4,
    .section_6,
    .section_7,
    .section_8,
    .section_9,
    .section_10 {
      width: 4.854rem;
      height: 1.6rem;
      background: url(/pages/assets/img/SketchPng580eae31f82f9b44663a87dfd6b299613e2ba55556358724ca47044475f2e32b.png)
        100% no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
      margin-top: 0.534rem;

      .section_5 {
        width: 0.107rem;
        height: 1.6rem;
        background-color: transparent;
      }

      .image-text_13,
      .image-text_14,
      .image-text_15,
      .image-text_16,
      .image-text_17,
      .image-text_18 {
        width: 2.614rem;
        height: 0.64rem;
        margin: 0.48rem 0 0 0.64rem;
        display: flex;
        align-items: center;

        .label_1,
        .label_2,
        .label_3,
        .label_4,
        .label_5,
        .label_6 {
          width: 0.64rem;
          height: 0.64rem;
        }

        .text-group_10,
        .text-group_11,
        .text-group_12,
        .text-group_13,
        .text-group_14,
        .text-group_15 {
          width: 1.44rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: 400;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
          margin: 0.08rem 0 0 0.534rem;
        }
      }

      &.active {
        background: url(/pages/assets/img/SketchPng12db6e589961dc6b6f3a429bef3639147b056b286b26cc2c65f6fbb8b7203ed0.png)
          100% no-repeat;
        background-size: 100% 100%;

        .section_5 {
          background-color: rgba(33, 135, 250, 1);
        }

        .text-group_10,
        .text-group_11,
        .text-group_12,
        .text-group_13,
        .text-group_14,
        .text-group_15 {
          color: rgba(33, 135, 250, 1);
          font-weight: 600;
        }
      }
    }

    // 第一个选项卡取消上边距
    .section_4 {
      margin-top: 0;
    }
  }

  // 新增样式类
  .demo-image-container {
    position: relative;
    border-radius: 8px;
    margin: 1rem;
    box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
    border: 1px solid rgba(227, 227, 227, 1);

    .demo-image {
      width: 100%;
      height: auto;
      border-radius: 8px;
    }

    .demo-overlay {
      position: absolute;
      bottom: 1rem;
      right: 1rem;

      .demo-overlay-image {
        border-radius: 8px;
        box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
        border: 1px solid rgba(227, 227, 227, 1);
      }
    }
  }

  .file-types-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem;
    padding: 0.5rem;
    background-color: rgba(243, 245, 249, 1);
    border-radius: 8px;

    .file-icon {
      width: 0.64rem;
      height: 0.72rem;
    }

    .file-types {
      display: flex;
      gap: 0.48rem;
      align-items: center;
      margin: 1.6rem 0 0.747rem 1.387rem;

      .file-icon {
        width: 0.64rem;
        height: 0.72rem;
      }

      .file-type {
        padding: 0.267rem 0.187rem;
        background-color: rgba(33, 135, 250, 1);
        color: white;
        border-radius: 4px;
        font-size: 0.266rem;
        text-transform: uppercase;
        width: 0.64rem;
        height: 0.72rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .knowledge-content {
    .content-right {
      .demo-container {
        position: relative;
        margin: 0.347rem 0.4rem 0 0.294rem;
        width: 17.867rem;
        height: 13.174rem;

        .demo-image {
          width: 14.134rem;
          height: 8.214rem;
          border-radius: 8px;
          box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
          border: 1px solid rgba(227, 227, 227, 1);
          margin: 1.04rem 0 0 1.014rem;
        }

        .file-types {
          position: absolute;
          left: 1.387rem;
          top: 9.6rem;
          width: 7.654rem;
          height: 1.574rem;
          background: url(/pages/assets/img/SketchPngcf5b3391c136a22f6c85142e2cb16917f1ab7f64d8e2ebded890d70213fa1cd1.png)
            100% no-repeat;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          gap: 0.48rem;
          padding-left: 1.12rem;

          .file-icon {
            width: 0.64rem;
            height: 0.72rem;
          }

          .file-type {
            width: 0.64rem;
            height: 0.72rem;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.266rem;
            text-transform: uppercase;
            color: white;

            &:nth-child(2) {
              background: url(/pages/assets/img/SketchPng2be2afc964d43f4099897ff316aeb08d25df042d4d3cc9d62d310a8d40312a0e.png)
                100% no-repeat;
              background-size: 100% 100%;
            }

            &:nth-child(3) {
              background: url(/pages/assets/img/SketchPng9e825320ca619d07b50a8069deb38a8a7f7f92f8c10f047953eb87e80468f1ba.png)
                100% no-repeat;
              background-size: 100% 100%;
            }

            &:nth-child(4) {
              background: url(/pages/assets/img/SketchPngac305cb2af0cfdf2d6f6a0bc00884393c15c1a387f973ea0fef00f9c62053083.png)
                100% no-repeat;
              background-size: 100% 100%;
            }

            &:nth-child(5) {
              background: url(/pages/assets/img/SketchPng68e2ca2dcc2e991aaaf236064d08c32f6b9c1c08878d6b0a4d3a8d71d5247ae6.png)
                100% no-repeat;
              background-size: 100% 100%;
            }
          }

          .import-label {
            background-color: rgba(33, 135, 250, 1);
            color: white;
            padding: 0.08rem 0.32rem;
            border-radius: 9px 100px 100px 0px;
            font-size: 0.32rem;
            position: absolute;
            left: 0.453rem;
            top: 1.547rem;
            width: 1.68rem;
            height: 0.614rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  .plugin-demo-container {
    position: relative;
    margin: 1rem;

    .plugin-demo-main {
      width: 100%;
      border-radius: 5px;
      box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
      border: 1px solid rgba(227, 227, 227, 1);
    }

    .plugin-demo-overlay {
      position: absolute;
      top: 2rem;
      left: -0.5rem;
      border-radius: 5px;
      box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
      border: 0.5px solid rgba(33, 135, 250, 1);
    }

    .plugin-demo-small {
      position: absolute;
      bottom: 1rem;
      right: 1rem;
      border-radius: 5px;
      box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
      border: 0.5px solid rgba(33, 135, 250, 1);
    }
  }

  .workflow-demo-container {
    margin: 1rem;

    .workflow-demo {
      width: 100%;
      border-radius: 8px;
      box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
      border: 1px solid rgba(227, 227, 227, 1);
    }
  }

  .channel-demo-container {
    position: relative;
    margin: 1rem;

    .channel-icons {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;

      .channel-icon {
        width: 1.174rem;
        height: 1.174rem;
      }
    }

    .channel-demo-main {
      position: relative;

      .channel-main-image {
        width: 100%;
        border-radius: 8px;
        box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
        border: 1px solid rgba(227, 227, 227, 1);
      }

      .channel-chat-demo {
        position: absolute;
        left: -0.773rem;
        top: 3rem;
        background-color: white;
        border-radius: 5px;
        box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
        border: 0.5px solid rgba(33, 135, 250, 1);
        padding: 0.5rem;
        width: 3.254rem;

        .chat-icon {
          width: 1.174rem;
          height: 1.174rem;
          margin-bottom: 0.5rem;
        }

        .chat-content {
          .chat-header {
            .chat-title {
              font-size: 0.133rem;
              color: white;
              background-color: rgba(63, 116, 246, 1);
              padding: 0.08rem 0.16rem;
              display: block;
            }

            .chat-subtitle {
              font-size: 0.08rem;
              color: white;
              background-color: rgba(63, 116, 246, 1);
              padding: 0 0.4rem 0.054rem;
              display: block;
            }
          }

          .chat-brand {
            background-color: rgba(69, 120, 255, 1);
            border-radius: 6px;
            color: white;
            font-size: 0.08rem;
            padding: 0.107rem 0.027rem;
            margin-top: 0.667rem;
            width: 0.32rem;
            text-align: center;
          }
        }
      }
    }

    .channel-side-demo {
      position: absolute;
      right: -1.84rem;
      bottom: 0;

      .side-demo-image {
        background-color: white;
        border-radius: 5px;
        box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
        border: 0.5px solid rgba(33, 135, 250, 1);
        width: 3.254rem;
      }
    }
  }

  .separator-line {
    width: 8.534rem;
    height: 0.027rem;
    margin: 0.8rem 0;
  }
  .box_1 {
    background-color: rgba(255, 255, 255, 1);
    height: 2.187rem;
    width: 51.2rem;
    .block_9 {
      width: 40.534rem;
      height: 0.64rem;
      margin: 0.774rem 0 0 5.334rem;
      .image_1 {
        width: 4.267rem;
        height: 0.64rem;
      }
      .text_1 {
        width: 0.907rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 15.867rem;
      }
      .text_2 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 2.08rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.08rem 0 0 0.107rem;
      }
      .text_3 {
        width: 3.174rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_4 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_5 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_6 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
    }
    .block_10 {
      width: 1.814rem;
      height: 0.08rem;
      margin: 0.24rem 0 0.454rem 28.427rem;
      .group_1 {
        background-color: rgba(33, 135, 250, 1);
        width: 1.814rem;
        height: 0.08rem;
      }
    }
  }
  .group_19 {
    width: 51.2rem;
    height: 78.134rem;
    .block_1 {
      background-image: url(/pages/assets/img/c07a69e907014853a419315e004e3a0f_mergeImage.png);
      background-repeat: no-repeat;
      background-size: cover;
      width: 51.2rem;
      height: 12.8rem;
      .image_2 {
        width: 51.2rem;
        height: 0.027rem;
      }
      .text-wrapper_1 {
        width: 18.08rem;
        height: 1.334rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 1.334rem;
        margin: 3.494rem 0 0 6.96rem;
        .text_7 {
          width: 18.08rem;
          height: 1.334rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 1.333rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 1.334rem;
        }
        .text_8 {
          width: 18.08rem;
          height: 1.334rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 1.333rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 1.334rem;
        }
      }
      .text_9 {
        width: 8.907rem;
        height: 0.64rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.64rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.64rem;
        margin: 0.96rem 0 0 6.96rem;
      }
      .group_2 {
        border-radius: 8px;
        width: 5.067rem;
        height: 1.6rem;
        border: 1px solid rgba(33, 135, 250, 1);
        margin: 1.76rem 0 2.987rem 6.934rem;
        .image-text_12 {
          width: 2.88rem;
          height: 0.534rem;
          margin: 0.534rem 0 0 1.094rem;
          .thumbnail_2 {
            width: 0.534rem;
            height: 0.534rem;
          }
          .text-group_1 {
            width: 1.92rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.48rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;
            margin-top: 0.027rem;
          }
        }
      }
    }
    .block_2 {
      background-color: rgba(243, 245, 249, 1);
      width: 51.2rem;
      height: 23.787rem;
      .box_3 {
        width: 13.734rem;
        height: 1.867rem;
        background: url(/pages/assets/img/SketchPng0e0d33df318eb84b68c25378364b3f9cd446e8a819351c4506f5e0e2ef97abbb.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin: 1.334rem 0 0 18.747rem;
        .text-wrapper_2 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 3.6rem;
          margin: 0.32rem 0 0 0.427rem;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);
            background: url(/pages/assets/img/SketchPngc0e3b3c1a97f6618d642155479c5c30ad48ff6b60e95c577c195add22afa4225.png)
              100% no-repeat;
            background-size: 100% 100%;

            .text_10 {
              color: rgba(255, 255, 255, 1);
            }
          }

          &:active {
            transform: scale(0.98);
          }

          .text_10 {
            width: 2.534rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
        .text-wrapper_3 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.774rem;
          margin: 0.32rem 0 0 0.32rem;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);
            background: url(/pages/assets/img/SketchPngc0e3b3c1a97f6618d642155479c5c30ad48ff6b60e95c577c195add22afa4225.png)
              100% no-repeat;
            background-size: 100% 100%;

            .text_11 {
              color: rgba(255, 255, 255, 1);
            }
          }

          &:active {
            transform: scale(0.98);
          }

          .text_11 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
        .text-wrapper_4 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.774rem;
          margin: 0.32rem 0 0 0.32rem;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);
            background: url(/pages/assets/img/SketchPngc0e3b3c1a97f6618d642155479c5c30ad48ff6b60e95c577c195add22afa4225.png)
              100% no-repeat;
            background-size: 100% 100%;

            .text_12 {
              color: rgba(255, 255, 255, 1);
            }
          }

          &:active {
            transform: scale(0.98);
          }

          .text_12 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
        .text-wrapper_5 {
          height: 1.227rem;
          background: url(/pages/assets/img/SketchPng8ed9adf4b38388e90497b7a73c7211f1ae5ffd61308cbf51965c5c7e81a7df26.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 2.774rem;
          margin: 0.32rem 0.427rem 0 0.32rem;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(33, 135, 250, 0.3);
            background: url(/pages/assets/img/SketchPngc0e3b3c1a97f6618d642155479c5c30ad48ff6b60e95c577c195add22afa4225.png)
              100% no-repeat;
            background-size: 100% 100%;

            .text_13 {
              color: rgba(255, 255, 255, 1);
            }
          }

          &:active {
            transform: scale(0.98);
          }

          .text_13 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
            margin: 0.32rem 0 0 0.534rem;
            transition: color 0.3s ease;
          }
        }
      }
      .text-group_21 {
        width: 15.36rem;
        height: 2.454rem;
        margin: 2.667rem 0 0 17.92rem;
        .text_14 {
          width: 15.36rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 1.28rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          white-space: nowrap;
          line-height: 1.28rem;
        }
        .text_15 {
          width: 12.747rem;
          height: 0.534rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.533rem;
          text-transform: uppercase;
          font-weight: NaN;
          text-align: right;
          white-space: nowrap;
          line-height: 0.534rem;
          margin: 0.64rem 0 0 1.307rem;
        }
      }
      .box_4 {
        background-color: rgba(243, 245, 249, 1);
        height: 13.334rem;
        margin-top: 2.134rem;
        width: 51.2rem;
        .group_3 {
          position: relative;
          width: 38.8rem;
          height: 13.334rem;
          background: url(/pages/assets/img/84bee5897edd4b5d8cba904c5db69b07_mergeImage.png) 100%
            no-repeat;
          background-size: 100% 100%;
          .image_3 {
            width: 4.267rem;
            height: 1.44rem;
            margin: 2.907rem 0 0 6.934rem;
          }
          .paragraph_1 {
            width: 13.44rem;
            height: 5.174rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.48rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: justify;
            line-height: 1.067rem;
            margin: 0.56rem 0 3.254rem 6.934rem;
          }
          .box_5 {
            box-shadow: 3px 2px 11px 0px rgba(203, 203, 203, 0.5);
            background-color: rgba(255, 255, 255, 1);
            border-radius: 8px;
            position: absolute;
            left: 20.747rem;
            top: 2.107rem;
            width: 23.52rem;
            height: 8.96rem;
            justify-content: flex-center;
            .text-wrapper_6 {
              width: 21.467rem;
              height: 2.667rem;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: justify;
              line-height: 0.907rem;
              margin: 1.067rem 0 0 1.067rem;
              .text_16 {
                width: 21.467rem;
                height: 2.667rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.48rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                line-height: 0.907rem;
              }
              .paragraph_2 {
                width: 21.467rem;
                height: 2.667rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.426rem;
                font-weight: NaN;
                text-align: left;
                line-height: 0.907rem;
              }
            }
            .image_4 {
              width: 21.467rem;
              height: 0.027rem;
              margin: 0.72rem 0 0 1.067rem;
            }
            .paragraph_3 {
              width: 21.467rem;
              height: 2.667rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: justify;
              line-height: 0.907rem;
              margin: 0.747rem 0 1.067rem 1.067rem;
            }
          }
        }
      }
    }
    .block_3 {
      position: relative;
      width: 51.2rem;
      height: 18.267rem;
      .section_11 {
        width: 10.08rem;
        height: 10.187rem;
        margin: 4.08rem 0 0 20.534rem;
        .box_6 {
          background-color: rgba(255, 255, 255, 1);
          width: 10.08rem;
          height: 4.614rem;
          .text-group_22 {
            width: 4.694rem;
            height: 2.48rem;
            margin: 1.067rem 0 0 0.854rem;
            .text_17 {
              width: 1.92rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
            .paragraph_4 {
              width: 4.694rem;
              height: 1.174rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              line-height: 0.587rem;
              margin-top: 0.427rem;
            }
          }
          .image_5 {
            width: 3.2rem;
            height: 3.2rem;
            margin: 0.694rem 0.32rem 0 1.014rem;
          }
        }
        .box_7 {
          background-color: rgba(255, 255, 255, 1);
          width: 10.08rem;
          height: 4.614rem;
          margin-top: 0.96rem;
          .text-group_23 {
            width: 4.827rem;
            height: 2.48rem;
            margin: 1.067rem 0 0 0.747rem;
            .text_18 {
              width: 1.92rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
            .paragraph_5 {
              width: 4.827rem;
              height: 1.174rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              line-height: 0.587rem;
              margin-top: 0.427rem;
            }
          }
          .image_6 {
            width: 3.2rem;
            height: 3.2rem;
            margin: 0.72rem 0.32rem 0 0.987rem;
          }
        }
      }
      .section_12 {
        width: 10.134rem;
        height: 10.214rem;
        margin: 4.08rem 9.6rem 0 0.854rem;
        .box_8 {
          background-color: rgba(255, 255, 255, 1);
          width: 10.08rem;
          height: 4.614rem;
          .text-group_24 {
            width: 3.414rem;
            height: 2.48rem;
            margin: 1.067rem 0 0 0.854rem;
            .text_19 {
              width: 1.92rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
            .paragraph_6 {
              width: 3.414rem;
              height: 1.174rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              line-height: 0.587rem;
              margin-top: 0.427rem;
            }
          }
          .image_7 {
            width: 3.2rem;
            height: 3.2rem;
            margin: 0.72rem 0.32rem 0 2.294rem;
          }
        }
        .box_9 {
          background-color: rgba(255, 255, 255, 1);
          width: 10.08rem;
          height: 4.614rem;
          margin: 0.987rem 0 0 0.054rem;
          .text-group_25 {
            width: 4.24rem;
            height: 2.48rem;
            margin: 1.04rem 0 0 0.747rem;
            .text_20 {
              width: 1.92rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
            .paragraph_7 {
              width: 4.24rem;
              height: 1.174rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              line-height: 0.587rem;
              margin-top: 0.427rem;
            }
          }
          .image_8 {
            width: 3.2rem;
            height: 3.2rem;
            margin: 0.694rem 0.32rem 0 1.574rem;
          }
        }
      }
      .group_6 {
        position: absolute;
        left: 8.267rem;
        top: 2.667rem;
        width: 16rem;
        height: 12.934rem;
        background: url(/pages/assets/img/SketchPng95087e84221aaa89017d7e61d8dd43f78ff216be5e669d7ed864bdca909ebbd1.png)
          100% no-repeat;
        background-size: 100% 100%;
        justify-content: flex-center;
        .text-group_26 {
          width: 5.12rem;
          height: 2.347rem;
          margin: 1.334rem 0 0 1.334rem;
          .text_21 {
            width: 5.12rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 1.28rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: right;
            white-space: nowrap;
            line-height: 1.28rem;
          }
          .text_22 {
            width: 5.12rem;
            height: 0.534rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: right;
            white-space: nowrap;
            line-height: 0.534rem;
            margin-top: 0.534rem;
          }
        }
        .section_3 {
          background-color: rgba(255, 255, 255, 1);
          width: 10.08rem;
          height: 4.614rem;
          margin: 3.307rem 0 1.334rem 1.334rem;
          .text-group_27 {
            width: 4.24rem;
            height: 2.48rem;
            margin: 1.067rem 0 0 0.854rem;
            .text_23 {
              width: 1.92rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
            .paragraph_8 {
              width: 4.24rem;
              height: 1.174rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              line-height: 0.587rem;
              margin-top: 0.427rem;
            }
          }
          .image_9 {
            width: 3.2rem;
            height: 3.2rem;
            margin: 0.694rem 0.32rem 0 1.467rem;
          }
        }
      }
    }
    .block_4 {
      background-color: rgba(255, 255, 255, 1);
      height: 23.254rem;
      margin-bottom: 0.027rem;
      width: 51.2rem;
      .block_11 {
        width: 10.987rem;
        height: 2.454rem;
        margin: 2.667rem 0 0 20.107rem;
        .text-group_28 {
          width: 10.987rem;
          height: 2.454rem;
          .text_24 {
            width: 5.12rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 1.28rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: right;
            white-space: nowrap;
            line-height: 1.28rem;
            margin-left: 2.934rem;
          }
          .text_25 {
            width: 10.987rem;
            height: 0.534rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: right;
            white-space: nowrap;
            line-height: 0.534rem;
            margin-top: 0.64rem;
          }
        }
      }
      .block_12 {
        width: 37.334rem;
        height: 13.867rem;
        margin: 2.134rem 0 2.134rem 6.934rem;
      }
      .box_11 {
        background-color: rgba(243, 245, 249, 0.5);
        width: 31.947rem;
        height: 13.867rem;
        border: 1px solid rgba(227, 227, 227, 1);
        .group_9 {
          background-color: rgba(255, 255, 255, 1);
          width: 13.387rem;
          height: 13.867rem;
          .text_26 {
            width: 2.24rem;
            height: 0.747rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.746rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.747rem;
            margin: 1.867rem 0 0 1.334rem;
          }
          .text-wrapper_7 {
            width: 8.534rem;
            height: 1.44rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: NaN;
            text-align: left;
            line-height: 0.8rem;
            margin: 0.64rem 0 0 1.334rem;
            .paragraph_9 {
              width: 8.534rem;
              height: 1.44rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              line-height: 0.8rem;
            }
            .text_27 {
              width: 8.534rem;
              height: 1.44rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              line-height: 0.8rem;
            }
            .paragraph_10 {
              width: 8.534rem;
              height: 1.44rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              line-height: 0.8rem;
            }
          }
          .text_28 {
            width: 1.6rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.32rem;
            margin: 1.334rem 0 0 1.334rem;
          }
          .box_20 {
            width: 9.174rem;
            height: 0.907rem;
            margin: 0.534rem 0 0 1.334rem;
            .text-wrapper_8 {
              background-color: rgba(239, 247, 255, 1);
              border-radius: 4px;
              height: 0.907rem;
              width: 2.774rem;
              .text_29 {
                width: 1.76rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: right;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.267rem 0 0 0.507rem;
              }
            }
            .text-wrapper_9 {
              background-color: rgba(239, 247, 255, 1);
              border-radius: 4px;
              height: 0.907rem;
              margin-left: 0.427rem;
              width: 2.774rem;
              .text_30 {
                width: 0.8rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: right;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.267rem 0 0 0.987rem;
              }
            }
            .text-wrapper_10 {
              background-color: rgba(239, 247, 255, 1);
              border-radius: 4px;
              height: 0.907rem;
              margin-left: 0.427rem;
              width: 2.774rem;
              .text_31 {
                width: 1.014rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: right;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.267rem 0 0 0.88rem;
              }
            }
          }
          .box_21 {
            width: 9.174rem;
            height: 0.907rem;
            margin: 0.427rem 0 0 1.334rem;
            .text-wrapper_11 {
              background-color: rgba(239, 247, 255, 1);
              border-radius: 4px;
              height: 0.907rem;
              width: 2.774rem;
              .text_32 {
                width: 1.387rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: right;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.267rem 0 0 0.694rem;
              }
            }
            .text-wrapper_12 {
              background-color: rgba(239, 247, 255, 1);
              border-radius: 4px;
              height: 0.907rem;
              margin-left: 0.427rem;
              width: 2.774rem;
              .text_33 {
                width: 0.747rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: right;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.267rem 0 0 1.014rem;
              }
            }
            .text-wrapper_13 {
              background-color: rgba(239, 247, 255, 1);
              border-radius: 4px;
              height: 0.907rem;
              margin-left: 0.427rem;
              width: 2.774rem;
              .text_34 {
                width: 1.494rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: right;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.267rem 0 0 0.64rem;
              }
            }
          }
          .box_22 {
            width: 7.947rem;
            height: 0.907rem;
            margin: 0.427rem 0 3.414rem 1.334rem;
            .block_5 {
              height: 0.907rem;
              background: url(/pages/assets/img/SketchPng37c6bff2e3ba4cc6c9e637ce8e40b0de3c733c95ec19a0a9ae57087185dd4cc5.png)
                100% no-repeat;
              background-size: 100% 100%;
              width: 4.747rem;
              .text-wrapper_14 {
                width: 4.107rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                font-size: 0;
                font-weight: NaN;
                text-align: right;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.267rem 0 0 0.32rem;
                .text_35 {
                  width: 4.107rem;
                  height: 0.374rem;
                  overflow-wrap: break-word;
                  color: rgba(0, 0, 0, 1);
                  font-size: 0.373rem;
                  font-weight: NaN;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.374rem;
                }
                .text_36 {
                  width: 4.107rem;
                  height: 0.374rem;
                  overflow-wrap: break-word;
                  color: rgba(0, 0, 0, 1);
                  font-size: 0.32rem;
                  font-weight: NaN;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.374rem;
                }
              }
            }
            .text-wrapper_15 {
              background-color: rgba(239, 247, 255, 1);
              border-radius: 4px;
              height: 0.907rem;
              width: 2.774rem;
              .text_37 {
                width: 1.494rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.373rem;
                font-weight: NaN;
                text-align: right;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.267rem 0 0 0.64rem;
              }
            }
          }
        }
        .group_10 {
          width: 15.68rem;
          margin: 1.574rem 1.84rem 0 1.04rem;
          .group_11 {
            margin: 4.32rem 0 0 6.854rem;
          }
        }
      }
    }
  }
}

.box_15 {
  width: 51.2rem;
  height: 26.267rem;
  margin-top: -0.026rem;
  .text-group_29 {
    width: 15.36rem;
    height: 2.454rem;
    margin: 2.667rem 0 0 17.92rem;
    .text_38 {
      width: 15.36rem;
      height: 1.28rem;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 1.28rem;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: right;
      white-space: nowrap;
      line-height: 1.28rem;
    }
    .text_39 {
      width: 4.267rem;
      height: 0.534rem;
      overflow-wrap: break-word;
      color: rgba(102, 102, 102, 1);
      font-size: 0.533rem;
      font-weight: NaN;
      text-align: right;
      white-space: nowrap;
      line-height: 0.534rem;
      margin: 0.64rem 0 0 5.547rem;
    }
  }
  .text-wrapper_21 {
    width: 18.987rem;
    height: 0.48rem;
    margin: 2.134rem 0 0 16.107rem;
    position: relative;

    .scenario-nav-item {
      width: 1.92rem;
      height: 0.48rem;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 0.48rem;
      font-weight: 400;
      text-align: left;
      white-space: nowrap;
      line-height: 0.48rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:not(:first-child) {
        margin-left: 1.494rem;
      }

      &.active {
        color: rgba(33, 135, 250, 1);
        font-family: PingFangSC-Medium;
        font-weight: 500;
      }

      &:hover {
        color: rgba(33, 135, 250, 0.8);
      }
    }
  }

  .scenario-underline {
    position: absolute;
    background-color: rgba(33, 135, 250, 1);
    width: 1.92rem;
    height: 0.08rem;
    margin-top: 0.854rem;
    transition: left 0.3s ease;
  }

  .box_23 {
    width: 37.334rem;
    height: 13.867rem;
    margin: 1.6rem 0 2.134rem 6.934rem;
    .group_12 {
      background-color: rgba(255, 255, 255, 1);
      width: 14.134rem;
      height: 13.867rem;
      .group_20 {
        width: 11.787rem;
        height: 3.734rem;
        margin: 1.067rem 0 0 1.28rem;
        .text-wrapper_22 {
          width: 7.2rem;
          height: 3.2rem;
          margin-top: 0.534rem;
          .text_46 {
            width: 3.414rem;
            height: 0.854rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.853rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.854rem;
          }
          .paragraph_11 {
            width: 7.2rem;
            height: 1.707rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: NaN;
            text-align: left;
            line-height: 0.854rem;
            margin-top: 0.64rem;
          }
        }
        .image-wrapper_1 {
          height: 2.667rem;
          background: url(/pages/assets/img/SketchPngddb5857055b17a27ceadb83741f4265e79908e91c113648aed738491dd25f6b8.png)
            0rem 0rem no-repeat;
          background-size: 2.667rem 2.694rem;
          width: 2.667rem;
          .image_10 {
            width: 2.134rem;
            height: 2.134rem;
            margin: 0.267rem 0 0 0.267rem;
          }
        }
      }
      .image_11 {
        width: 11.84rem;
        height: 0.027rem;
        margin: 0.747rem 0 0 1.28rem;
      }
      .paragraph_12 {
        width: 10.667rem;
        height: 6.347rem;
        overflow-wrap: break-word;
        color: rgba(68, 68, 68, 1);
        font-size: 0.426rem;
        font-weight: NaN;
        text-align: left;
        line-height: 0.907rem;
        margin: 0.614rem 0 1.334rem 1.28rem;
      }
    }
    .group_14 {
      height: 13.867rem;
      background: url(/pages/assets/img/SketchPng184a93001eb57f31fe07dbbce1a8e219091a43445240c00011a537720db9d238.png)
        100% no-repeat;
      background-size: 100% 100%;
      width: 23.2rem;
      position: relative;

      .demo-image {
        width: 20rem;
        height: 13rem;
        margin: 1.254rem 0 0 2.774rem;
        border-radius: 8px;
        object-fit: contain;
      }
    }
  }
}
.box_18 {
  background-color: rgba(17, 26, 52, 1);
  width: 51.2rem;
  height: 11.04rem;
  .box_26 {
    width: 31.787rem;
    height: 3.44rem;
    margin: 1.36rem 0 0 9.6rem;
    .section_13 {
      width: 7.44rem;
      height: 2.934rem;
      margin-top: 0.507rem;
      .image-text_19 {
        width: 4.374rem;
        height: 0.374rem;
        .thumbnail_6 {
          width: 0.374rem;
          height: 0.374rem;
        }
        .text-group_17 {
          width: 3.734rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
        }
      }
      .image-text_20 {
        width: 5.707rem;
        height: 0.374rem;
        margin-top: 0.907rem;
        .thumbnail_7 {
          width: 0.374rem;
          height: 0.374rem;
        }
        .text-group_18 {
          width: 5.067rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
          .text_50 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
          .text_51 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
      }
      .image-text_21 {
        width: 7.44rem;
        height: 0.374rem;
        margin-top: 0.907rem;
        .thumbnail_8 {
          width: 0.374rem;
          height: 0.374rem;
        }
        .text-group_19 {
          width: 6.8rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
        }
      }
    }
    .image-text_22 {
      width: 3.36rem;
      height: 3.44rem;
      .block_7 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/4aa4ebd398a54cc4ba90bb6275002e64_mergeImage.png);
        background-repeat: no-repeat;
        background-size: cover;
        width: 2.667rem;
        height: 2.667rem;
        margin-left: 0.347rem;
      }
      .text-group_20 {
        width: 3.36rem;
        height: 0.374rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.374rem;
        margin-top: 0.4rem;
      }
    }
  }
  .block_8 {
    width: 51.2rem;
    height: 4.054rem;
    background: url(/pages/assets/img/SketchPng5c8ba53f71bd92f957537da4d03c76390cd3535fdd5fe926d77e0f7c541f1f80.png)
      100% no-repeat;
    background-size: 100% 100%;
    margin: 1.387rem 0 0.8rem 0;
    .image_12 {
      width: 4.267rem;
      height: 0.64rem;
      margin: 0.854rem 0 0 9.6rem;
    }
    .paragraph_13 {
      width: 7.227rem;
      height: 1.494rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 0.426rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      line-height: 0.747rem;
      margin: 0.587rem 0 0.48rem 9.6rem;
    }
  }
}

/* 产品能力部分样式 - 基于demo模板 */
.section_20 {
  display: flex;
  width: 35rem;
  height: 13.867rem;
  margin: 2.134rem 0 0 6.934rem;
}

/* 左侧菜单栏样式 - 基于demo的box_19 */
.group_38 {
  display: flex;
  flex-direction: column;
  width: 4.854rem;
  height: 12.267rem;
  margin-top: 0.427rem;
}

.menu-item {
  width: 4.854rem;
  height: 1.6rem;
  background: url(/pages/assets/img/SketchPng580eae31f82f9b44663a87dfd6b299613e2ba55556358724ca47044475f2e32b.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin-top: 0.534rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;

  &:first-child {
    margin-top: 0;
    background: url(/pages/assets/img/SketchPng580eae31f82f9b44663a87dfd6b299613e2ba55556358724ca47044475f2e32b.png)
      100% no-repeat;
    background-size: 100% 100%;
  }

  &.active {
    background: url(/pages/assets/img/SketchPng12db6e589961dc6b6f3a429bef3639147b056b286b26cc2c65f6fbb8b7203ed0.png)
      100% no-repeat;
    background-size: 100% 100%;

    .active-indicator {
      display: block;
    }

    .menu-text {
      color: rgba(33, 135, 250, 1);
      font-weight: 600;
    }
  }
}

.active-indicator {
  background-color: rgba(33, 135, 250, 1);
  width: 0.107rem;
  height: 1.6rem;
  display: none;
}

.menu-icon {
  width: 0.64rem;
  height: 0.64rem;
  margin: 0.48rem 0 0 0.534rem;
}

.menu-text {
  width: 1.44rem;
  height: 0.48rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  text-align: left;
  white-space: nowrap;
  line-height: 0.48rem;
  margin: 0.56rem 0 0 0.534rem;
  transition: all 0.3s ease;
}

/* 右侧内容区域样式 - 基于demo的box_11 */
.content-area {
  background-color: rgba(243, 245, 249, 0.5);
  width: 31.947rem;
  height: 13.867rem;
  border: 1px solid rgba(227, 227, 227, 1);
  border-radius: 12px;
  margin-left: 0.533rem;
  overflow: hidden;

  /* 统一所有产品能力的右侧图片样式 */
  .group_31 {
    width: 18.56rem;
    height: 13.867rem;

    .box_29 {
      width: 100%;
      height: 100%;
      background-color: rgba(248, 248, 248, 1);
      border-radius: 0px 12px 12px 0px;
      display: flex;
      align-items: center;
      justify-content: center;

      .demo-image {
        width: 20rem;
        height: 13rem;
        object-fit: contain;
        border-radius: 8px;
      }
    }
  }
}

.content-section {
  display: flex;
  height: 100%;
}

.content-left {
  background-color: rgba(255, 255, 255, 1);
  width: 13.387rem;
  height: 13.867rem;
  padding: 0;
  display: flex;
  flex-direction: column;
  border-radius: 12px 0 0 12px;
}

.content-right {
  width: 18.56rem;
  height: 13.867rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.content-title {
  width: 2.24rem;
  height: 0.747rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.746rem;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 0.747rem;
  margin: 1.867rem 0 0 1.334rem;
}

.content-description {
  width: 8.534rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  font-size: 0;
  text-align: left;
  line-height: 0.8rem;
  margin: 0.64rem 0 0 1.334rem;
}

.description-text {
  width: 8.534rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  text-align: left;
  line-height: 0.8rem;
}

.highlight-text {
  width: 8.534rem;
  height: 1.44rem;
  overflow-wrap: break-word;
  color: rgba(33, 135, 250, 1);
  font-size: 0.426rem;
  text-align: left;
  line-height: 0.8rem;
}

.separator-line {
  width: 8.534rem;
  height: 1px;
  background-color: rgba(227, 227, 227, 1);
  margin: 1.334rem 0 0 1.334rem;
}

.models-label {
  width: 1.6rem;
  height: 0.32rem;
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 0.32rem;
  text-align: left;
  white-space: nowrap;
  line-height: 0.32rem;
  margin: 1.334rem 0 0 1.334rem;
}

.models-grid {
  width: 9.174rem;
  height: 0.907rem;
  margin: 0.534rem 0 0 1.334rem;
  display: flex;
  gap: 0.427rem;

  &:nth-child(6) {
    margin-top: 0.427rem;
  }

  &:nth-child(7) {
    margin-top: 0.427rem;
  }
}

.model-tag {
  background-color: rgba(239, 247, 255, 1);
  border-radius: 4px;
  height: 0.907rem;
  width: 2.774rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 1);
  font-size: 0.373rem;
  text-align: center;
  white-space: nowrap;
  line-height: 0.374rem;

  &.wide {
    width: 5.971rem;
  }
}

.demo-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.demo-image {
  width: 20rem;
  height: 13rem;
  object-fit: contain;
  border-radius: 8px;
}

/* 知识库特殊样式 */
.file-types {
  display: flex;
  gap: 0.267rem;
  margin: 1.067rem 0 0 1.334rem;
  align-items: center;
}

.file-type {
  background-color: rgba(246, 246, 246, 1);
  padding: 0.134rem 0.267rem;
  border-radius: 2px;
  font-size: 0.32rem;
  font-weight: bold;
  color: rgba(102, 102, 102, 1);
  border: 0.5px solid rgba(224, 224, 224, 1);
}

.import-label {
  font-size: 0.32rem;
  color: rgba(153, 153, 153, 1);
  margin-left: 0.267rem;
}

/* 渠道接入特殊样式 */
.channel-icons {
  display: flex;
  gap: 0.534rem;
  margin: 1.334rem 0 0 1.334rem;
  align-items: center;
}

.channel-icon {
  width: 1.067rem;
  height: 1.067rem;
}

/* 添加内容区域的默认样式 */
.content-section p {
  margin: 1.334rem 0 0 1.334rem;
  width: 8.534rem;
  color: rgba(0, 0, 0, 1);
  font-size: 0.426rem;
  line-height: 0.8rem;
}

/* 分隔线样式 */
.separator-line {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==)
    repeat-x;
}

/* 基于demo模板的产品能力样式 */

// 大模型内容样式
.box_9 {
  background-color: rgba(243, 245, 249, 0.5);
  border-radius: 12px;
  width: 31.947rem;
  height: 13.867rem;
  border: 1px solid rgba(227, 227, 227, 1);
  display: flex;
}

.section_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px 0px 0px 12px;
  width: 13.387rem;
  height: 13.867rem;

  .text_5 {
    width: 2.24rem;
    height: 0.747rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.746rem;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 0.747rem;
    margin: 1.867rem 0 0 1.334rem;
  }

  .text-wrapper_3 {
    width: 8.534rem;
    height: 1.44rem;
    overflow-wrap: break-word;
    font-size: 0;
    font-weight: NaN;
    text-align: left;
    line-height: 0.8rem;
    margin: 0.64rem 0 0 1.334rem;

    .paragraph_1,
    .paragraph_2 {
      color: rgba(0, 0, 0, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }

    .text_6 {
      color: rgba(33, 135, 250, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }
  }

  .text_7 {
    width: 1.6rem;
    height: 0.32rem;
    overflow-wrap: break-word;
    color: rgba(153, 153, 153, 1);
    font-size: 0.32rem;
    font-weight: NaN;
    text-align: left;
    white-space: nowrap;
    line-height: 0.32rem;
    margin: 1.334rem 0 0 1.334rem;
  }

  .box_37,
  .box_38,
  .box_39 {
    width: 9.174rem;
    height: 0.907rem;
    margin: 0.534rem 0 0 1.334rem;
    display: flex;
    gap: 0.427rem;

    .text-wrapper_4,
    .text-wrapper_5,
    .text-wrapper_6,
    .text-wrapper_7,
    .text-wrapper_8,
    .text-wrapper_9,
    .text-wrapper_10,
    .text-wrapper_11 {
      background-color: rgba(246, 246, 246, 1);
      border-radius: 4px;
      height: 0.907rem;
      border: 0.5px solid rgba(224, 224, 224, 1);
      width: 2.774rem;
      display: flex;
      align-items: center;
      justify-content: center;

      span {
        color: rgba(0, 0, 0, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.374rem;
      }
    }
  }

  .box_39 {
    margin: 0.427rem 0 3.414rem 1.334rem;
    width: 7.947rem;

    .box_13 {
      height: 0.907rem;
      background-color: rgba(246, 246, 246, 1);
      border-radius: 4px;
      border: 0.5px solid rgba(224, 224, 224, 1);
      width: 4.747rem;
      display: flex;
      align-items: center;
      justify-content: center;

      .text-wrapper_10 {
        width: auto;
        height: auto;
        margin: 0;

        .text_14,
        .text_15 {
          color: rgba(0, 0, 0, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 0.374rem;
        }
      }
    }

    .text-wrapper_11 {
      margin-left: 0.427rem;
    }
  }
}

.section_2 {
  width: 18.56rem;
  height: 13.867rem;

  .box_14 {
    width: 100%;
    height: 100%;

    .block_1 {
      width: 100%;
      height: 100%;
      background-color: rgba(248, 248, 248, 1);
      border-radius: 0px 12px 12px 0px;
    }
  }
}

// 知识库内容样式
.group_8 {
  width: 31.947rem;
  height: 13.867rem;
  background-color: rgba(243, 245, 249, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(227, 227, 227, 1);
  display: flex;
}

.group_9 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px 0px 0px 12px;
  width: 13.387rem;
  height: 13.867rem;

  .text_19 {
    width: 2.24rem;
    height: 0.747rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.746rem;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 0.747rem;
    margin: 1.867rem 0 0 1.334rem;
  }

  .text-wrapper_12 {
    width: 8.534rem;
    height: 1.44rem;
    overflow-wrap: break-word;
    font-size: 0;
    font-weight: NaN;
    text-align: left;
    line-height: 0.8rem;
    margin: 0.64rem 0 0 1.334rem;

    .text_20,
    .text_21 {
      color: rgba(33, 135, 250, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }

    .paragraph_3 {
      color: rgba(0, 0, 0, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }
  }

  .image_1 {
    width: 8.534rem;
    height: 0.027rem;
    margin: 1.334rem 0 0 1.334rem;
  }

  .paragraph_4 {
    width: 8.534rem;
    height: 2.56rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.426rem;
    font-weight: NaN;
    text-align: left;
    line-height: 0.8rem;
    margin: 1.334rem 0 0 1.334rem;
  }
}

.group_10 {
  width: 18.56rem;
  height: 13.867rem;

  .box_21 {
    width: 100%;
    height: 10.667rem;

    .section_4 {
      width: 100%;
      height: 100%;
      background-color: rgba(248, 248, 248, 1);
      border-radius: 0px 12px 0px 0px;
      display: flex;
      align-items: center;
      justify-content: center;

      .demo-image {
        width: 20rem;
        height: 13rem;
        object-fit: contain;
        border-radius: 8px;
      }
    }
  }

  .box_22 {
    width: 8.534rem;
    height: 0.8rem;
    margin: 0.534rem 0 0 5.013rem;
    display: flex;
    align-items: center;
    gap: 0.267rem;

    .label_13 {
      width: 0.8rem;
      height: 0.8rem;
    }

    .text-wrapper_13,
    .text-wrapper_14,
    .text-wrapper_15,
    .text-wrapper_16 {
      background-color: rgba(246, 246, 246, 1);
      border-radius: 4px;
      width: 0.8rem;
      height: 0.8rem;
      border: 0.5px solid rgba(224, 224, 224, 1);
      display: flex;
      align-items: center;
      justify-content: center;

      span {
        color: rgba(0, 0, 0, 1);
        font-size: 0.32rem;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.32rem;
        text-transform: uppercase;
      }
    }
  }

  .text-wrapper_17 {
    width: 1.28rem;
    height: 0.32rem;
    margin: 0.534rem 0 0 9.6rem;

    .text_26 {
      color: rgba(153, 153, 153, 1);
      font-size: 0.32rem;
      font-weight: NaN;
      text-align: left;
      white-space: nowrap;
      line-height: 0.32rem;
    }
  }
}

// 数据库内容样式
.group_14 {
  width: 31.947rem;
  height: 13.867rem;
  background-color: rgba(243, 245, 249, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(227, 227, 227, 1);
  display: flex;
}

.block_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px 0px 0px 12px;
  width: 13.387rem;
  height: 13.867rem;

  .text_29 {
    width: 2.24rem;
    height: 0.747rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.746rem;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 0.747rem;
    margin: 1.867rem 0 0 1.334rem;
  }

  .text-wrapper_18 {
    width: 8.534rem;
    height: 1.44rem;
    overflow-wrap: break-word;
    font-size: 0;
    font-weight: NaN;
    text-align: left;
    line-height: 0.8rem;
    margin: 0.64rem 0 0 1.334rem;

    .text_30,
    .paragraph_5 {
      color: rgba(0, 0, 0, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }

    .text_31 {
      color: rgba(33, 135, 250, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }
  }

  .image_2 {
    width: 8.534rem;
    height: 0.027rem;
    margin: 1.334rem 0 0 1.334rem;
  }

  .paragraph_6 {
    width: 8.534rem;
    height: 1.92rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.426rem;
    font-weight: NaN;
    text-align: left;
    line-height: 0.8rem;
    margin: 1.334rem 0 0 1.334rem;
  }
}

.block_3 {
  width: 18.56rem;
  height: 13.867rem;

  .block_4 {
    width: 100%;
    height: 100%;

    .box_24 {
      width: 100%;
      height: 100%;
      background-color: rgba(248, 248, 248, 1);
      border-radius: 0px 12px 12px 0px;
      display: flex;
      align-items: center;
      justify-content: center;

      .demo-image {
        width: 20rem;
        height: 13rem;
        object-fit: contain;
        border-radius: 8px;
      }
    }
  }
}

// 插件内容样式
.section_13 {
  width: 31.947rem;
  height: 13.867rem;
  background-color: rgba(243, 245, 249, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(227, 227, 227, 1);
  display: flex;
}

.box_25 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px 0px 0px 12px;
  width: 13.387rem;
  height: 13.867rem;

  .text_34 {
    width: 2.24rem;
    height: 0.747rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.746rem;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 0.747rem;
    margin: 1.867rem 0 0 1.334rem;
  }

  .text-wrapper_19 {
    width: 8.534rem;
    height: 1.44rem;
    overflow-wrap: break-word;
    font-size: 0;
    font-weight: NaN;
    text-align: left;
    line-height: 0.8rem;
    margin: 0.64rem 0 0 1.334rem;

    .text_35,
    .paragraph_7 {
      color: rgba(0, 0, 0, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }

    .text_36 {
      color: rgba(33, 135, 250, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }
  }

  .image_3 {
    width: 8.534rem;
    height: 0.027rem;
    margin: 1.334rem 0 0 1.334rem;
  }

  .paragraph_8 {
    width: 8.534rem;
    height: 3.84rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.426rem;
    font-weight: NaN;
    text-align: left;
    line-height: 0.8rem;
    margin: 1.334rem 0 0 1.334rem;
  }
}

.box_26 {
  width: 18.56rem;
  height: 13.867rem;

  .group_41 {
    width: 100%;
    height: 10.667rem;

    .block_11 {
      width: 100%;
      height: 100%;
      background-color: rgba(248, 248, 248, 1);
      border-radius: 0px 12px 0px 0px;
      display: flex;
      align-items: center;
      justify-content: center;

      .demo-image {
        width: 20rem;
        height: 13rem;
        object-fit: contain;
        border-radius: 8px;
      }
    }
  }

  .group_42 {
    width: 100%;
    height: 3.2rem;
    display: flex;

    .box_27,
    .box_28 {
      width: 50%;
      height: 100%;
      background-color: rgba(248, 248, 248, 1);
    }

    .box_27 {
      border-radius: 0px 0px 0px 12px;
    }

    .box_28 {
      border-radius: 0px 0px 12px 0px;
    }
  }
}

// 工作流内容样式
.group_29 {
  width: 31.947rem;
  height: 13.867rem;
  background-color: rgba(243, 245, 249, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(227, 227, 227, 1);
  display: flex;
}

.group_30 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px 0px 0px 12px;
  width: 13.387rem;
  height: 13.867rem;

  .text_39 {
    width: 2.24rem;
    height: 0.747rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.746rem;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 0.747rem;
    margin: 1.867rem 0 0 1.334rem;
  }

  .text-wrapper_20 {
    width: 8.534rem;
    height: 1.44rem;
    overflow-wrap: break-word;
    font-size: 0;
    font-weight: NaN;
    text-align: left;
    line-height: 0.8rem;
    margin: 0.64rem 0 0 1.334rem;

    .paragraph_9 {
      color: rgba(0, 0, 0, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }

    .text_40,
    .text_41,
    .text_42 {
      color: rgba(33, 135, 250, 1);
      font-size: 0.426rem;
      font-weight: NaN;
      text-align: left;
      line-height: 0.8rem;
    }
  }

  .image_4 {
    width: 8.534rem;
    height: 0.027rem;
    margin: 1.334rem 0 0 1.334rem;
  }

  .paragraph_10 {
    width: 8.534rem;
    height: 0.68rem;
    overflow-wrap: break-word;
    color: rgba(153, 153, 153, 1);
    font-size: 0.42rem;
    text-align: left;
    white-space: wrap;
    line-height: 0.68rem;
    margin: 1.34rem 0 0 1.334rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #666666;
    text-align: left;
    font-style: normal;
  }

  /* 大模型网格样式 */
  .box_37,
  .box_38,
  .box_39 {
    width: 9.174rem;
    height: 0.907rem;
    margin: 0.534rem 0 0 1.334rem;
    display: flex;
    gap: 0.427rem;

    .text-wrapper_4,
    .text-wrapper_5,
    .text-wrapper_6,
    .text-wrapper_7,
    .text-wrapper_8,
    .text-wrapper_9,
    .text-wrapper_10,
    .text-wrapper_11 {
      background-color: rgba(246, 246, 246, 1);
      border-radius: 4px;
      height: 0.907rem;
      border: 0.5px solid rgba(224, 224, 224, 1);
      width: 2.774rem;
      display: flex;
      align-items: center;
      justify-content: center;

      span {
        color: rgba(0, 0, 0, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.374rem;
      }
    }
  }

  .box_39 {
    margin: 0.427rem 0 0 1.334rem;
    width: 7.947rem;

    .box_13 {
      height: 0.907rem;
      background-color: rgba(246, 246, 246, 1);
      border-radius: 4px;
      border: 0.5px solid rgba(224, 224, 224, 1);
      width: 4.747rem;
      display: flex;
      align-items: center;
      justify-content: center;

      .text-wrapper_10 {
        width: auto;
        height: auto;
        margin: 0;

        .text_14,
        .text_15 {
          color: rgba(0, 0, 0, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 0.374rem;
        }
      }
    }

    .text-wrapper_11 {
      margin-left: 0.427rem;
      background-color: rgba(246, 246, 246, 1);
      border-radius: 4px;
      height: 0.907rem;
      border: 0.5px solid rgba(224, 224, 224, 1);
      width: 2.774rem;
      display: flex;
      align-items: center;
      justify-content: center;

      span {
        color: rgba(0, 0, 0, 1);
        font-size: 0.373rem;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.374rem;
      }
    }
  }
}

.group_31 {
  width: 18.56rem;
  height: 13.867rem;

  .box_29 {
    width: 100%;
    height: 100%;
    background-color: rgba(248, 248, 248, 1);
    border-radius: 0px 12px 12px 0px;
    display: flex;
    align-items: center;
    justify-content: center;

    .demo-image {
      width: 20rem;
      height: 13rem;
      object-fit: contain;
      border-radius: 8px;
    }
  }
}

// 渠道接入内容样式
.block_14 {
  width: 31.947rem;
  height: 13.867rem;
  background-color: rgba(243, 245, 249, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(227, 227, 227, 1);
  display: flex;
}

.box_30 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px 0px 0px 12px;
  width: 13.387rem;
  height: 13.867rem;

  .text_45 {
    width: 2.24rem;
    height: 0.747rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.746rem;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 0.747rem;
    margin: 1.867rem 0 0 1.334rem;
  }

  .paragraph_11 {
    width: 8.534rem;
    height: 1.067rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.426rem;
    font-weight: NaN;
    text-align: left;
    line-height: 0.533rem;
    margin: 0.64rem 0 0 1.334rem;
  }

  .image_5 {
    width: 8.534rem;
    height: 0.027rem;
    margin: 1.334rem 0 0 1.334rem;
  }

  .paragraph_12 {
    width: 8.534rem;
    height: 4.48rem;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 0.426rem;
    font-weight: NaN;
    text-align: left;
    line-height: 0.8rem;
    margin: 1.334rem 0 0 1.334rem;
  }
}

.box_31 {
  width: 18.56rem;
  height: 13.867rem;
  background-color: rgba(248, 248, 248, 1);
  border-radius: 0px 12px 12px 0px;
  position: relative;

  .group_45 {
    width: 6.4rem;
    height: 1.6rem;
    margin: 1.334rem 0 0 6.08rem;
    display: flex;
    align-items: center;
    gap: 0.533rem;

    .label_38 {
      width: 1.6rem;
      height: 1.6rem;
    }

    .group_34 {
      width: 4.8rem;
      height: 1.6rem;
    }
  }

  .group_46 {
    width: 16.267rem;
    height: 8.534rem;
    margin: 0.8rem 0 0 1.147rem;
    display: flex;
    gap: 0.533rem;

    .label_39 {
      width: 1.6rem;
      height: 1.6rem;
      margin-top: 3.467rem;
    }

    .section_25 {
      width: 8.534rem;
      height: 8.534rem;

      .box_34 {
        width: 8.534rem;
        height: 6.4rem;
        position: relative;

        .label_40 {
          width: 8.534rem;
          height: 6.4rem;
        }

        .box_35 {
          position: absolute;
          top: 0;
          left: 0;
          width: 8.534rem;
          height: 6.4rem;

          .group_35 {
            width: 8.534rem;
            height: 6.4rem;

            .group_47 {
              width: 6.4rem;
              height: 1.6rem;
              margin: 0.533rem 0 0 1.067rem;

              .text-wrapper_21 {
                width: 6.4rem;
                height: 1.6rem;

                .text_46 {
                  width: 4.8rem;
                  height: 0.533rem;
                  overflow-wrap: break-word;
                  color: rgba(0, 0, 0, 1);
                  font-size: 0.533rem;
                  font-family: PingFangSC-Semibold;
                  font-weight: 600;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.533rem;
                }

                .text_47 {
                  width: 3.2rem;
                  height: 0.427rem;
                  overflow-wrap: break-word;
                  color: rgba(153, 153, 153, 1);
                  font-size: 0.427rem;
                  font-weight: NaN;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 0.427rem;
                  margin-top: 0.64rem;
                }
              }
            }

            .group_48 {
              width: 1.6rem;
              height: 0.533rem;
              margin: 2.987rem 0 0 6.4rem;

              .text-wrapper_22 {
                width: 1.6rem;
                height: 0.533rem;

                .text_48 {
                  width: 1.6rem;
                  height: 0.533rem;
                  overflow-wrap: break-word;
                  color: rgba(0, 0, 0, 1);
                  font-size: 0.533rem;
                  font-family: PingFangSC-Semibold;
                  font-weight: 600;
                  text-align: right;
                  white-space: nowrap;
                  line-height: 0.533rem;
                }
              }
            }

            .group_49 {
              width: 8.534rem;
              height: 1.067rem;
              margin: 0.533rem 0 0 0;

              .box_36 {
                width: 8.534rem;
                height: 1.067rem;
                background-color: rgba(243, 245, 249, 1);
              }
            }
          }
        }
      }

      .label_41 {
        width: 8.534rem;
        height: 2.134rem;
        margin-top: 0;
      }
    }

    .block_19 {
      width: 6.4rem;
      height: 8.534rem;

      .group_36 {
        width: 6.4rem;
        height: 8.534rem;

        .group_37 {
          width: 6.4rem;
          height: 8.534rem;
          background-color: rgba(248, 248, 248, 1);
        }
      }
    }
  }

  .label_42 {
    width: 1.6rem;
    height: 1.6rem;
    margin: 0.8rem 0 0 8.48rem;
  }

  .demo-image-container {
    position: absolute;
    bottom: 1.334rem;
    right: 1.334rem;

    .demo-image {
      width: 20rem;
      height: 13rem;
      object-fit: contain;
      border-radius: 8px;
    }
  }
}

/* 大模型特殊右侧样式 */
.model-demo {
  border-radius: 0.213rem;
  height: 13.174rem;
  width: 17.867rem;
  margin: 0.32rem 0.374rem 0 0.32rem;

  .demo-outer-container {
    box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
    border-radius: 0.213rem;
    background-image: url(/pages/assets/img/fd2e0fd105a5488db2b2e0b65fe1ca55_mergeImage.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    height: 9.92rem;
    border: 1px solid rgba(227, 227, 227, 1);
    width: 15.44rem;
    margin: 1.067rem 0 0 0.694rem;

    .demo-inner-container {
      box-shadow: 0px 2px 9px 0px rgba(194, 194, 194, 0.5);
      border-radius: 0.213rem;
      background-image: url(/pages/assets/img/2ea6bcde74804597b0a0bdcfd8ab52f7_mergeImage.png);
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      width: 9.627rem;
      height: 6.267rem;
      border: 1px solid rgba(227, 227, 227, 1);
      margin: 4.267rem 0 0 6.774rem;
    }
  }
}
