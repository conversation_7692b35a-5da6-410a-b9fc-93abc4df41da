<template>
  <div class="page flex-col">
    <div class="box_21 flex-col">
      <header class="group_3 flex-col">
        <img
          class="image_2"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png"
          alt="AI医助产品标志 - 慧医云科技智能医疗助手"
          width="200"
          height="150"
          loading="lazy"
        />
        <div class="text-wrapper_1">
          <h1 class="paragraph_1">
            AI医助
            <br />
          </h1>
          <p class="text_7">专为医疗场景打造的智能辅助工具</p>
        </div>
        <p class="text_8">致力于解决医生群体长期面临的文书负担与效率提升</p>
      </header>
      <div class="group_4 flex-col"></div>
      <section id="functions" class="group_5 flex-col">
        <h2 class="text_9">产品功能</h2>
        <nav class="text-wrapper_23 flex-row justify-between" role="tablist" aria-label="产品功能导航">
          <button
            class="text_10"
            :class="{ active: activeFunctionTab === 'voice' }"
            @click="setActiveFunctionTab('voice')"
            role="tab"
            :aria-selected="activeFunctionTab === 'voice'"
            aria-controls="voice-content"
          >智能语音病历生成​</button>
          <button
            class="text_11"
            :class="{ active: activeFunctionTab === 'surgery' }"
            @click="setActiveFunctionTab('surgery')"
            role="tab"
            :aria-selected="activeFunctionTab === 'surgery'"
            aria-controls="surgery-content"
          >手术记录自动化引擎</button>
          <button
            class="text_12"
            :class="{ active: activeFunctionTab === 'rounds' }"
            @click="setActiveFunctionTab('rounds')"
            role="tab"
            :aria-selected="activeFunctionTab === 'rounds'"
            aria-controls="rounds-content"
          >AI数字化查房助手</button>
          <button
            class="text_13"
            :class="{ active: activeFunctionTab === 'security' }"
            @click="setActiveFunctionTab('security')"
            role="tab"
            :aria-selected="activeFunctionTab === 'security'"
            aria-controls="security-content"
          >隐私安全与多系统融合</button>
          <button
            class="text_14"
            :class="{ active: activeFunctionTab === 'multiterm' }"
            @click="setActiveFunctionTab('multiterm')"
            role="tab"
            :aria-selected="activeFunctionTab === 'multiterm'"
            aria-controls="multiterm-content"
          >多终端跨场景灵活应用​</button>
        </nav>
        <div class="section_3 flex-col" :class="activeFunctionTab ? 'underline-' + activeFunctionTab : ''"></div>

        <article
          v-show="activeFunctionTab === 'voice'"
          class="box_22 flex-row justify-between"
          id="voice-content"
          role="tabpanel"
          aria-labelledby="voice-tab"
        >
          <div class="box_1 flex-col">
            <h3 class="text_15">智能语音病历生成​</h3>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
              alt="智能语音病历生成功能图标"
              width="80"
              height="80"
              loading="lazy"
            />
            <p class="paragraph_2">基于医疗级语音识别与深度学习技术，实时捕捉医患对话内容：
              <br /><br />
              ·自动提取主诉、病史、诊断等关键信息；
              <br />
              ·12秒内生成符合临床规范的结构化电子病历；
              <br />
              ·1并一键同步至医院电子病历系统；
              <br /><br />
            </p>
          </div>
          <div class="box_2 flex-col">
            <img
              src="/pages/assets/img/d0e19f3ca15f41dcb4ab4c8ab7e4829c_mergeImage.png"
              alt="智能语音病历生成界面演示图 - 显示语音转文字和病历自动生成过程"
              style="width: 100%; height: 100%; object-fit: cover;"
              width="500"
              height="400"
              loading="lazy"
            />
          </div>
        </article>

        <article
          v-show="activeFunctionTab === 'surgery'"
          class="box_22 flex-row justify-between"
          id="surgery-content"
          role="tabpanel"
          aria-labelledby="surgery-tab"
        >
          <div class="box_1 flex-col">
            <h3 class="text_15">手术记录自动化引擎​​</h3>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
              alt="手术记录自动化引擎功能图标"
              width="80"
              height="80"
              loading="lazy"
            />
            <p class="paragraph_2">通过自然语言处理（NLP）精准识别外科医生口述的手术步骤、操作细节及术中事件，自动生成标准化手术记录，同步归档至电子病历系统。</p>
          </div>
          <div class="box_2 flex-col">
            <img
              src="/pages/assets/img/c3ef2d05d43f4524902b09c943995492_mergeImage.png"
              alt="手术记录自动化引擎界面演示图 - 显示手术记录自动生成和归档过程"
              style="width: 100%; height: 100%; object-fit: cover;"
              width="500"
              height="400"
              loading="lazy"
            />
          </div>
        </article>

        <article
          v-show="activeFunctionTab === 'rounds'"
          class="box_22 flex-row justify-between"
          id="rounds-content"
          role="tabpanel"
          aria-labelledby="rounds-tab"
        >
          <div class="box_1 flex-col">
            <h3 class="text_15">AI数字化查房助手</h3>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
              alt="AI数字化查房助手功能图标"
              width="80"
              height="80"
              loading="lazy"
            />
            <p class="paragraph_2">整合AI语音交互与体征数据采集功能，实时记录查房过程中患者的主诉、用药反馈及生命体征，自动生成结构化查房报告并推送至医护终端。</p>
          </div>
          <div class="box_2 flex-col">
            <img
              src="/pages/assets/img/3935f3c8e57a41aa92c82dbce20e6631_mergeImage.png"
              alt="AI数字化查房助手界面演示图 - 显示查房记录和报告生成过程"
              style="width: 100%; height: 100%; object-fit: cover;"
              width="500"
              height="400"
              loading="lazy"
            />
          </div>
        </article>

        <article
          v-show="activeFunctionTab === 'security'"
          class="box_22 flex-row justify-between"
          id="security-content"
          role="tabpanel"
          aria-labelledby="security-tab"
        >
          <div class="box_1 flex-col">
            <h3 class="text_15">隐私安全与多系统融合</h3>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
              alt="隐私安全与多系统融合功能图标"
              width="80"
              height="80"
              loading="lazy"
            />
            <p class="paragraph_2">采用数据脱敏、封闭式存储及匿名化技术，确保患者隐私零泄露；通过API+RPA无缝对接医院HIS、EMR等系统，消除跨平台重复录入问题，30秒内完成信息跨系统同步。</p>
          </div>
          <div class="box_2 flex-col">
            <img
              src="/pages/assets/img/1c605964f6ba4177b7556ac17a96a9fb_mergeImage.png"
              alt="隐私安全与多系统融合界面演示图 - 显示数据安全和系统集成功能"
              style="width: 100%; height: 100%; object-fit: cover;"
              width="500"
              height="400"
              loading="lazy"
            />
          </div>
        </article>

        <article
          v-show="activeFunctionTab === 'multiterm'"
          class="box_22 flex-row justify-between"
          id="multiterm-content"
          role="tabpanel"
          aria-labelledby="multiterm-tab"
        >
          <div class="box_1 flex-col">
            <h3 class="text_15">多终端跨场景灵活应用​</h3>
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngf60fad07063dc82eefc88dffdcb5980639b551bd0b5e977bcd786a8c89c5d156.png"
              alt="多终端跨场景灵活应用功能图标"
              width="80"
              height="80"
              loading="lazy"
            />
            <p class="paragraph_2">适配电脑、平板、手机等多设备，支持诊室、手术室、病房及移动办公场景，医生可随时随地调用AI医助功能，实现诊疗全流程数字化管理。</p>
          </div>
          <div class="box_2 flex-col">
            <img
              src="/pages/assets/img/19273847ef094c2a9b0e1621205912c2_mergeImage.png"
              alt="多终端跨场景灵活应用界面演示图 - 显示多设备适配和场景应用"
              style="width: 100%; height: 100%; object-fit: cover;"
              width="500"
              height="400"
              loading="lazy"
            />
          </div>
        </article>
      </section>
      <section class="group_6 flex-col">
        <nav class="group_7 flex-row" role="navigation" aria-label="页面内导航">
          <button class="text-wrapper_3 flex-col" :class="{ active: activeNav === 'about' }" @click="scrollToSection('about')"><span class="text_16">关于ai医助</span></button>
          <button class="text-wrapper_4 flex-col" :class="{ active: activeNav === 'functions' }" @click="scrollToSection('functions')"><span class="text_17">产品功能</span></button>
          <button class="text-wrapper_5 flex-col" :class="{ active: activeNav === 'highlights' }" @click="scrollToSection('highlights')"><span class="text_18">产品亮点</span></button>
          <button class="text-wrapper_6 flex-col" :class="{ active: activeNav === 'applications' }" @click="scrollToSection('applications')"><span class="text_19">应用场景</span></button>
        </nav>

        <section id="about" class="text-group_12 flex-col justify-between">
          <h2 class="text_20">关于AI医助</h2>
          <p class="text_21">
            AI医助是专为医疗场景打造的智能辅助工具，致力于解决医生群体长期面临的文书负担与效率提升。
          </p>
        </section>

        <div class="group_27 flex-row justify-between">
          <article class="box_3 flex-col">
            <img
              class="image_4"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPng328df33b129b6ef009a6157de83eda54079bd37c65e4f55112dec2c21151e558.png"
              alt="智能语音交互功能演示 - 医患对话实时转换为结构化病历"
              width="300"
              height="200"
              loading="lazy"
            />
            <div class="text-wrapper_7">
              <p class="text_22">通过智能语音交互和自然语言处理技术，系统可实时捕捉医患对话，</p>
              <strong class="text_23">2秒内自动生成结构化电子病历</strong>
              <p class="paragraph_3">
                ，较传统方式节省75%书写时间。
                <br />
              </p>
            </div>
          </article>

          <article class="box_4 flex-col">
            <img
              class="image_5"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPngcfb75c785045fec82f2f9c383995c69e138f4fd4a206da5c3d4b40c73ae9fddb.png"
              alt="智能预检模块界面 - 患者症状信息收集和诊疗决策辅助"
              width="300"
              height="200"
              loading="lazy"
            />
            <div class="text-wrapper_8">
              <p class="text_24">智能预检模块能主动引导患者描述病情，结合诊疗决策辅助功能，</p>
              <p class="paragraph_4">
                帮助医生快速锁定关键信息，使门诊接诊效率提升1.5倍。
                <br />
              </p>
            </div>
          </article>

          <article class="box_5 flex-col">
            <img
              class="image_6"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPng2c42d9ebf24f6d3809de600e3fd2deeb5375886e5bee0c48833d52a5c1ab69ac.png"
              alt="医患沟通场景图 - 面对面交流无屏幕阻隔"
              width="300"
              height="200"
              loading="lazy"
            />
            <p class="paragraph_5">
              有效解放医生的双手和屏幕阻隔，让诊疗过程回归面对面的人文交流，有效提升医患之间的关系。
              <br />
            </p>
          </article>
        </div>
      </section>

      <div class="text-wrapper_9 flex-col">
        <p class="paragraph_6">
          让医院实现45%的病历质量提升，助力医生将核心精力回归临床决策，
          <br />
          推动医疗行业向智能化、人性化方向革新。
        </p>
      </div>

      <section id="applications" class="group_9 flex-col">
        <div class="text-wrapper_24 flex-row"><h2 class="text_25">应用场景</h2></div>
        <div class="group_28 flex-row">
          <article class="box_6 flex-col">
            <div class="text-group_13 flex-col justify-between">
              <h3 class="text_26">AI医助</h3>
              <p class="paragraph_7">
                让医生更注重患者
                <br />
                而不是写病历
              </p>
            </div>
            <div class="block_2 flex-row">
              <div class="section_6 flex-col"></div>
              <span class="text_27">智能病历</span>
            </div>
            <div class="text-wrapper_11 flex-col"><span class="text_28">智能术计</span></div>
            <div class="text-wrapper_12 flex-col"><span class="text_29">智能查房</span></div>
            <img
              class="image_7"
              referrerpolicy="no-referrer"
              src="/pages/assets/img/SketchPng66ee60e592e1a0851d8f5068c0d5257fc26751e7d9167eb7e5d437c8e0bca808.png"
              alt="AI医助应用场景图标 - 智能病历、智能术计、智能查房功能展示"
              width="250"
              height="300"
              loading="lazy"
            />
          </article>
          <div class="box_7 flex-col">
            <div class="image-wrapper_1 flex-col">
              <img
                class="image_8"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng0a1dcfae6159c1c2dcd8a85670b9d50bd1b0fc079fb7a7c2a33bfd33b3a8cdf9.png"
                alt="AI医助产品应用场景全景图 - 展示医院各科室使用情况"
                width="400"
                height="350"
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </section>

      <section id="highlights" class="group_10 flex-col">
        <div class="text-group_14 flex-col justify-between">
          <h2 class="text_30">产品亮点</h2>
          <p class="text_31">支持api、rpa两种emr系统对接方式，数据实时同步传输</p>
        </div>
        <div class="grid_2">
          <article class="group_11 flex-col">
            <div class="text-wrapper_25 flex-row"><h3 class="text_32">专注患者，而非病历</h3></div>
            <div class="block_7 flex-row justify-between">
              <div class="image-text_19 flex-row">
                <div class="image-text_20 flex-row">
                  <div class="image-text_21 flex-row justify-between">
                    <div class="block_3 flex-col"></div>
                    <p class="paragraph_8">
                      实时捕捉诊疗对话，自动生成结构化病历
                      <br />
                      智能提取关键信息，减少手动录入
                      <br />
                      让医生更专注于患者沟通与临床决策
                    </p>
                  </div>
                  <div class="block_4 flex-col"></div>
                </div>
                <div class="block_5 flex-col"></div>
              </div>
              <img
                class="image_9"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPngc0b0b74d9b33fc744bd26e8a9c3ac05244cfe912ff6e4162e5b55d5b602fe933.png"
                alt="专注患者而非病历功能图示 - 医生与患者交流场景"
                width="200"
                height="150"
                loading="lazy"
              />
            </div>
            <div class="group_13 flex-col"></div>
          </article>

          <article class="group_14 flex-col">
            <div class="text-wrapper_26 flex-row"><h3 class="text_33">优化病历书写体验</h3></div>
            <div class="group_29 flex-row justify-between">
              <div class="image-text_22 flex-row">
                <div class="image-text_23 flex-row justify-between">
                  <div class="group_15 flex-col"></div>
                  <p class="paragraph_9">
                    智能问诊/预检，预先收集患者症状信息
                    <br />
                    杜绝"模板套用"，呈现完整诊疗细节
                  </p>
                </div>
                <div class="box_9 flex-col"></div>
              </div>
              <img
                class="image_10"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng2965ac604003d6ec947b473dc03ed679a93fcb0976cd641fc8d7366039edcd91.png"
                alt="优化病历书写体验功能图示 - 智能问诊和病历生成界面"
                width="200"
                height="150"
                loading="lazy"
              />
            </div>
            <div class="box_10 flex-col"></div>
          </article>

          <article class="group_16 flex-col">
            <div class="text-wrapper_27 flex-row"><h3 class="text_34">医患沟通更显温度</h3></div>
            <div class="section_11 flex-row justify-between">
              <div class="image-text_24 flex-row">
                <div class="image-text_25 flex-row justify-between">
                  <div class="group_17 flex-col"></div>
                  <p class="paragraph_10">
                    支持语音录入，无需"边问边写"
                    <br />
                    告别"屏幕阻隔"，实现更多面对面交流
                  </p>
                </div>
                <div class="box_12 flex-col"></div>
              </div>
              <img
                class="image_11"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng2048cf6eabdd591a84c16f234edb0e5d3bf9a8f831f93f30a44b18480aba8308.png"
                alt="医患沟通更显温度功能图示 - 面对面交流无屏幕阻隔"
                width="200"
                height="150"
                loading="lazy"
              />
            </div>
            <div class="box_13 flex-col"></div>
          </article>

          <article class="group_18 flex-col">
            <div class="text-wrapper_28 flex-row"><h3 class="text_35">智能化的多角色交互</h3></div>
            <div class="section_12 flex-row justify-between">
              <div class="image-text_26 flex-row">
                <div class="image-text_27 flex-row">
                  <div class="image-text_28 flex-row justify-between">
                    <div class="group_19 flex-col"></div>
                    <p class="paragraph_11">
                      AI主动引导患者描述病情
                      <br />
                      AI智能辅助医生诊疗决策
                      <br />
                      AI实时分析医患对话内容
                    </p>
                  </div>
                  <div class="box_15 flex-col"></div>
                </div>
                <div class="block_6 flex-col"></div>
              </div>
              <div class="image-wrapper_2 flex-col">
                <img
                  class="image_12"
                  referrerpolicy="no-referrer"
                  src="/pages/assets/img/SketchPng5cc065844e3f947d4f9f26f7a5e4a35e1a926eeb7285dd8052b41f1f8a4ae5ac.png"
                  alt="智能化多角色交互功能图示 - AI引导、辅助和分析功能展示"
                  width="200"
                  height="150"
                  loading="lazy"
                />
              </div>
            </div>
          </article>

          <article class="group_20 flex-col">
            <div class="text-wrapper_29 flex-row"><h3 class="text_36">严格保护患者隐私</h3></div>
            <div class="block_8 flex-row justify-between">
              <div class="image-text_29 flex-row">
                <div class="image-text_30 flex-row justify-between">
                  <div class="box_16 flex-col"></div>
                  <p class="paragraph_12">
                    采用数据脱敏与匿名化处理技术
                    <br />
                    封闭式数据管理，确保信息安全
                  </p>
                </div>
                <div class="section_8 flex-col"></div>
              </div>
              <img
                class="image_13"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng597ad43225818972fb96a38c07ea193e9adbfda9cff17c7be5c04522fb30750f.png"
                alt="患者隐私保护功能图示 - 数据脱敏和安全管理"
                width="200"
                height="150"
                loading="lazy"
              />
            </div>
          </article>

          <article class="group_21 flex-col">
            <div class="text-wrapper_30 flex-row"><h3 class="text_37">丰富的&nbsp;EMR&nbsp;集成技术</h3></div>
            <div class="box_23 flex-row justify-between">
              <div class="image-text_31 flex-row">
                <div class="image-text_32 flex-row justify-between">
                  <div class="group_23 flex-col"></div>
                  <p class="paragraph_13">
                    API（应用程序接口）
                    <br />
                    RPA（机器人流程自动化）
                  </p>
                </div>
                <div class="box_17 flex-col"></div>
              </div>
              <img
                class="image_14"
                referrerpolicy="no-referrer"
                src="/pages/assets/img/SketchPng8ac9a02b711b86c6ee476b717851752d6147ca16c81dead436687ba47d3e9477.png"
                alt="EMR集成技术图示 - API和RPA系统对接示意图"
                width="200"
                height="150"
                loading="lazy"
              />
            </div>
          </article>
        </div>
      </section>

      <section class="group_24 flex-row" aria-label="产品效果数据统计">
        <div class="text-group_15 flex-col justify-between">
          <div class="text-wrapper_19">
            <span class="text_38">2</span>
            <span class="text_39">秒</span>
          </div>
          <p class="text_40">生成电子病历</p>
        </div>
        <div class="text-group_16 flex-col justify-between">
          <div class="text-wrapper_20">
            <span class="text_41">75</span>
            <span class="text_42">%</span>
          </div>
          <p class="text_43">节省病历书写时间</p>
        </div>
        <div class="text-group_17 flex-col justify-between">
          <div class="text-wrapper_21">
            <span class="text_44">45</span>
            <span class="text_45">%</span>
          </div>
          <p class="text_46">提升病历质量</p>
        </div>
        <div class="text-group_18 flex-col justify-between">
          <div class="text-wrapper_22">
            <span class="text_47">75</span>
            <span class="text_48">%</span>
          </div>
          <p class="text_49">提升患者服务数量</p>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const activeNav = ref('')
const activeFunctionTab = ref('voice')

const setActiveFunctionTab = (tab) => {
  activeFunctionTab.value = tab
}

const scrollToSection = (sectionId) => {
  activeNav.value = sectionId
  const element = document.getElementById(sectionId)
  if (element) {
    const headerHeight = 82 // 固定头部高度（rem转px）
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset
    const offsetPosition = elementPosition - headerHeight

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    })
  }

  // 2秒后清除活动状态
  setTimeout(() => {
    activeNav.value = ''
  }, 2000)
}

// 结构化数据定义
const structuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "AI医助",
  "description": "专为医疗场景打造的智能辅助工具，致力于解决医生群体长期面临的文书负担与效率提升",
  "applicationCategory": "医疗软件",
  "operatingSystem": "跨平台",
  "offers": {
    "@type": "Offer",
    "category": "医疗AI"
  },
  "author": {
    "@type": "Organization",
    "name": "四川慧医云科技有限公司"
  },
  "featureList": [
    "智能语音病历生成",
    "手术记录自动化引擎",
    "AI数字化查房助手",
    "隐私安全与多系统融合",
    "多终端跨场景灵活应用"
  ],
  "screenshots": "/pages/assets/img/SketchPng96d042623f94647948f434b31e8adf406da2ca802c9aa7ddff1f06e967470ed7.png"
}

useHead({
  title: 'AI医助 - 慧医云科技 | 专为医疗场景打造的智能辅助工具',
  meta: [
    { name: 'description', content: 'AI医助是慧医云科技专为医疗场景打造的智能辅助工具，致力于解决医生群体长期面临的文书负担与效率提升。2秒内自动生成结构化电子病历，节省75%书写时间，提升医患沟通体验。' },
    { name: 'keywords', content: 'AI医助,智能病历,语音识别,医疗AI,电子病历,医患沟通,诊疗效率,智能预检,医疗科技,慧医云科技' },
    { name: 'author', content: '四川慧医云科技有限公司' },
    { name: 'robots', content: 'index,follow' },
    { property: 'og:title', content: 'AI医助 - 慧医云科技 | 专为医疗场景打造的智能辅助工具' },
    { property: 'og:description', content: 'AI医助是专为医疗场景打造的智能辅助工具，2秒内自动生成结构化电子病历，节省75%书写时间，提升医患沟通体验。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'http://www.huiyiyunai.com/products/aiyizhu' },
    { property: 'og:image', content: '/pages/assets/img/SketchPng96d042623f94647948f434b31e8adf406da2ca802c9aa7ddff1f06e967470ed7.png' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
    { name: 'theme-color', content: '#1976d2' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
    { name: 'format-detection', content: 'telephone=no' }
  ],
  link: [
    { rel: 'canonical', href: 'http://www.huiyiyunai.com/products/aiyizhu' },
    { rel: 'preload', href: '/pages/assets/img/SketchPng2ad04a8a3674f7a605f9ffff052ef4a4d2270e4e360d9d245aa32f552440e71c.png', as: 'image' }
  ],
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify(structuredData)
    }
  ]
})

definePageMeta({
  title: 'AI医助',
  description: '专为医疗场景打造的智能辅助工具',
  layout: 'default'
})
</script>

<style scoped lang="less" src="./assets/aiyizhu.rem.less" />
