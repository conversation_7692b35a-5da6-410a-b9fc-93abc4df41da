<template>
  <div class="page flex-col">
    <div class="group_4 flex-col">
      <div class="text-wrapper_1 flex-col">
        <span class="text_7">联系我们</span>
        <span class="text_8">把握时代趋势，拥抱行业变革</span>
      </div>
      <div class="text-wrapper_2 flex-row justify-between">
        <span class="text_9">联系方式</span>
        <span class="text_10">Contact&nbsp;information</span>
      </div>
      <div class="block_2 flex-col">
        <div class="group_5 flex-row justify-between">
          <div class="group_6 flex-col"></div>
          <span class="text_11" itemscope itemtype="http://schema.org/Organization" itemprop="name">四川慧医云科技（总部）</span>
        </div>
        <div class="group_7 flex-row justify-between">
          <img
            class="label_1"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng932e75f95f0e62d1afebef62e9ce92178914f09aeeab540c25263ceca6ea4285.png"
          />
          <div class="text-wrapper_3">
            <span class="paragraph_1">
              地址
              <br />
            </span>
            <span class="text_12" itemscope itemtype="http://schema.org/PostalAddress" itemprop="streetAddress">四川省成都市武侯区高攀路26号-1</span>
          </div>
        </div>
        <div class="image-wrapper_1 flex-row">
          <img
            class="image_2"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngcb8c5941cbadb0b7b3fff1d2b489e75094c650d2668196fe6aed7f909842c8fd.png"
          />
        </div>
        <div class="group_8 flex-row justify-between">
          <img
            class="label_2"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng563fad8923a6052b23941fa9bb98659806b012094aec18744c93ea9af1801a15.png"
          />
          <div class="text-wrapper_4">
            <span class="paragraph_2">
              电话
              <br />
            </span>
            <span class="text_13" itemscope itemtype="http://schema.org/Organization" itemprop="telephone">18848457100</span>
          </div>
        </div>
        <div class="image-wrapper_2 flex-row">
          <img
            class="image_3"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngcb8c5941cbadb0b7b3fff1d2b489e75094c650d2668196fe6aed7f909842c8fd.png"
          />
        </div>
        <div class="group_9 flex-row justify-between">
          <img
            class="label_3"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng9d02c2a4b598e609fa70b0cc1183fdc5815f7f8de7857c15261d7a1556ed6583.png"
          />
          <div class="text-wrapper_5">
            <span class="paragraph_3">
              邮箱
              <br />
            </span>
            <span class="text_14" itemscope itemtype="http://schema.org/Organization" itemprop="email">yuanzhiqi_ai&#64;163.com</span>
          </div>
        </div>
      </div>
      <div class="block_3 flex-col"><div class="group_10 flex-col"></div></div>
    </div>
  </div>
</template>

<script setup>
// 页面头部信息 - 完整SEO配置
useHead({
  title: '联系我们 - 慧医云科技 | 四川慧医云科技有限公司',
  meta: [
    { name: 'description', content: '联系四川慧医云科技有限公司，获取专业的AI医疗解决方案服务。地址：四川省成都市武侯区高攀路26号-1，电话：18848457100，邮箱：<EMAIL>。' },
    { name: 'keywords', content: '联系我们,四川慧医云科技,慧医云科技联系方式,AI医疗解决方案,元智启AI,成都医疗科技公司,医疗AI服务商' },
    { name: 'author', content: '四川慧医云科技有限公司' },
    { name: 'robots', content: 'index,follow' },
    { name: 'googlebot', content: 'index,follow' },
    { name: 'baiduspider', content: 'index,follow' },

    // Open Graph标签
    { property: 'og:title', content: '联系我们 - 慧医云科技 | 四川慧医云科技有限公司' },
    { property: 'og:description', content: '联系四川慧医云科技有限公司，获取专业的AI医疗解决方案服务。地址：四川省成都市武侯区高攀路26号-1，电话：18848457100。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://www.huiyiyunai.com/contact' },
    { property: 'og:image', content: 'https://www.huiyiyunai.com/og-image.jpg' },
    { property: 'og:site_name', content: '慧医云科技' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '联系我们 - 慧医云科技' },
    { name: 'twitter:description', content: '联系四川慧医云科技有限公司，获取专业的AI医疗解决方案服务。' },
    { name: 'twitter:image', content: 'https://www.huiyiyunai.com/twitter-image.jpg' },

    // 移动端优化
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
    { name: 'format-detection', content: 'telephone=no' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },

    // 地理位置标签
    { name: 'geo.region', content: 'CN-SC' },
    { name: 'geo.placename', content: '成都市' },
    { name: 'geo.position', content: '30.6586;104.0647' },
    { name: 'ICBM', content: '30.6586, 104.0647' }
  ],
  link: [
    { rel: 'canonical', href: 'https://www.huiyiyunai.com/contact' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://www.huiyiyunai.com/contact' }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "ContactPage",
        "name": "联系我们 - 四川慧医云科技有限公司",
        "url": "https://www.huiyiyunai.com/contact",
        "mainEntity": {
          "@type": "Organization",
          "name": "四川慧医云科技有限公司",
          "alternateName": "慧医云科技",
          "url": "https://www.huiyiyunai.com",
          "logo": "https://www.huiyiyunai.com/logo.png",
          "contactPoint": [
            {
              "@type": "ContactPoint",
              "telephone": "+86-188-4845-7100",
              "contactType": "customer service",
              "email": "<EMAIL>",
              "availableLanguage": ["Chinese", "zh-CN"],
              "areaServed": "CN"
            },
            {
              "@type": "ContactPoint",
              "telephone": "+86-188-4845-7100",
              "contactType": "sales",
              "email": "<EMAIL>",
              "availableLanguage": ["Chinese", "zh-CN"],
              "areaServed": "CN"
            }
          ],
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "高攀路26号-1",
            "addressLocality": "成都市",
            "addressRegion": "四川省",
            "postalCode": "610000",
            "addressCountry": "CN"
          },
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": "30.6586",
            "longitude": "104.0647"
          },
          "sameAs": [
            "https://www.huiyiyunai.com"
          ],
          "description": "聚焦医药及医疗行业，提供AI深度业务应用的解决方案服务商",
          "foundingDate": "2020",
          "industry": "医疗科技",
          "keywords": "AI医疗,元智启AI,AI医助,AI患助,智能营销作战中心",
          "openingHours": "Mo-Fr 09:00-18:00",
          "priceRange": "$$"
        },
        "breadcrumb": {
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "首页",
              "item": "https://www.huiyiyunai.com"
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "联系我们",
              "item": "https://www.huiyiyunai.com/contact"
            }
          ]
        }
      })
    }
  ]
})
</script>

<style scoped lang="less">
.page {
  background-color: rgba(243, 245, 249, 1);
  position: relative;
  width: 51.2rem;
  overflow: hidden;
  padding-top: 2.187rem;
  // 面包屑导航样式
  .breadcrumb-nav {
    background-color: #f8f9fa;
    padding: 0.533rem 0;
    border-bottom: 1px solid #e9ecef;
    margin-top: 2.187rem;

    .breadcrumb-container {
      max-width: 40rem;
      margin: 0 auto;
      padding: 0 1.333rem;
      font-size: 0.373rem;
      color: #6c757d;

      a {
        color: #007bff;
        text-decoration: none;
        transition: color 0.3s ease;

        &:hover {
          color: #0056b3;
          text-decoration: underline;
        }
      }

      .breadcrumb-separator {
        margin: 0 0.267rem;
        color: #adb5bd;
      }

      span:last-child {
        color: #495057;
        font-weight: 500;
      }
    }
  }

  .group_4 {
    position: relative;
    width: 51.2rem;
    height: 29.333rem;
    margin-top: 0;

    .text-wrapper_1 {
      background-image: url(/pages/assets/img/797e26d4e173497cb646b19c6d2903f2_mergeImage.png);
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
      width: 51.2rem;
      height: 9.067rem;
      justify-content: flex-center;
      display: flex;
      flex-direction: column;

      .text_7 {
        width: 6.4rem;
        height: 2.24rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 1.6rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 2.24rem;
        margin:2.934rem 0 0 10.587rem;

      }

      .text_8 {
        width: 6.24rem;
        height: 0.667rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.48rem;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.667rem;
        margin: 0.32rem 0 2.907rem 10.5rem;
      }
    }

    .text-wrapper_2 {
      width: 12.907rem;
      height: 1.28rem;
      margin: 3.173rem 0 0 7.947rem;

      .text_9 {
        width: 4.267rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 1.067rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 1.28rem;
      }

      .text_10 {
        width: 8.107rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(102, 111, 131, 1);
        font-size: 0.533rem;
        letter-spacing: 0.0824rem;
        text-transform: uppercase;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin-top: 0.613rem;
      }
    }

    .block_2 {
      height: 11.787rem;
      background: url(/pages/assets/img/SketchPngbaddfc642d6331b2f98d774d48f87f14ef2e6269962721926475c5291486f14a.png) 100% no-repeat;
      background-size: 100% 100%;
      width: 20.453rem;
      margin: 2.133rem 0 1.893rem 7.947rem;

      .group_5 {
        width: 8.32rem;
        height: 0.64rem;
        margin-top: 1.333rem;

        .group_6 {
          background-color: rgba(33, 135, 250, 1);
          width: 0.107rem;
          height: 0.64rem;
        }

        .text_11 {
          width: 7.04rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.64rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 0.64rem;
          margin-left: 1.28rem;
        }
      }

      .group_7 {
        width: 8.827rem;
        height: 1.707rem;
        margin: 1.547rem 0 0 1.28rem;

        .label_1 {
          width: 0.747rem;
          height: 0.747rem;
          margin-top: 0.4rem;
        }

        .text-wrapper_3 {
          width: 7.307rem;
          height: 1.707rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: justify;
          line-height: 0.853rem;
          margin-left: 0.773rem;

          .paragraph_1 {
            width: 7.307rem;
            height: 1.707rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            line-height: 0.853rem;
          }

          .text_12 {
            width: 7.307rem;
            height: 1.707rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.853rem;
          }
        }
      }

      .image-wrapper_1 {
        width: 9.52rem;
        height: 0.027rem;
        margin: 0.507rem 0 0 1.28rem;

        .image_2 {
          width: 9.52rem;
          height: 0.027rem;
        }
      }

      .group_8 {
        width: 4.48rem;
        height: 1.707rem;
        margin: 0.533rem 0 0 1.28rem;

        .label_2 {
          width: 0.747rem;
          height: 0.747rem;
          margin-top: 0.4rem;
        }

        .text-wrapper_4 {
          height: 1.707rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: justify;
          line-height: 0.853rem;
          margin-left: 0.773rem;

          .paragraph_2 {
            width: 2.96rem;
            height: 1.707rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            line-height: 0.853rem;
          }

          .text_13 {
            width: 2.96rem;
            height: 1.707rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.853rem;
          }
        }
      }

      .image-wrapper_2 {
        width: 9.52rem;
        height: 0.027rem;
        margin: 0.507rem 0 0 1.28rem;

        .image_3 {
          width: 9.52rem;
          height: 0.027rem;
        }
      }

      .group_9 {
        width: 6.507rem;
        height: 1.707rem;
        margin: 0.533rem 0 1.013rem 1.28rem;

        .label_3 {
          width: 0.747rem;
          height: 0.747rem;
          margin-top: 0.4rem;
        }

        .text-wrapper_5 {
          width: 5.987rem;
          height: 1.707rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: justify;
          line-height: 0.853rem;
          margin-left: 0.773rem;

          .paragraph_3 {
            width: 4.987rem;
            height: 1.707rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            line-height: 0.853rem;
          }

          .text_14 {
            width: 4.987rem;
            height: 1.707rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.853rem;
          }
        }
      }
    }

    .block_3 {
      height: 15.173rem;
      background: url(/pages/assets/img/846b4fbd8d8f434f820f7cdc4989b43e_mergeImage.png) 100% no-repeat;
      background-size: 100% 100%;
      width: 15.147rem;
      position: absolute;
      left: 28.107rem;
      top: 9.813rem;

      .group_10 {
        border-radius: 0.32rem;
        background-image: url(/pages/assets/img/149413b47f884833a8d5139c3eea4149_mergeImage.png);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
        width: 16.533rem;
        height: 9.333rem;
        border: 1px solid rgba(255, 255, 255, 1);
        margin: 7.387rem 0 0 -4.693rem;
      }
    }
  }
}
</style>
