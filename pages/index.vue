<template>
  <!-- 加载状态 -->
  <div v-if="isLoading" class="loading-container">
    <div class="loading-spinner">加载中...</div>
  </div>

  <!-- 错误状态 -->
  <div v-else-if="hasError" class="error-container">
    <h1>页面加载失败</h1>
    <p>抱歉，页面暂时无法加载，请稍后再试。</p>
    <button @click="$router.go(0)">重新加载</button>
  </div>

  <!-- 主要内容 -->
  <main v-else class="page flex-col justify-between" role="main">
    <!-- 面包屑导航 -->
    <nav v-show="false" class="breadcrumb" aria-label="面包屑导航" style="padding: 1rem 2rem; background: rgba(248, 249, 250, 1);">
      <ol itemscope itemtype="https://schema.org/BreadcrumbList" style="display: flex; list-style: none; margin: 0; padding: 0; font-size: 0.875rem;">
        <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
          <a itemprop="item" href="/" style="color: #2187FA; text-decoration: none;">
            <span itemprop="name">首页</span>
          </a>
          <meta itemprop="position" content="1" />
        </li>
      </ol>
    </nav>

    <!-- Hero Section -->
    <section class="group_3 flex-col" aria-labelledby="hero-title">
      <div class="text-group_22 flex-col justify-between">
        <div class="text-wrapper_1">
          <h1 id="hero-title" class="text_7">聚焦医药及医疗行业</h1>
          <span class="paragraph_1"><br /></span>
          <h2 class="text_8">提供AI深度业务应用的解决方案服务商</h2>
        </div>
        <p class="text_9">
          A&nbsp;solution&nbsp;provider&nbsp;focusing&nbsp;on&nbsp;deep&nbsp;AI&nbsp;business&nbsp;applications&nbsp;in&nbsp;the&nbsp;pharmaceutical&nbsp;and&nbsp;medical&nbsp;industry.
        </p>
      </div>
      <img
        class="image_2"
        referrerpolicy="no-referrer"
        src="/pages/assets//img/SketchPngc898e09bb13c75ce286739d40a534715f555634bdd06c306f26fbe643cf82308.png"
        alt="慧医云科技AI医疗解决方案展示图 - 展现企业在医疗行业的AI技术应用"
        loading="lazy"
        width="544"
        height="11"
      />
    </section>

    <!-- Core Products Section -->
    <section class="group_4 flex-col" aria-labelledby="products-title">
      <header class="text-group_23 flex-col justify-between">
        <h2 id="products-title" class="text_10">核心产品</h2>
        <p class="text_11">企业级AI应用配置平台，支持零代码搭建多种智能应用</p>
      </header>

      <article class="section_7 flex-row justify-between">
        <div class="section_1 flex-col justify-center align-center"><div class="box_1 flex-col"></div></div>
        <div class="group_16 flex-col">
          <div class="group_17 flex-row justify-between">
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="/pages/assets//img/SketchPng1fcd51b44ce460072c341a539248e193fc8c7612672c0b02918d18d67e0319f2.png"
              alt="元智启AI企业级应用配置平台品牌标识"
              loading="lazy"
              width="120"
              height="120"
            />
            <div class="text-wrapper_2">
              <span class="text_12">元智启ai</span>
              <span class="text_13">-企业级AI应用配置平台</span>
            </div>
          </div>
          <p class="paragraph_2">
            元智启ai是四川慧医云科技打造的AI应用零代码配置平台，
            <br />
            致力于为企业提供高效智能化的数字化转型解决方案。
          </p>
          <div class="group_7 flex-row">
            <div class="image-text_11 flex-row justify-between">
              <a class="text-group_3"
                 @click="openYuanzhiqiHome"
                 href="http://www.yuanzhiqi.com/home"
                 target="_blank"
                 rel="noopener noreferrer"
                 title="了解更多元智启AI企业级应用配置平台信息"
                 style="cursor: pointer; pointer-events: auto; text-decoration: none;">了解更多</a>
              <img
                class="image_4"
                referrerpolicy="no-referrer"
                src="/pages/assets//img/SketchPngcc49aee98d10a7fe19249c50512f1a20122050b1efed370c8fb08734c506bfcf.png"
                alt="外部链接箭头图标"
                loading="lazy"
                width="16"
                height="16"
              />
            </div>
          </div>
        </div>
      </article>

      <div class="list_2 flex-row" role="list">
        <div class="list-items_1 flex-col" v-for="(item, index) in loopData0" :key="index" role="listitem">
          <img class="image_5" referrerpolicy="no-referrer" :src="item.lanhuimage0" :alt="`${item.lanhutext0}特色功能图标 - 展示平台${item.lanhutext0}优势`" loading="lazy" width="64" height="64" />
          <div class="text-group_24 flex-col justify-between">
            <h3 class="text_14" v-html="item.lanhutext0"></h3>
            <p class="text_15" v-html="item.lanhutext1"></p>
          </div>
        </div>
      </div>
    </section>

    <!-- Industry Solutions Section -->
    <section class="group_8 flex-col" aria-labelledby="solutions-title">
      <header class="text-group_25 flex-col justify-between">
        <h2 id="solutions-title" class="text_16">行业解决方案</h2>
        <p class="text_17">丰富的应用场景，满足多种业务需求</p>
      </header>

      <!-- AI智能营销作战中心 -->
      <article class="section_10 flex-row justify-between">
        <div class="section_11 flex-col">
          <header class="group_18 flex-row">
            <img
              class="image_18"
              referrerpolicy="no-referrer"
              src="/pages/assets//img/SketchPngaccb34f33fbd160e412ed52d09bbc62c6dd3d1d2026351db6700c2360ec4d32a.png"
              alt="AI智能营销作战中心产品图标"
              loading="lazy"
              width="32"
              height="32"
            />
            <h3 class="text_61">AI智能营销作战中心</h3>
          </header>
          <p class="text_62">
            以AI驱动的可视化地图，整合市场数据、客户需求与销售动线，实现可点击、可追踪、可指挥的智能操作，精准指导营销，提升企业营销效能。​​
          </p>
          <img
            class="image_19"
            referrerpolicy="no-referrer"
            src="/pages/assets//img/SketchPngbbfbe3cd1fa3a58ae9a002fa1a6916590e3742b6909b79c425c6c0d7ef671915.png"
            alt="AI智能营销作战中心功能界面展示图 - 可视化营销数据分析"
            loading="lazy"
            width="400"
            height="300"
          />
          <div class="group_19 flex-row justify-between" role="list">
            <div class="text-wrapper_9 flex-col" role="listitem"><span class="text_63">市场洞察</span></div>
            <div class="text-wrapper_10 flex-col" role="listitem"><span class="text_64">客户分析</span></div>
            <div class="text-wrapper_11 flex-col" role="listitem"><span class="text_65">精准目标客户规划</span></div>
            <div class="text-wrapper_12 flex-col" role="listitem"><span class="text_66">指挥作战</span></div>
          </div>
          <div class="image-text_14 flex-row justify-between">
            <a href="/products/marketing" class="text-group_33" title="了解更多AI智能营销作战中心详情">了解更多</a>
            <img
              class="image_20"
              referrerpolicy="no-referrer"
              src="/pages/assets//img/SketchPng0e3ad71c2c6d47b5bc5d2a6a6a6f39bd8603565a71e88de3f00030ae66aad418.png"
              alt="内部链接箭头图标"
              loading="lazy"
              width="16"
              height="16"
            />
          </div>
        </div>
        <div class="section_12 flex-col">
          <div class="box_16 flex-col"><div class="box_17 flex-col"></div></div>
        </div>
      </article>

      <!-- AI患助 -->
      <article class="section_9 flex-col">
        <header class="box_15 flex-row">
          <img
            class="image_15"
            referrerpolicy="no-referrer"
            src="/pages/assets//img/SketchPngaccb34f33fbd160e412ed52d09bbc62c6dd3d1d2026351db6700c2360ec4d32a.png"
            alt="AI患助产品图标"
            loading="lazy"
            width="32"
            height="32"
          />
          <h3 class="text_54">ai患助</h3>
        </header>
        <p class="text_55">
          以AI技术重构全流程就医体验，让患者回归健康管理核心。产品贯穿诊前、诊中、诊后全周期，通过三大核心模块化解医疗痛点。
        </p>
        <img
          class="image_16"
          referrerpolicy="no-referrer"
          src="/pages/assets//img/SketchPngbbfbe3cd1fa3a58ae9a002fa1a6916590e3742b6909b79c425c6c0d7ef671915.png"
          alt="AI患助功能界面展示图 - 全流程就医体验"
          loading="lazy"
          width="400"
          height="300"
        />
        <div class="list_3 flex-row" role="list">
          <div class="text-group_31 flex-col" v-for="(item, index) in loopData1" :key="index" role="listitem">
            <div v-if="item.slot1 === 1" class="text-wrapper_7">
              <span class="text_56" v-html="item.specialSlot1.lanhutext0"></span>
              <span class="text_57" v-html="item.specialSlot1.lanhutext1"></span>
            </div>
            <div v-if="item.slot2 === 2" class="text-wrapper_8">
              <span class="text_58" v-html="item.specialSlot2.lanhutext0"></span>
              <span class="text_59" v-html="item.specialSlot2.lanhutext1"></span>
            </div>
            <p class="text_60" v-html="item.lanhutext0"></p>
          </div>
        </div>
        <div class="image-text_13 flex-row justify-between">
          <a href="/products/aihuanzhu" class="text-group_32" title="了解更多AI患助产品详情">了解更多</a>
          <img
            class="image_17"
            referrerpolicy="no-referrer"
            src="/pages/assets//img/SketchPng0e3ad71c2c6d47b5bc5d2a6a6a6f39bd8603565a71e88de3f00030ae66aad418.png"
            alt="内部链接箭头图标"
            loading="lazy"
            width="16"
            height="16"
          />
        </div>
      </article>

      <!-- AI医助 -->
      <article class="section_8 flex-row justify-between">
        <div class="box_11 flex-col">
          <header class="box_12 flex-row">
            <img
              class="image_12"
              referrerpolicy="no-referrer"
              src="/pages/assets//img/SketchPngaccb34f33fbd160e412ed52d09bbc62c6dd3d1d2026351db6700c2360ec4d32a.png"
              alt="AI医助产品图标"
              loading="lazy"
              width="32"
              height="32"
            />
            <h3 class="text_40">ai医助</h3>
          </header>
          <p class="text_41">专为医疗场景打造的智能辅助工具，致力于解决医生群体长期面临的文书负担与效率提升。</p>
          <img
            class="image_13"
            referrerpolicy="no-referrer"
            src="/pages/assets//img/SketchPngbbfbe3cd1fa3a58ae9a002fa1a6916590e3742b6909b79c425c6c0d7ef671915.png"
            alt="AI医助功能界面展示图 - 智能医疗辅助工具"
            loading="lazy"
            width="400"
            height="300"
          />
          <div class="box_13 flex-row justify-between" role="list">
            <div class="text-group_26 flex-col justify-between" role="listitem">
              <div class="text-wrapper_3">
                <span class="text_42">2</span>
                <span class="text_43">秒</span>
              </div>
              <span class="text_44">生成电子病历</span>
            </div>
            <div class="text-group_27 flex-col justify-between" role="listitem">
              <div class="text-wrapper_4">
                <span class="text_45">75</span>
                <span class="text_46">%</span>
              </div>
              <span class="text_47">节省病历书写时间</span>
            </div>
            <div class="text-group_28 flex-col justify-between" role="listitem">
              <div class="text-wrapper_5">
                <span class="text_48">45</span>
                <span class="text_49">%</span>
              </div>
              <span class="text_50">提升病历质量</span>
            </div>
            <div class="text-group_29 flex-col justify-between" role="listitem">
              <div class="text-wrapper_6">
                <span class="text_51">75</span>
                <span class="text_52">%</span>
              </div>
              <span class="text_53">提升患者服务数量</span>
            </div>
          </div>
          <div class="image-text_12 flex-row justify-between">
            <a href="/products/aiyizhu" class="text-group_30" title="了解更多AI医助产品详情">了解更多</a>
            <img
              class="image_14"
              referrerpolicy="no-referrer"
              src="/pages/assets//img/SketchPng0e3ad71c2c6d47b5bc5d2a6a6a6f39bd8603565a71e88de3f00030ae66aad418.png"
              alt="内部链接箭头图标"
              loading="lazy"
              width="16"
              height="16"
            />
          </div>
        </div>
        <div class="box_14 flex-col"></div>
      </article>
      <div class="section_13 flex-col"></div>
    </section>

    <!-- News Section -->
    <section class="group_10 flex-col" aria-labelledby="news-title">
      <header class="text-group_34 flex-col justify-between">
        <h2 id="news-title" class="text_24">新闻动态</h2>
        <p class="text_25">最新行业资讯，慧医云新闻动态集结于此</p>
      </header>

      <article class="block_4 flex-row">
        <div class="box_6 flex-col"></div>
        <div class="box_18 flex-col justify-between">
          <header class="box_8 flex-row">
            <div class="text-group_35 flex-col justify-between">
              <time datetime="2025-12-23" class="text_26">1</time>
              <time datetime="2025-12" class="text_27">2024.12</time>
            </div>
          </header>
          <div class="text-group_36 flex-col justify-between">
            <h3 class="text_28">敬请期待！！！</h3>
            <p class="text_29">
            敬请期待！！！
            </p>
          </div>
        </div>
      </article>

      <article class="block_5 flex-row">
        <div class="section_3 flex-col"></div>
        <div class="group_20 flex-col justify-between">
          <header class="group_11 flex-row">
            <div class="text-group_37 flex-col justify-between">
              <time datetime="2025-12-20" class="text_30">2</time>
              <time datetime="2025-12" class="text_31">2024.12</time>
            </div>
          </header>
          <div class="text-group_38 flex-col justify-between">
            <h3 class="text_32">敬请期待！！！</h3>
            <p class="text_33">
              敬请期待！！！
            </p>
          </div>
        </div>
      </article>

      <article class="block_6 flex-row">
        <div class="group_12 flex-col"></div>
        <div class="group_21 flex-col justify-between">
          <header class="section_5 flex-row">
            <div class="text-group_39 flex-col justify-between">
              <time datetime="2025-12-18" class="text_34">3</time>
              <time datetime="2025-12" class="text_35">2024.12</time>
            </div>
          </header>
          <div class="text-group_40 flex-col justify-between">
            <h3 class="text_36">敬请期待！！！</h3>
            <p class="text_37">
              敬请期待！！！
            </p>
          </div>
        </div>
      </article>

      <div class="image-text_15 flex-row justify-between" @click="goToNews" style="cursor: pointer;">
        <a href="/news" class="text-group_17" title="查看更多新闻动态">了解更多新闻动态</a>
        <img
          class="image_10"
          referrerpolicy="no-referrer"
          src="/pages/assets//img/SketchPng110188783d1f2a8711eb7a30f39c9c4cda79a3e5f04447c0ec6262ba26a4e208.png"
          alt="内部链接箭头图标"
          loading="lazy"
          width="16"
          height="16"
        />
      </div>
    </section>
  </main>
</template>

<script setup>
// 页面加载状态
const isLoading = ref(true)
const hasError = ref(false)

// 页面生命周期
onMounted(() => {
  try {
    // 模拟页面加载完成
    setTimeout(() => {
      isLoading.value = false
    }, 100)
  } catch (error) {
    console.error('页面加载失败:', error)
    hasError.value = true
    isLoading.value = false
  }
})

// 页面头部信息
useHead({
  title: '慧医云科技 - AI医疗解决方案服务商',
  meta: [
    { name: 'description', content: '四川慧医云科技，聚焦医药及医疗行业，提供AI深度业务应用的解决方案服务商。主要产品包括元智启AI、AI医助、AI患助、AI智能营销作战中心等。' },
    { name: 'keywords', content: 'AI医疗,元智启AI,AI医助,AI患助,智能营销作战中心,医疗科技,人工智能,医疗解决方案,慧医云科技,四川医疗科技' },
    { name: 'author', content: '四川慧医云科技有限公司' },
    { name: 'robots', content: 'index,follow' },
    { name: 'googlebot', content: 'index,follow' },
    { name: 'baiduspider', content: 'index,follow' },

    // Open Graph标签
    { property: 'og:title', content: '慧医云科技 - AI医疗解决方案服务商' },
    { property: 'og:description', content: '四川慧医云科技，聚焦医药及医疗行业，提供AI深度业务应用的解决方案服务商。主要产品包括元智启AI、AI医助、AI患助、AI智能营销作战中心等。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://www.huiyiyunai.com' },
    { property: 'og:image', content: 'https://www.huiyiyunai.com/og-image.jpg' },
    { property: 'og:site_name', content: '慧医云科技' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '慧医云科技 - AI医疗解决方案服务商' },
    { name: 'twitter:description', content: '四川慧医云科技，聚焦医药及医疗行业，提供AI深度业务应用的解决方案服务商。' },
    { name: 'twitter:image', content: 'https://www.huiyiyunai.com/twitter-image.jpg' },

    // 移动端优化
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
    { name: 'format-detection', content: 'telephone=no' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },

    // 安全和性能优化
    { 'http-equiv': 'X-Content-Type-Options', content: 'nosniff' },
    { 'http-equiv': 'X-Frame-Options', content: 'DENY' },
    { 'http-equiv': 'X-XSS-Protection', content: '1; mode=block' }
  ],
  link: [
    { rel: 'canonical', href: 'https://www.huiyiyunai.com' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://www.huiyiyunai.com' },
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true },
    { rel: 'dns-prefetch', href: 'https://www.yuanzhiqi.com' }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "四川慧医云科技有限公司",
        "alternateName": "慧医云科技",
        "url": "https://www.huiyiyunai.com",
        "logo": "https://www.huiyiyunai.com/logo.png",
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "+86-188-4845-7100",
          "contactType": "customer service",
          "email": "<EMAIL>"
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "高攀路26号-1",
          "addressLocality": "成都市",
          "addressRegion": "四川省",
          "addressCountry": "CN"
        },
        "sameAs": [
          "https://www.huiyiyunai.com"
        ],
        "description": "聚焦医药及医疗行业，提供AI深度业务应用的解决方案服务商",
        "foundingDate": "2020",
        "industry": "医疗科技",
        "keywords": "AI医疗,元智启AI,AI医助,AI患助,智能营销作战中心",
        "products": [
          {
            "@type": "Product",
            "name": "元智启AI",
            "description": "企业级AI应用配置平台，AI应用零代码配置平台"
          },
          {
            "@type": "Product",
            "name": "AI医助",
            "description": "专为医疗场景打造的智能辅助工具，解决医生文书负担与效率提升"
          },
          {
            "@type": "Product",
            "name": "AI患助",
            "description": "以AI技术重构全流程就医体验，贯穿诊前、诊中、诊后全周期"
          },
          {
            "@type": "Product",
            "name": "AI智能营销作战中心",
            "description": "AI驱动的可视化地图，整合市场数据、客户需求与销售动线"
          }
        ]
      })
    }
  ]
})

// 产品特点数据
const loopData0 = ref([
  {
    lanhuimage0: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPng7e955bb8e41d876decdd8baa09c918626cb8415d4cfb2c001f3481fc58acd11d',
    lanhutext0: '低成本',
    lanhutext1: '开发成本低，时间成本低<br/>人员能力要求低'
  },
  {
    lanhuimage0: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPng7e955bb8e41d876decdd8baa09c918626cb8415d4cfb2c001f3481fc58acd11d',
    lanhutext0: '无门槛',
    lanhutext1: '小白也能快速掌握<br/>轻松搭建AI智能体'
  },
  {
    lanhuimage0: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPng1af3e1640f5c931e201286efa9a2de7d103ac85aa7c0ec6470b8b66156e4bde3',
    lanhutext0: '零代码',
    lanhutext1: '区别于其他AI开发平台<br/>可实现无代码配置'
  },
  {
    lanhuimage0: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPngccf11369b8e9942e10d3d2e5052f81d2a0b6ee8e865b07528564105cf1b7be7a',
    lanhutext0: '配置快',
    lanhutext1: '轻量级配置<br/>一个AI智能体1个小时搞定'
  },
  {
    lanhuimage0: 'https://lanhu-oss-2537-2.lanhuapp.com/SketchPng81887b49a2d6694130edf7b9b52ce7c959bfd365b04abf09ef9e40aa7b1b4757',
    lanhutext0: '应用广',
    lanhutext1: '不管哪个行业，凡适合AI的业务应用，均可配置生成'
  }
])

const loopData1 = ref([
  { lanhutext0: '节省患者预诊<br/>信息录入时间', specialSlot1: { lanhutext0: '90', lanhutext1: '%' }, slot1: 1 },
  { lanhutext0: '科室匹配<br/>准确率提升', specialSlot2: { lanhutext0: '95', lanhutext1: '%' }, slot2: 2 },
  { lanhutext0: '患者候诊<br/>等待时间缩短', specialSlot1: { lanhutext0: '40', lanhutext1: '%' }, slot1: 1 },
  { lanhutext0: '主诉信息<br/>完整度提升', specialSlot2: { lanhutext0: '60', lanhutext1: '%' }, slot2: 2 },
  {
    lanhutext0: '慢性病患者院外<br/>指标达标率提高',
    specialSlot1: { lanhutext0: '40', lanhutext1: '%' },
    slot1: 1
  }
])

// 点击事件处理函数
const openYuanzhiqiHome = () => {
  console.log('点击了解更多按钮')
  try {
    window.open('http://www.yuanzhiqi.com/home', '_blank')
    console.log('成功打开新窗口')
  } catch (error) {
    console.error('打开窗口失败:', error)
    // 备用方案：直接跳转
    window.location.href = 'http://www.yuanzhiqi.com/home'
  }
}

// 跳转到新闻中心页面
const goToNews = () => {
  console.log('点击了解更多新闻动态')
  navigateTo('/news')
}
</script>

<style scoped lang="less" src="/pages/assets//index.rem.less" />


