<template>
  <div class="page flex-col justify-between">
    <div class="box_1 flex-col">

      <div class="group_4 flex-row"><div class="group_1 flex-col"></div></div>
    </div>
    <div class="text-wrapper_1 flex-col">
      <span class="text_7">欢迎合作</span>
      <span class="text_8">把握时代趋势，拥抱行业变革</span>
    </div>
    <div class="block_1 flex-col">
      <span class="text_13">
        注：成为元智启平台代理人，即可享受代理商收益。代理者可介绍他人成为平台代理人，可享受向下两级代理者业务开展收益。
      </span>
      <!-- <span class="text_14">一、代理类型</span>
      <div class="text-group_5 flex-col justify-between">
        <span class="paragraph_3">
          个人代理：指代理元智启平台的个人
          <br />
          企业代理：指代理元智启平台的企事业单位
        </span>
        <span class="text_15">二、成为代理人条件</span>
      </div>
      <div class="text-group_6 flex-col justify-between">
        <span class="paragraph_4">
          代理人需积极开发客户，帮助客户使用元智启平台，将AI智能体应用于各行各业，助力AI发展
          <br />
          代理人需介绍其他个人或企业成为代理者，共同推动元智启平台发展
        </span>
        <span class="text_16">三、代理人权益对比</span>
      </div> -->
      <div class="group_5 flex-col">
        <div class="group_6 flex-col">
          <img
            class="image_3"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngdccef0fe28ce160ad9cad90febef03f48e0274b9d7ccaa443ce3e9e4db456128.png"
          />
          <div class="text-wrapper_3 flex-row">
            <span class="text_17">权益项</span>
            <span class="text_18">个人代理</span>
            <span class="text_19">企业代理</span>
            <span class="text_20">介绍他人成为代理人</span>
          </div>
          <img
            class="image_4"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPngdccef0fe28ce160ad9cad90febef03f48e0274b9d7ccaa443ce3e9e4db456128.png"
          />
        </div>
        <div class="group_7 flex-row">
          <div class="text-group_7 flex-col justify-between">
            <span class="text_21">积分奖励</span>
            <span class="text_22">(积分可兑换平台Token使用)</span>
          </div>
          <div class="text-wrapper_4">
            <span class="paragraph_5">
              成为元智启平台个人代理人
              <br />
              获得代理人启动积分
            </span>
            <span class="text_23">XX</span>
            <span class="text_24">个</span>
          </div>
          <div class="text-wrapper_5">
            <span class="paragraph_6">
              成为元智启平台企业代理人
              <br />
              获得代理人启动积分
            </span>
            <span class="text_25">XX</span>
            <span class="text_26">个</span>
          </div>
          <img
            class="thumbnail_6"
            referrerpolicy="no-referrer"
            src="/pages/assets/img/SketchPng23c4a0aeb39db0dce612afe7d0acb245105d7937df0e8e41280a188862720560.png"
          />
          <div class="text-wrapper_6">
            <span class="paragraph_7">
              介绍他人成为元智启平台代理人
              <br />
              代理者及被介绍者均可获得积分
            </span>
            <span class="text_27">XX</span>
            <span class="text_28">个</span>
          </div>
        </div>
        <img
          class="image_5"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPng33ba4ece6ff561c908af7676fc601d74449ff17b6db90e4d120026998d5958a7.png"
        />
        <div class="text-wrapper_7 flex-row">
          <span class="text_29">元智启积分购买折扣</span>
          <span class="text_30">9折</span>
          <span class="text_31">8.5折</span>
          <span class="text_32">——</span>
        </div>
        <img
          class="image_6"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPngdccef0fe28ce160ad9cad90febef03f48e0274b9d7ccaa443ce3e9e4db456128.png"
        />
        <div class="group_8 flex-row">
          <div class="text-group_8 flex-col justify-between">
            <span class="text_33">版本销售分成</span>
            <span class="text_34">(代理人开发的客户购买元智启版本账号）</span>
          </div>
          <div class="text-wrapper_8">
            <span class="text_35">按购买金额的</span>
            <span class="text_36">40%</span>
            <span class="text_37">分成</span>
          </div>
          <div class="text-wrapper_9">
            <span class="text_38">按购买金额的</span>
            <span class="text_39">40%</span>
            <span class="text_40">分成</span>
          </div>
          <div class="text-wrapper_10">
            <span class="text_41">一级代理人，按购买金额的</span>
            <span class="text_42">10%</span>
            <span class="paragraph_8">
              分成
              <br />
              二级代理人，按购买金额的
            </span>
            <span class="text_43">5%</span>
            <span class="text_44">分成</span>
          </div>
        </div>
        <img
          class="image_7"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPngdccef0fe28ce160ad9cad90febef03f48e0274b9d7ccaa443ce3e9e4db456128.png"
        />
        <div class="group_9 flex-row">
          <div class="text-group_9 flex-col justify-between">
            <span class="text_45">积分购买分成</span>
            <span class="text_46">(代理人开发的客户购买元智启积分）</span>
          </div>
          <div class="text-wrapper_11">
            <span class="text_47">按购买积分金额的</span>
            <span class="text_48">30%</span>
            <span class="text_49">分成</span>
          </div>
          <div class="text-wrapper_12">
            <span class="text_50">按购买积分金额的</span>
            <span class="text_51">30%</span>
            <span class="text_52">分成</span>
          </div>
          <div class="text-wrapper_13">
            <span class="text_53">一级代理人，按购买积分金额的</span>
            <span class="text_54">10%</span>
            <span class="paragraph_9">
              分成

              <br />
              二级代理人，按购买积分金额的
            </span>
            <span class="text_55">5%</span>
            <span class="text_56">分成</span>
          </div>
        </div>
        <img
          class="image_8"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPngdccef0fe28ce160ad9cad90febef03f48e0274b9d7ccaa443ce3e9e4db456128.png"
        />
        <div class="group_10 flex-row">
          <div class="text-group_10 flex-col justify-between">
            <span class="text_57">开发客户代理人账号可升级</span>
            <span class="text_58">(购买版本或积分的客户方可纳入计算）</span>
          </div>
          <div class="text-wrapper_14">
            <span class="text_59">开发客户超</span>
            <span class="text_60">5个</span>
            <span class="paragraph_10">
              ，账号升级为专业版
              <br />
              开发客户超
            </span>
            <span class="text_61">10个</span>
            <span class="text_62">，账号升级为企业版</span>
          </div>
          <div class="text-wrapper_15">
            <span class="text_63">开发客户超</span>
            <span class="text_64">5个</span>
            <span class="paragraph_11">
              ，账号升级为专业版
              <br />
              开发客户超
            </span>
            <span class="text_65">10个</span>
            <span class="text_66">，账号升级为企业版</span>
          </div>
          <span class="text_67">——</span>
        </div>
        <img
          class="image_9"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPngdccef0fe28ce160ad9cad90febef03f48e0274b9d7ccaa443ce3e9e4db456128.png"
        />
        <div class="group_11 flex-row">
          <span class="text_68">私有化部署购买折扣</span>
          <span class="text_69">——</span>
          <div class="text-wrapper_16">
            <span class="text_70">按标准报价的</span>
            <span class="text_71">5折</span>
            <span class="text_72">结算</span>
          </div>
          <span class="text_73">——</span>
        </div>
        <img
          class="image_10"
          referrerpolicy="no-referrer"
          src="/pages/assets/img/SketchPngdccef0fe28ce160ad9cad90febef03f48e0274b9d7ccaa443ce3e9e4db456128.png"
        />
        <div class="group_12 flex-row">
          <span class="text_74">私有化部署赠送</span>
          <span class="text_75">——</span>
          <div class="text-group_11 flex-col justify-between">
            <div class="text-wrapper_17">
              <span class="text_76">代理人开发的客户在元智启平台消费</span>
              <span class="text_77">每达30万</span>
              <span class="paragraph_12">
                <br />
                可获得元智启私有化部署1套
                <br />
              </span>
            </div>
            <span class="text_78">
              （注：企业代理人可用该平台独立开发应用智能体承接对外业务或提供给第三方客户使用，不额外收取私有化系统费用）
            </span>
          </div>
          <span class="text_79">——</span>
        </div>
      </div>
      <div class="text-wrapper_18 flex-col">
        <span class="text_80">
          备注<br />
          一级指代理人直接介绍的代理者，
          <br />
          二级指他介绍的代理者继续向下推荐的代理者
        </span>
      </div>
    </div>

    <!-- 聚焦医药及医疗行业模块 -->
    <div class="box_11 flex-col">
      <div class="text-wrapper_21 flex-col">
        <span class="paragraph_14">
          聚焦医药及医疗行业
          <br />
          提供AI深度业务应用的解决方案服务商
        </span>
      </div>
      <!-- 真实可用的表单 -->
      <form @submit.prevent="submitForm" class="block_6 flex-col">
        <div class="group_20 flex-row justify-between">
          <div class="text-wrapper_29">
            <span class="text_100">*</span>
            <span class="text_101">公司名称</span>
            <span class="text_102"> (必须是注册企业)</span>
          </div>
          <div class="text-wrapper_30">
            <span class="text_103">*</span>
            <span class="text_104">填写省市</span>
          </div>
        </div>
                  <div class="group_21 flex-row justify-between">
            <div class="text-wrapper_31 flex-col">
              <input
                type="text"
                v-model="formData.companyName"
                placeholder="请输入您的公司名称"
                class="form-input-location"
                required
              />
            </div>
            <div class="box_15 flex-row">
              <input
                type="text"
                v-model="formData.location"
                placeholder="请填写省市地区"
                class="form-input-location"
                required
              />
            </div>
          </div>
        <div class="group_22 flex-row justify-between">
          <div class="text-wrapper_32">
            <span class="text_107">*</span>
            <span class="text_108">联系人</span>
          </div>
          <div class="text-wrapper_33">
            <span class="text_109">*</span>
            <span class="text_110">联系方式</span>
          </div>
        </div>
                  <div class="group_23 flex-row justify-between">
            <div class="text-wrapper_34 flex-col">
              <input
                type="text"
                v-model="formData.contactName"
                placeholder="请输入您的姓名"
                class="form-input-location"
                required
              />
            </div>
            <div class="text-wrapper_35 flex-col">
              <input
                type="tel"
                v-model="formData.contactPhone"
                placeholder="请输入您的联系方式"
                class="form-input-location"
                pattern="^1[3-9]\d{9}$"
                required
              />
            </div>
          </div>
        <div class="text-wrapper_36">
          <span class="text_113">您所属的行业</span>
          <span class="text_114">(个人用户无效)</span>
        </div>
        <div class="group_24 flex-row">
          <div class="text-wrapper_38 flex-col">
            <input
              type="text"
              v-model="formData.industry"
              placeholder="请输入您的所属行业"
              class="form-input-location"
            />
          </div>
        </div>
        <button type="submit" class="text-wrapper_37 flex-col" :disabled="isSubmitting">
          <span class="text_116">{{ isSubmitting ? '提交中...' : '提交' }}</span>
        </button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 表单数据
const formData = ref({
  companyName: '',
  location: '',
  contactName: '',
  contactPhone: '',
  industry: ''
})

// 提交状态
const isSubmitting = ref(false)

// 防抖相关 - 30秒只能提交一次
const lastSubmitTime = ref(0)
const SUBMIT_INTERVAL = 30000 // 30秒间隔



// 表单提交处理
const submitForm = async () => {
  try {
    isSubmitting.value = true

    // 防抖检查：30秒内只能提交一次
    const now = Date.now()
    if (now - lastSubmitTime.value < SUBMIT_INTERVAL) {
      const remainingTime = Math.ceil((SUBMIT_INTERVAL - (now - lastSubmitTime.value)) / 1000)
      throw new Error(`请勿频繁提交，请等待 ${remainingTime} 秒后再试`)
    }

    // 表单验证
    if (!formData.value.companyName.trim()) {
      throw new Error('请输入公司名称')
    }
    if (!formData.value.location.trim()) {
      throw new Error('请填写省市地区')
    }
    if (!formData.value.contactName.trim()) {
      throw new Error('请输入联系人姓名')
    }
    if (!formData.value.contactPhone.trim()) {
      throw new Error('请输入联系方式')
    }

    // 手机号验证
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(formData.value.contactPhone)) {
      throw new Error('请输入正确的手机号码')
    }

    // 准备提交数据
    const submitData = {
      companyName: formData.value.companyName.trim(),
      location: formData.value.location.trim(),
      contactName: formData.value.contactName.trim(),
      contactPhone: formData.value.contactPhone.trim(),
      industry: formData.value.industry || '未选择',
      submitTime: new Date().toISOString(),
      source: '招商代理页面'
    }

    // 调用API提交数据 - 使用代理避免CORS问题
    const response = await $fetch('/api/agency/submit', {
      method: 'POST',
      body: submitData,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.success) {
      // 记录提交时间
      lastSubmitTime.value = Date.now()

      // 提交成功
      alert('提交成功！我们会尽快与您联系。')

      // 重置表单
      formData.value = {
        companyName: '',
        location: '',
        contactName: '',
        contactPhone: '',
        industry: ''
      }
    } else {
      throw new Error(response.message || '提交失败，请稍后重试')
    }

  } catch (error) {
    console.error('表单提交错误:', error)

    // 如果是网络错误或API不存在，先用临时方案
    if (error.message.includes('404') || error.message.includes('fetch')) {
      // 临时存储到localStorage作为备用方案
      const submissions = JSON.parse(localStorage.getItem('agencySubmissions') || '[]')
              const submitData = {
          id: Date.now(),
          companyName: formData.value.companyName.trim(),
          location: formData.value.location.trim(),
          contactName: formData.value.contactName.trim(),
          contactPhone: formData.value.contactPhone.trim(),
          industry: formData.value.industry || '未选择',
          submitTime: new Date().toISOString(),
          source: '招商代理页面'
        }

            submissions.push(submitData)
      localStorage.setItem('agencySubmissions', JSON.stringify(submissions))

      // 记录提交时间
      lastSubmitTime.value = Date.now()

      alert('提交成功！我们会尽快与您联系。\n（注：数据已临时保存，请联系管理员配置后端接口）')

      // 重置表单
      formData.value = {
        companyName: '',
        location: '',
        contactName: '',
        contactPhone: '',
        industry: ''
      }
    } else {
      alert(error.message || '提交失败，请稍后重试')
    }
  } finally {
    isSubmitting.value = false
  }
}

// 页面头部信息
useHead({
  title: '元智启招商代理 - 慧医云科技',
  meta: [
    { name: 'description', content: '元智启平台招商代理，把握时代趋势，拥抱行业变革。成为代理人享受丰厚收益，包括积分奖励、销售分成、账号升级等多重权益。' },
    { name: 'keywords', content: '元智启代理,招商代理,AI平台代理,个人代理,企业代理,代理权益,销售分成,积分奖励,慧医云科技,四川医疗科技' },
    { name: 'author', content: '四川慧医云科技有限公司' },
    { name: 'robots', content: 'index,follow' },
    { name: 'googlebot', content: 'index,follow' },
    { name: 'baiduspider', content: 'index,follow' },

    // Open Graph标签
    { property: 'og:title', content: '元智启招商代理 - 慧医云科技' },
    { property: 'og:description', content: '元智启平台招商代理，把握时代趋势，拥抱行业变革。成为代理人享受丰厚收益，包括积分奖励、销售分成、账号升级等多重权益。' },
    { property: 'og:type', content: 'website' },
    { property: 'og:url', content: 'https://www.huiyiyunai.com/agency' },
    { property: 'og:image', content: 'https://www.huiyiyunai.com/og-image.jpg' },
    { property: 'og:site_name', content: '慧医云科技' },
    { property: 'og:locale', content: 'zh_CN' },

    // Twitter Card标签
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '元智启招商代理 - 慧医云科技' },
    { name: 'twitter:description', content: '元智启平台招商代理，把握时代趋势，拥抱行业变革。' },
    { name: 'twitter:image', content: 'https://www.huiyiyunai.com/twitter-image.jpg' },

    // 移动端优化
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
    { name: 'format-detection', content: 'telephone=no' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' }
  ],
  link: [
    { rel: 'canonical', href: 'https://www.huiyiyunai.com/agency' },
    { rel: 'alternate', hreflang: 'zh-CN', href: 'https://www.huiyiyunai.com/agency' }
  ],
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "四川慧医云科技有限公司",
        "alternateName": "慧医云科技",
        "url": "https://www.huiyiyunai.com",
        "logo": "https://www.huiyiyunai.com/logo.png",
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "+86-188-4845-7100",
          "contactType": "customer service",
          "email": "<EMAIL>"
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "高攀路26号-1",
          "addressLocality": "成都市",
          "addressRegion": "四川省",
          "addressCountry": "CN"
        },
        "sameAs": [
          "https://www.huiyiyunai.com"
        ],
        "description": "聚焦医药及医疗行业，提供AI深度业务应用的解决方案服务商",
        "foundingDate": "2020",
        "industry": "医疗科技",
        "keywords": "元智启代理,招商代理,AI平台代理,个人代理,企业代理",
        "mainEntity": {
          "@type": "Service",
          "name": "元智启代理服务",
          "description": "提供个人代理和企业代理两种类型，享受积分奖励、销售分成等多重权益",
          "provider": {
            "@type": "Organization",
            "name": "四川慧医云科技有限公司"
          },
          "offers": [
            {
              "@type": "Offer",
              "name": "个人代理",
              "description": "个人代理元智启平台，享受40%版本销售分成，30%积分购买分成"
            },
            {
              "@type": "Offer",
              "name": "企业代理",
              "description": "企业代理元智启平台，享受40%版本销售分成，30%积分购买分成，私有化部署5折优惠"
            }
          ]
        }
      })
    }
  ]
})
</script>

<style scoped lang="less" src="/pages/assets//agency.rem.less" />
