// 通用flex布局工具类
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

// 主样式
.page {
  background-color: rgba(251, 251, 251, 1);
  position: relative;
  width: 51.2rem;
  overflow: hidden;
  padding-top: 2.187rem;
  .box_1 {
    background-color: rgba(255, 255, 255, 1);
    height: 2.187rem;
    width: 51.2rem;
    .group_20 {
      width: 40.534rem;
      height: 0.64rem;
      margin: 0.774rem 0 0 5.334rem;
      .image_1 {
        width: 4.267rem;
        height: 0.64rem;
      }
      .text_1 {
        width: 0.907rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 15.867rem;
      }
      .text_2 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 2.08rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.08rem 0 0 0.107rem;
      }
      .text_3 {
        width: 3.174rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_4 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_5 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_6 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
    }
    .group_21 {
      width: 1.814rem;
      height: 0.08rem;
      margin: 0.24rem 0 0.454rem 44.054rem;
      .box_2 {
        background-color: rgba(33, 135, 250, 1);
        width: 1.814rem;
        height: 0.08rem;
      }
    }
  }
  .section_9 {
    position: relative;
    width: 51.2rem;
    height: 61.654rem;
    .text-wrapper_1 {
      background-image: url(/pages/assets/img/26285a4f3ec242c38c8395cf7b7e2da6_mergeImage.png);
      background-repeat: no-repeat;
      background-size: cover;
      width: 51.2rem;
      height: 12.8rem;
      .paragraph_1 {
        height: 3.734rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 1.333rem;
        text-transform: uppercase;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        line-height: 1.867rem;
        margin: 3.094rem 0 0 6.96rem;
      }
      .text_7 {
        width: 5.707rem;
        height: 0.64rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.64rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.64rem;
        margin: 0.747rem 0 4.587rem 6.96rem;
      }
    }
    .group_3 {
      background-color: rgba(255, 255, 255, 1);
      position: relative;
      width: 51.2rem;
      height: 23.494rem;
      .image_2 {
        width: 14.08rem;
        height: 2.454rem;
        margin: 3.014rem 0 0 6.934rem;
      }
      .image_3 {
        width: 23.334rem;
        height: 0.027rem;
        margin: 1.334rem 0 0 6.934rem;
      }
      .text_8 {
        width: 1.28rem;
        height: 0.88rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 0.88rem;
        margin: 1.067rem 0 0 6.96rem;
      }
      .text-wrapper_2 {
        width: 16.04rem;
        height: 2.667rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        line-height: 1.334rem;
        margin: 0.827rem 0 0 6.96rem;
        .text_9 {
          width: 11.04rem;
          height: 2.667rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.853rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.334rem;
        }
        .text_10 {
          height: 2.667rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 0.853rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.334rem;
        }
        .paragraph_2 {
          height: 2.667rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.853rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.334rem;
        }
      }
      .box_4 {
        height: 10.667rem;
        background: url(/pages/assets/img/SketchPngda8be8214daef77d3b6d3ce651ed02b958a0ff1bf28a9776426c857125c6d142.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 51.2rem;
        position: relative;
        margin: 1.36rem 0 0.8rem 0;
        .text-wrapper_21 {
          width: 1.28rem;
          height: 0.88rem;
          margin: 1.334rem 0 0 6.96rem;
          .text_11 {
            width: 1.28rem;
            height: 0.88rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 0.88rem;
          }
        }
        .box_21 {
          width: 5.04rem;
          height: 0.88rem;
          margin: 0.854rem 0 0 6.96rem;
          .image-text_7 {
            width: 4.24rem;
            height: 0.88rem;
            .text-group_1 {
              width: 3.174rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.64rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
            .label_1 {
              width: 0.8rem;
              height: 0.747rem;
              margin-top: 0.08rem;
            }
          }
          .label_2 {
            width: 0.8rem;
            height: 0.747rem;
            margin-top: 0.08rem;
          }
        }
        .box_22 {
          width: 24.64rem;
          height: 2.24rem;
          margin: 0.534rem 0 0 6.96rem;
          .block_1 {
            background-color: rgba(255, 255, 255, 1);
            width: 12.054rem;
            height: 2.24rem;
            .box_5 {
              background-color: rgba(33, 135, 250, 1);
              width: 0.107rem;
              height: 2.24rem;
            }
            .text-wrapper_4 {
              width: 10.667rem;
              height: 0.747rem;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.747rem;
              margin: 0.747rem 0.747rem 0 0.534rem;
              .text_12 {
                width: 10.667rem;
                height: 0.747rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.533rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.747rem;
              }
              .text_13 {
                width: 10.667rem;
                height: 0.747rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.533rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.747rem;
              }
            }
          }
          .block_2 {
            background-color: rgba(255, 255, 255, 1);
            width: 12.054rem;
            height: 2.24rem;
            .group_4 {
              background-color: rgba(33, 135, 250, 1);
              width: 0.107rem;
              height: 2.24rem;
            }
            .text-wrapper_5 {
              width: 10.667rem;
              height: 0.747rem;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.747rem;
              margin: 0.747rem 0.747rem 0 0.534rem;
              .text_14 {
                width: 10.667rem;
                height: 0.747rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.533rem;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                white-space: nowrap;
                line-height: 0.747rem;
              }
              .text_15 {
                width: 10.667rem;
                height: 0.747rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.533rem;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                white-space: nowrap;
                line-height: 0.747rem;
              }
            }
          }
        }
        .box_23 {
          width: 12.054rem;
          height: 2.24rem;
          margin: 0.534rem 0 1.174rem 6.96rem;
          .group_5 {
            background-color: rgba(255, 255, 255, 1);
            width: 12.054rem;
            height: 2.24rem;
            .group_6 {
              background-color: rgba(33, 135, 250, 1);
              width: 0.107rem;
              height: 2.24rem;
            }
            .text-wrapper_6 {
              width: 9.6rem;
              height: 0.747rem;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.747rem;
              margin: 0.747rem 1.814rem 0 0.534rem;
              .text_16 {
                width: 9.6rem;
                height: 0.747rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.533rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.747rem;
              }
              .text_17 {
                width: 9.6rem;
                height: 0.747rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.533rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.747rem;
              }
              .text_18 {
                width: 9.6rem;
                height: 0.747rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.533rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.747rem;
              }
            }
          }
        }
        .section_4 {
          background-image: url(/pages/assets/img/eae7f3137c614589ac0f13ed345778b5_mergeImage.png);
          background-repeat: no-repeat;
          background-size: cover;
          position: absolute;
          left: 32.134rem;
          top: -6.826rem;
          width: 12.134rem;
          height: 17.467rem;
        }
      }
      .text_19 {
        position: absolute;
        left: 6.96rem;
        top: 2.667rem;
        width: 5.12rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
      }
    }
    .group_7 {
      height: 24.507rem;
              background: url(/pages/assets/img/SketchPng80a3ff052332553bfa7f4bde43e59ec15acff2e58998a52f5e8033df4bae25a3.png)
          100% no-repeat;
      background-size: 100% 100%;
      width: 51.2rem;
      position: relative;
      margin: 0.8rem 0 0.054rem 0;
      .image-wrapper_7 {
        width: 12.107rem;
        height: 2.507rem;
        margin: 3.067rem 0 0 31.947rem;
        .image_4 {
          width: 12.107rem;
          height: 2.507rem;
        }
      }
      .box_24 {
        width: 37.254rem;
        height: 18.507rem;
        margin: 0.427rem 0 0 6.96rem;
        .group_22 {
          width: 18.107rem;
          height: 18.507rem;
          .section_10 {
            width: 13.947rem;
            height: 3.574rem;
            .image_29 {
              width: 1.92rem;
              height: 1.654rem;
            }
            .paragraph_3 {
              width: 11.147rem;
              height: 3.04rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 1.013rem;
              font-family: Alibaba-PuHuiTi-H;
              font-weight: NaN;
              text-align: left;
              line-height: 1.52rem;
              margin-top: 0.534rem;
            }
          }
          .block_3 {
            height: 13.334rem;
            background: url(/pages/assets/img/SketchPngadad7755f86d7e2e093fc05ed2cb7e07ded68908f623927d7ede290bb4486fd7.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 1.6rem;
            width: 18.107rem;
            .box_25 {
              width: 15.974rem;
              height: 2.56rem;
              margin: 1.067rem 0 0 1.067rem;
              .paragraph_4 {
                width: 10.214rem;
                height: 2.347rem;
                overflow-wrap: break-word;
                color: rgba(0, 0, 0, 1);
                font-size: 0.64rem;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                line-height: 1.174rem;
                margin-top: 0.214rem;
              }
              .image_5 {
                width: 2.667rem;
                height: 2rem;
              }
            }
            .text-wrapper_22 {
              width: 11.2rem;
              height: 2.134rem;
              margin: 2.027rem 0 5.547rem 1.1rem;
              .paragraph_5 {
                width: 11.2rem;
                height: 2.134rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.533rem;
                font-weight: NaN;
                text-align: left;
                line-height: 1.067rem;
              }
            }
          }
        }
        .image_6 {
          width: 12.667rem;
          height: 12.4rem;
          margin-top: 0.507rem;
        }
      }
      .text-wrapper_23 {
        position: absolute;
        left: 39.147rem;
        top: 2.667rem;
        width: 5.12rem;
        height: 1.28rem;
        .text_21 {
          width: 5.12rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 1.28rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          white-space: nowrap;
          line-height: 1.28rem;
        }
      }
      .section_7 {
        position: absolute;
        left: 26.134rem;
        top: 11.174rem;
        width: 18.107rem;
        height: 13.334rem;
                  background: url(/pages/assets/img/SketchPngadad7755f86d7e2e093fc05ed2cb7e07ded68908f623927d7ede290bb4486fd7.png)
            100% no-repeat;
        background-size: 100% 100%;
        .text-group_11 {
          width: 12.16rem;
          height: 6.507rem;
          margin: 1.28rem 0 0 1.067rem;
          .paragraph_6 {
            width: 12.16rem;
            height: 3.52rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            line-height: 1.174rem;
          }
          .paragraph_7 {
            width: 9.947rem;
            height: 2.134rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.533rem;
            font-weight: NaN;
            text-align: left;
            line-height: 1.067rem;
            margin: 0.854rem 0 0 0.174rem;
          }
        }
        .image_7 {
          width: 2.667rem;
          height: 2rem;
          margin: 1.067rem 1.067rem 0 0;
        }
      }
    }
    .image_8 {
      position: absolute;
      left: 0;
      top: 54rem;
      width: 51.2rem;
      height: 7.627rem;
    }
  }
  .box_6 {
    background-color: rgba(255, 255, 255, 1);
    position: relative;
    width: 51.2rem;
    height: 35.36rem;
    margin-top: -0.026rem;
    .image_9 {
      width: 11.787rem;
      height: 2.507rem;
      margin: 3.227rem 0 0 7.174rem;
    }
    .box_7 {
      position: relative;
      width: 37.36rem;
      height: 20.294rem;
              background: url(/pages/assets/img/SketchPnge994b70d328b4be233e490428b5dff478249e251d60632cfb1c653c88734eea7.png)
          0rem -0.027rem no-repeat;
      background-size: 37.36rem 20.32rem;
      margin: 2.587rem 0 6.747rem 6.907rem;
      .text_22 {
        width: 12.747rem;
        height: 1.2rem;
        overflow-wrap: break-word;
        color: rgba(33, 135, 250, 1);
        font-size: 0.853rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.2rem;
        margin: 1.6rem 0 0 12.32rem;
      }
      .block_4 {
        background-color: rgba(255, 255, 255, 1);
        width: 21.547rem;
        height: 6.294rem;
        border: 1px solid rgba(33, 135, 250, 0.3);
        margin: 1.6rem 0 0 14.96rem;
        .group_23 {
          width: 12.64rem;
          height: 4.987rem;
          margin: 0.64rem 0 0 0.854rem;
          .text_24 {
            width: 1.894rem;
            height: 0.88rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.64rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.88rem;
          }
          .paragraph_8 {
            width: 12.64rem;
            height: 2.08rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.694rem;
            margin-top: 0.427rem;
          }
          .text-wrapper_9 {
            height: 0.96rem;
            background: url(/pages/assets/img/SketchPng9691728238ed5676c51cf905e696ab01d85d666aeaeb93559fbeb87047ce12c2.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 0.64rem;
            width: 8.667rem;
            .text_25 {
              width: 6.347rem;
              height: 0.534rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.534rem;
              margin: 0.214rem 0 0 0.427rem;
            }
          }
        }
        .image_10 {
          width: 6.747rem;
          height: 5.014rem;
          margin: 0.64rem 0.64rem 0 0.667rem;
        }
      }
      .block_5 {
        background-color: rgba(255, 255, 255, 1);
        width: 21.547rem;
        height: 6.294rem;
        border: 1px solid rgba(33, 135, 250, 0.3);
        margin: 0.854rem 0 2.454rem 14.96rem;
        .box_26 {
          width: 12.64rem;
          height: 4.347rem;
          margin: 0.64rem 0 0 0.854rem;
          .text_26 {
            width: 1.894rem;
            height: 0.88rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.64rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.88rem;
          }
          .text_27 {
            width: 12.64rem;
            height: 1.387rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.694rem;
            margin-top: 0.427rem;
          }
          .text-wrapper_10 {
            height: 0.96rem;
            background: url(/pages/assets/img/SketchPng9691728238ed5676c51cf905e696ab01d85d666aeaeb93559fbeb87047ce12c2.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 0.694rem;
            width: 8.667rem;
            .text_28 {
              width: 5.974rem;
              height: 0.427rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.427rem;
              margin: 0.267rem 0 0 0.427rem;
            }
          }
        }
        .group_12 {
          width: 6.747rem;
          height: 5.014rem;
          background: url(/pages/assets/img/5b26ce730f564a5b996caa585ea20918_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin: 0.64rem 0.64rem 0 0.667rem;
          .section_8 {
            border-radius: 50%;
            width: 0.214rem;
            height: 0.214rem;
            border: 2px solid rgba(255, 255, 255, 0.5);
            margin: 4.294rem 0 0 3.654rem;
          }
          .text_29 {
            width: 2.667rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.266rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
            margin: 4.214rem 0.134rem 0 0.08rem;
          }
        }
      }
      .block_6 {
        background-color: rgba(33, 135, 250, 1);
        position: absolute;
        left: 0.854rem;
        top: 4.4rem;
        width: 13.254rem;
        height: 20.587rem;
        justify-content: flex-center;
        .image-text_8 {
          width: 4.667rem;
          height: 1.6rem;
          margin: 0.854rem 0 0 0.854rem;
          .image_11 {
            width: 1.6rem;
            height: 1.6rem;
          }
          .text-group_4 {
            width: 2.534rem;
            height: 0.88rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.64rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.88rem;
            margin-top: 0.374rem;
          }
        }
        .paragraph_9 {
          width: 11.547rem;
          height: 2.32rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: justify;
          line-height: 0.8rem;
          margin: 0.72rem 0 0 0.854rem;
        }
        .image_12 {
          width: 11.547rem;
          height: 0.027rem;
          margin: 0.854rem 0 0 0.854rem;
        }
        .text_30 {
          width: 2.8rem;
          height: 0.8rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.4rem;
          font-weight: NaN;
          text-align: justify;
          line-height: 0.8rem;
          margin: 1.04rem 0 0 0.854rem;
        }
        .group_24 {
          width: 11.547rem;
          height: 1.067rem;
          margin: 0.534rem 0 0 0.854rem;
          .text-wrapper_11 {
            height: 1.067rem;
            background: url(/pages/assets/img/SketchPng4cfb3e84f609b59943769a46b71ff301e62405c9b228a3c22083eb090b722795.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 2.054rem;
            .text_31 {
              width: 1.2rem;
              height: 0.427rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.4rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.427rem;
              margin: 0.32rem 0 0 0.427rem;
            }
          }
          .text-wrapper_12 {
            height: 1.067rem;
            background: url(/pages/assets/img/SketchPng4cfb3e84f609b59943769a46b71ff301e62405c9b228a3c22083eb090b722795.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 0.32rem;
            width: 2.054rem;
            .text_32 {
              width: 1.2rem;
              height: 0.427rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.4rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.427rem;
              margin: 0.32rem 0 0 0.427rem;
            }
          }
          .text-wrapper_13 {
            height: 1.067rem;
            background: url(/pages/assets/img/SketchPng4cfb3e84f609b59943769a46b71ff301e62405c9b228a3c22083eb090b722795.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 0.32rem;
            width: 2.054rem;
            .text_33 {
              width: 1.2rem;
              height: 0.427rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.4rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.427rem;
              margin: 0.32rem 0 0 0.427rem;
            }
          }
          .text-wrapper_14 {
            height: 1.067rem;
            background: url(/pages/assets/img/SketchPng4cfb3e84f609b59943769a46b71ff301e62405c9b228a3c22083eb090b722795.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 0.32rem;
            width: 2.054rem;
            .text_34 {
              width: 1.2rem;
              height: 0.427rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.4rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.427rem;
              margin: 0.32rem 0 0 0.427rem;
            }
          }
          .text-wrapper_15 {
            height: 1.067rem;
            background: url(/pages/assets/img/SketchPng4cfb3e84f609b59943769a46b71ff301e62405c9b228a3c22083eb090b722795.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 0.32rem;
            width: 2.054rem;
            .text_35 {
              width: 1.2rem;
              height: 0.427rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.4rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.427rem;
              margin: 0.32rem 0 0 0.427rem;
            }
          }
        }
        .group_14 {
          height: 8.88rem;
          width: 11.547rem;
          margin: 1.04rem 0 0.854rem 0.854rem;
          .block_7 {
            border-radius: 4px;
            background-image: url(/pages/assets/img/b17996aa3c104e98938f8dede77a5a05_mergeImage.png);
            background-repeat: no-repeat;
            background-size: cover;
            height: 6.667rem;
            border: 0.5px solid rgba(227, 227, 227, 1);
            width: 10.374rem;
            margin: 0.907rem 0 0 0.267rem;
            .group_15 {
              border-radius: 4px;
              background-image: url(/pages/assets/img/6201f5b197444eb2be141e49f66b8556_mergeImage.png);
              background-repeat: no-repeat;
              background-size: cover;
              width: 6.48rem;
              height: 4.214rem;
              border: 0.5px solid rgba(227, 227, 227, 1);
              margin: 2.854rem 0 0 4.534rem;
            }
          }
        }
      }
    }
    .text_36 {
      position: absolute;
      left: 6.907rem;
      top: 2.8rem;
      width: 6.4rem;
      height: 1.28rem;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 1.28rem;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 1.28rem;
    }
    .box_8 {
      background-color: rgba(255, 255, 255, 1);
      position: absolute;
      left: 21.867rem;
      top: 27.014rem;
      width: 21.547rem;
      height: 6.294rem;
      border: 1px solid rgba(33, 135, 250, 0.3);
      .text-group_12 {
        width: 12.64rem;
        height: 4.08rem;
        margin: 0.64rem 0 0 0.854rem;
        .text_37 {
          width: 5.734rem;
          height: 0.88rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 0.64rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 0.88rem;
        }
        .text_38 {
          width: 12.64rem;
          height: 2.774rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: justify;
          line-height: 0.694rem;
          margin-top: 0.427rem;
        }
      }
      .image_13 {
        width: 6.747rem;
        height: 5.014rem;
        margin: 0.64rem 0.64rem 0 0.667rem;
      }
    }
  }
  .box_9 {
    background-color: rgba(26, 32, 66, 1);
    position: relative;
    width: 51.2rem;
    height: 30.4rem;
    margin-top: -0.08rem;
    .image_14 {
      width: 17.04rem;
      height: 2.827rem;
      margin: 3.094rem 0 0 27.227rem;
    }
    .image_15 {
      width: 51.2rem;
      height: 6.774rem;
      margin-top: 17.707rem;
    }
    .text_39 {
      position: absolute;
      left: 39.147rem;
      top: 2.667rem;
      width: 5.12rem;
      height: 1.28rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1.28rem;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: right;
      white-space: nowrap;
      line-height: 1.28rem;
    }
    .box_10 {
      background-image: url(/pages/assets/img/c2c09a33e23e406a958c7fca858a1271_mergeImage.png);
      background-repeat: no-repeat;
      background-size: cover;
      position: absolute;
      left: 6.96rem;
      top: 8.427rem;
      width: 18.107rem;
      height: 19.547rem;
      .group_16 {
        position: relative;
        width: 18.107rem;
        height: 13.254rem;
        background: url(/pages/assets/img/SketchPngf900fad7c931aa7133684f80644f9743c77ea9e519b3ffb94af79cf4f9ea7e91.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text_40 {
          width: 7.68rem;
          height: 1.2rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.853rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 1.2rem;
          margin: 1.387rem 0 0 5.227rem;
        }
        .block_8 {
          width: 5.334rem;
          height: 1.947rem;
          background: url(/pages/assets/img/SketchPng9603765f8cde7e546e8fa2e81a62d2c75169eba0b26925d2f91b4171330279f4.png)
            0rem 0rem no-repeat;
          background-size: 5.334rem 1.974rem;
          margin: 0.854rem 0 0 6.4rem;
        }
        .text-wrapper_16 {
          width: 10.56rem;
          height: 2.134rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: NaN;
          text-align: right;
          line-height: 1.067rem;
          margin: 0.667rem 0 5.067rem 3.787rem;
          .paragraph_10 {
            width: 10.56rem;
            height: 2.134rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.64rem;
            font-weight: NaN;
            text-align: center;
            line-height: 1.067rem;
          }
          .text_41 {
            width: 10.56rem;
            height: 2.134rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: center;
            line-height: 1.067rem;
          }
          .text_42 {
            width: 10.56rem;
            height: 2.134rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.64rem;
            font-weight: NaN;
            text-align: left;
            line-height: 1.067rem;
          }
        }
        .text-wrapper_17 {
          background-color: rgba(255, 255, 255, 0.9);
          position: absolute;
          left: 1.067rem;
          top: 9.04rem;
          width: 15.947rem;
          height: 5.36rem;
          .paragraph_11 {
            width: 9.814rem;
            height: 1.494rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: center;
            line-height: 0.747rem;
            margin: 0.534rem 0 0 3.067rem;
          }
          .text_43 {
            width: 8.534rem;
            height: 0.747rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: right;
            white-space: nowrap;
            line-height: 0.747rem;
            margin: 1.28rem 0 1.307rem 3.707rem;
          }
        }
      }
      .paragraph_12 {
        width: 14.614rem;
        height: 2.56rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.48rem;
        font-weight: NaN;
        text-align: center;
        line-height: 0.854rem;
        margin: 2.32rem 0 1.414rem 2rem;
      }
    }
    .box_11 {
      background-image: url(/pages/assets/img/5f6b78840ed844ffb802cbc9923eb5b0_mergeImage.png);
      background-repeat: no-repeat;
      background-size: cover;
      position: absolute;
      left: 26.16rem;
      top: 8.427rem;
      width: 18.107rem;
      height: 19.547rem;
      .box_12 {
        position: relative;
        width: 18.107rem;
        height: 13.254rem;
        background: url(/pages/assets/img/SketchPngf900fad7c931aa7133684f80644f9743c77ea9e519b3ffb94af79cf4f9ea7e91.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin-left: 0.027rem;
        .text_44 {
          width: 7.68rem;
          height: 1.2rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.853rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 1.2rem;
          margin: 1.387rem 0 0 5.2rem;
        }
        .group_17 {
          width: 5.334rem;
          height: 1.947rem;
          background: url(/pages/assets/img/SketchPng9603765f8cde7e546e8fa2e81a62d2c75169eba0b26925d2f91b4171330279f4.png)
            0rem 0rem no-repeat;
          background-size: 5.334rem 1.974rem;
          margin: 0.854rem 0 0 6.374rem;
        }
        .paragraph_13 {
          width: 7.04rem;
          height: 2.134rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.64rem;
          font-weight: NaN;
          text-align: center;
          line-height: 1.067rem;
          margin: 0.667rem 0 5.067rem 5.52rem;
        }
        .group_18 {
          background-color: rgba(255, 255, 255, 0.9);
          position: absolute;
          left: 1.04rem;
          top: 9.04rem;
          width: 15.947rem;
          height: 5.36rem;
          .text-group_13 {
            width: 11.094rem;
            height: 4.267rem;
            margin: 0.534rem 0 0 2.427rem;
            .paragraph_14 {
              width: 6.827rem;
              height: 1.494rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: center;
              line-height: 0.747rem;
              margin-left: 2.134rem;
            }
            .paragraph_15 {
              width: 11.094rem;
              height: 2.24rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: center;
              line-height: 0.747rem;
              margin-top: 0.534rem;
            }
          }
        }
      }
      .paragraph_16 {
        width: 12.934rem;
        height: 2.56rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.48rem;
        font-weight: NaN;
        text-align: center;
        line-height: 0.854rem;
        margin: 2.32rem 0 1.414rem 3.68rem;
      }
    }
  }
  .box_13 {
    background-color: rgba(255, 255, 255, 1);
    height: 25.627rem;
    width: 51.2rem;
    position: relative;
    .image-wrapper_8 {
      width: 15.654rem;
      height: 2.827rem;
      margin: 3.28rem 0 0 6.907rem;
      .image_16 {
        width: 15.654rem;
        height: 2.827rem;
      }
    }
    .box_27 {
      width: 35.92rem;
      height: 14.32rem;
      margin: 2.534rem 0 2.667rem 8.294rem;
      .box_15 {
        background-image: url(/pages/assets/img/bb74d8d5f29f47d2b7f2cf80dc50eb11_mergeImage.png);
        background-repeat: no-repeat;
        background-size: cover;
        height: 14.32rem;
        width: 8.694rem;
        .box_28 {
          width: 3.787rem;
          height: 1.254rem;
          margin: 1.147rem 0 0 4.88rem;
          .image-wrapper_2 {
            background-color: rgba(255, 255, 255, 1);
            height: 1.254rem;
            width: 3.787rem;
            .image_17 {
              width: 3.12rem;
              height: 0.64rem;
              margin: 0.32rem 0 0 0.294rem;
            }
          }
        }
        .box_29 {
          width: 3.787rem;
          height: 1.254rem;
          margin: 1.227rem 0 0 0.56rem;
          .image-wrapper_3 {
            background-color: rgba(255, 255, 255, 1);
            height: 1.254rem;
            width: 3.787rem;
            .image_18 {
              width: 2.774rem;
              height: 0.8rem;
              margin: 0.214rem 0 0 0.507rem;
            }
          }
        }
        .image-wrapper_9 {
          width: 3.6rem;
          height: 1.014rem;
          margin-top: 2.907rem;
          .image_19 {
            width: 3.6rem;
            height: 1.014rem;
          }
        }
        .image-wrapper_10 {
          width: 3.627rem;
          height: 1.014rem;
          margin: 1.254rem 0 3.254rem 4.347rem;
          .image_20 {
            width: 3.627rem;
            height: 1.014rem;
          }
        }
      }
      .image-wrapper_11 {
        width: 2.267rem;
        height: 12.267rem;
        margin: 0.614rem 0 0 2.694rem;
        .image_21 {
          width: 2.267rem;
          height: 1.707rem;
        }
        .image_22 {
          width: 0.027rem;
          height: 2.134rem;
          margin: 0.747rem 0 0 1.12rem;
        }
        .image_23 {
          width: 2.267rem;
          height: 1.707rem;
          margin-top: 0.694rem;
        }
        .image_24 {
          width: 0.027rem;
          height: 2.134rem;
          margin: 0.747rem 0 0 1.12rem;
        }
        .image_25 {
          width: 2.267rem;
          height: 1.707rem;
          margin-top: 0.694rem;
        }
      }
      .block_11 {
        width: 19.467rem;
        height: 12.774rem;
        margin: 0.374rem 0 0 2.8rem;
        .text-wrapper_18 {
          width: 19.467rem;
          height: 2.187rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.094rem;
          .text_45 {
            width: 19.467rem;
            height: 2.187rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            line-height: 1.094rem;
          }
          .text_46 {
            width: 19.467rem;
            height: 2.187rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            line-height: 1.094rem;
          }
          .text_47 {
            width: 19.467rem;
            height: 2.187rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            line-height: 1.094rem;
          }
        }
        .text-wrapper_19 {
          width: 15.467rem;
          height: 1.094rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          line-height: 1.094rem;
          margin-top: 3.654rem;
          .text_48 {
            width: 15.467rem;
            height: 1.094rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            line-height: 1.094rem;
          }
          .text_49 {
            width: 15.467rem;
            height: 1.094rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            line-height: 1.094rem;
          }
          .text_50 {
            width: 15.467rem;
            height: 1.094rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            line-height: 1.094rem;
          }
        }
        .paragraph_17 {
          width: 12.8rem;
          height: 2.187rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.533rem;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: left;
          line-height: 1.094rem;
          margin-top: 3.654rem;
        }
      }
    }
    .text-wrapper_24 {
      position: absolute;
      left: 6.907rem;
      top: 2.667rem;
      width: 5.12rem;
      height: 1.28rem;
      .text_51 {
        width: 5.12rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
      }
    }
    .image_26 {
      position: absolute;
      left: 13.787rem;
      top: 14.134rem;
      width: 3.6rem;
      height: 1.2rem;
    }
    .image_27 {
      position: absolute;
      left: 6.934rem;
      top: 7.707rem;
      width: 2.534rem;
      height: 2.187rem;
    }
  }
  .box_19 {
    background-color: rgba(17, 26, 52, 1);
    width: 51.2rem;
    height: 11.04rem;
    .group_25 {
      width: 31.787rem;
      height: 3.44rem;
      margin: 1.36rem 0 0 9.6rem;
      .group_26 {
        width: 7.44rem;
        height: 2.934rem;
        margin-top: 0.507rem;
        .image-text_9 {
          width: 4.374rem;
          height: 0.374rem;
          .thumbnail_5 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_7 {
            width: 3.734rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
        .image-text_10 {
          width: 5.707rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_6 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_8 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
            .text_52 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
            .text_53 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
          }
        }
        .image-text_11 {
          width: 7.44rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_7 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_9 {
            width: 6.8rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
      }
      .image-text_12 {
        width: 3.36rem;
        height: 3.44rem;
        .group_19 {
          border-radius: 8px;
          background-image: url(/pages/assets/img/5639bdfd95b0448a8ab43d201ab51531_mergeImage.png);
          background-repeat: no-repeat;
          background-size: cover;
          width: 2.667rem;
          height: 2.667rem;
          margin-left: 0.347rem;
        }
        .text-group_10 {
          width: 3.36rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
          margin-top: 0.4rem;
        }
      }
    }
    .block_10 {
      width: 51.2rem;
      height: 4.054rem;
              background: url(/pages/assets/img/SketchPng5c8ba53f71bd92f957537da4d03c76390cd3535fdd5fe926d77e0f7c541f1f80.png)
          100% no-repeat;
      background-size: 100% 100%;
      margin: 1.387rem 0 0.8rem 0;
      .image_28 {
        width: 4.267rem;
        height: 0.64rem;
        margin: 0.854rem 0 0 9.6rem;
      }
      .paragraph_18 {
        width: 7.227rem;
        height: 1.494rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.426rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        line-height: 0.747rem;
        margin: 0.587rem 0 0.48rem 9.6rem;
      }
    }
  }
}
