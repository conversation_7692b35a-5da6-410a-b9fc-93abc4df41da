.page {
  background-color: rgba(251, 251, 251, 1);
  position: relative;
  width: 51.227rem;
  overflow: hidden;
  padding-top: 2.187rem;
  .group_1 {
    background-color: rgba(255, 255, 255, 1);
    height: 2.187rem;
    width: 51.2rem;
    .block_7 {
      width: 40.534rem;
      height: 0.64rem;
      margin: 0.774rem 0 0 5.334rem;
      .image_1 {
        width: 4.267rem;
        height: 0.64rem;
      }
      .text_1 {
        width: 0.907rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 15.867rem;
      }
      .text_2 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 2.08rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.08rem 0 0 0.107rem;
      }
      .text_3 {
        width: 3.174rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_4 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_5 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_6 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
    }
    .block_8 {
      width: 1.814rem;
      height: 0.08rem;
      margin: 0.24rem 0 0.454rem 25.04rem;
      .group_2 {
        background-color: rgba(33, 135, 250, 1);
        width: 1.814rem;
        height: 0.08rem;
      }
    }
  }
  .group_3 {
    background-image: url(/pages/assets/img/96b75ed5d39647758a258deee139f405_mergeImage.png);
    background-repeat: no-repeat;
    background-size: cover;
    width: 51.2rem;
    height: 20.8rem;
    margin-left: 0.027rem;
    .text-group_22 {
      width: 22.507rem;
      height: 6.96rem;
      margin: 5.707rem 0 0 6.934rem;
      .text-wrapper_1 {
        width: 22.507rem;
        height: 4.534rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-weight: NaN;
        text-align: left;
        line-height: 2.267rem;
        .text_7 {
          width: 22.507rem;
          height: 4.534rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 1.76rem;
          font-weight: NaN;
          text-align: left;
          line-height: 2.267rem;
        }
        .paragraph_1 {
          width: 22.507rem;
          height: 4.534rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 1.333rem;
          font-weight: NaN;
          text-align: left;
          line-height: 2.267rem;
        }
        .text_8 {
          width: 22.507rem;
          height: 4.534rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 1.333rem;
          font-family: PingFangSC-Light;
          font-weight: 300;
          text-align: left;
          line-height: 2.267rem;
        }
      }
      .text_9 {
        width: 20.214rem;
        height: 1.76rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Thin;
        font-weight: 100;
        text-align: left;
        line-height: 0.88rem;
        margin-top: 0.667rem;
      }
    }
    .image_2 {
      width: 5.44rem;
      height: 0.107rem;
      margin: 7.227rem 0 0.8rem 22.88rem;
    }
  }
  .group_4 {
    background-color: rgba(255, 255, 255, 1);
    width: 51.2rem;
    height: 26.934rem;
    margin-left: 0.027rem;
    .text-group_23 {
      width: 12.747rem;
      height: 2.56rem;
      margin: 2.667rem 0 0 19.2rem;
      .text_10 {
        width: 5.12rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
        margin-left: 3.814rem;
      }
      .text_11 {
        width: 12.747rem;
        height: 0.534rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.533rem;
        text-transform: uppercase;
        font-weight: NaN;
        text-align: right;
        white-space: nowrap;
        line-height: 0.534rem;
        margin-top: 0.747rem;
      }
    }
    .section_7 {
      width: 32.507rem;
      height: 9.174rem;
      margin: 2.4rem 0 0 8rem;
      .section_1 {
        height: 8.374rem;
        background: url(/pages/assets/img/SketchPng7d039d12b8c8ef1ab92f3aacfc5c75e2ea28bbb463ebba11faaf97d75f0bc83d.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin-top: 0.8rem;
        width: 17.28rem;
        .box_1 {
          background-image: url(/pages/assets/img/488a54b556e34aaea5068a3fa19c1cc0_mergeImage.png);
          background-repeat: no-repeat;
          background-size: cover;
          width: 17.28rem;
          height: 8.374rem;
        }
      }
      .group_16 {
        width: 12.4rem;
        height: 8.107rem;
        .group_17 {
          width: 10.614rem;
          height: 2.134rem;
          .image_3 {
            width: 2.134rem;
            height: 2.134rem;
          }
          .text-wrapper_2 {
            width: 7.84rem;
            height: 0.88rem;
            overflow-wrap: break-word;
            font-size: 0;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.88rem;
            margin-top: 0.64rem;
            .text_12 {
              width: 7.84rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.64rem;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
            .text_13 {
              width: 7.84rem;
              height: 0.88rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.64rem;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 0.88rem;
            }
          }
        }
        .paragraph_2 {
          width: 12.4rem;
          height: 2.027rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          text-transform: uppercase;
          font-weight: NaN;
          text-align: left;
          line-height: 1.014rem;
          margin-top: 0.8rem;
        }
        .group_7 {
          background-color: rgba(33, 135, 250, 1);
          width: 4.107rem;
          height: 1.547rem;
          margin-top: 1.6rem;
          .image-text_11 {
            width: 3.04rem;
            height: 0.587rem;
            margin: 0.48rem 0 0 0.534rem;
            .text-group_3 {
              width: 1.707rem;
              height: 0.587rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.426rem;
              text-transform: uppercase;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.587rem;
            }
            .image_4 {
              width: 1.014rem;
              height: 0.534rem;
              margin-top: 0.027rem;
            }
          }
        }
      }
    }
    .list_2 {
      width: 37.334rem;
      height: 6.934rem;
      justify-content: space-between;
      margin: 1.067rem 0 2.134rem 6.934rem;
      .list-items_1 {
        width: 7.04rem;
        height: 6.934rem;
        background: url(/pages/assets/img/SketchPng510f1eff0eb4a2621ba6108deaa56e4f775acb19391b68e44b6121352bd7273e.png)
          100% no-repeat;
        background-size: 100% 100%;
        margin-right: 0.534rem;
        .image_5 {
          width: 2.134rem;
          height: 2.134rem;
          margin: 0.8rem 0 0 2.454rem;
        }
        .text-group_24 {
          width: 4.694rem;
          height: 2.347rem;
          margin: 0.64rem 0 1.014rem 1.174rem;
          .text_14 {
            width: 4.694rem;
            height: 0.747rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: center;
            white-space: nowrap;
            line-height: 0.747rem;
            margin-left: 0;
          }
          .text_15 {
            width: 5.094rem;
            height: 1.174rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: center;
            line-height: 0.587rem;
            margin-top: 0.427rem;
          }
        }
      }
    }
  }
  .group_8 {
    background-color: rgba(26, 32, 66, 1);
    position: relative;
    width: 51.2rem;
    height: 46.294rem;
    margin-left: 0.027rem;
    .text-group_25 {
      width: 8.534rem;
      height: 2.56rem;
      margin: 2.667rem 0 0 21.334rem;
      .text_16 {
        width: 7.68rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
        margin-left: 0.427rem;
      }
      .text_17 {
        width: 8.534rem;
        height: 0.534rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.533rem;
        text-transform: uppercase;
        font-weight: NaN;
        text-align: right;
        white-space: nowrap;
        line-height: 0.534rem;
        margin-top: 0.747rem;
      }
    }
    .section_8 {
      width: 37.387rem;
      height: 11.734rem;
      margin: 1.867rem 0 0 6.907rem;
      .box_11 {
        width: 18.72rem;
        height: 11.734rem;
        background: url(/pages/assets/img/SketchPng37aeb0fafd372ed5b583dc3e9342eca0fae085910028d87fc840b3e6908da8c7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .box_12 {
          width: 2.934rem;
          height: 1.28rem;
          margin: 1.334rem 0 0 1.334rem;
          .image_12 {
            width: 2.16rem;
            height: 1.28rem;
          }
          .text_40 {
            width: 2.507rem;
            height: 1.2rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.853rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 1.2rem;
            margin: 0.027rem 0 0 -1.734rem;
          }
        }
        .text_41 {
          width: 15.467rem;
          height: 1.814rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          text-transform: uppercase;
          font-weight: NaN;
          text-align: left;
          line-height: 0.907rem;
          margin: 0.8rem 0 0 1.334rem;
        }
        .image_13 {
          width: 15.467rem;
          height: 0.027rem;
          margin: 0.64rem 0 0 1.334rem;
        }
        .box_13 {
          width: 14.454rem;
          height: 1.76rem;
          margin: 0.774rem 0 0 1.334rem;
          .text-group_26 {
            width: 2.24rem;
            height: 1.76rem;
            .text-wrapper_3 {
              width: 0.88rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              font-size: 0;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
              margin-left: 0.587rem;
              .text_42 {
                width: 0.88rem;
                height: 1.067rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.746rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                white-space: nowrap;
                line-height: 1.067rem;
              }
              .text_43 {
                width: 0.88rem;
                height: 1.067rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.426rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                line-height: 1.067rem;
              }
            }
            .text_44 {
              width: 2.24rem;
              height: 0.534rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.534rem;
              margin-top: 0.16rem;
            }
          }
          .text-group_27 {
            width: 2.987rem;
            height: 1.76rem;
            margin-left: 1.334rem;
            .text-wrapper_4 {
              width: 1.307rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              font-size: 0;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
              margin-left: 0.854rem;
              .text_45 {
                width: 1.307rem;
                height: 1.067rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.746rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                white-space: nowrap;
                line-height: 1.067rem;
              }
              .text_46 {
                width: 1.307rem;
                height: 1.067rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.426rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                line-height: 1.067rem;
              }
            }
            .text_47 {
              width: 2.987rem;
              height: 0.534rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.534rem;
              margin-top: 0.16rem;
            }
          }
          .text-group_28 {
            width: 2.24rem;
            height: 1.76rem;
            margin-left: 1.334rem;
            .text-wrapper_5 {
              width: 1.334rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              font-size: 0;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
              margin-left: 0.507rem;
              .text_48 {
                width: 1.334rem;
                height: 1.067rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.746rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                white-space: nowrap;
                line-height: 1.067rem;
              }
              .text_49 {
                width: 1.334rem;
                height: 1.067rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.426rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                line-height: 1.067rem;
              }
            }
            .text_50 {
              width: 2.24rem;
              height: 0.534rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.534rem;
              margin-top: 0.16rem;
            }
          }
          .text-group_29 {
            width: 2.987rem;
            height: 1.76rem;
            margin-left: 1.334rem;
            .text-wrapper_6 {
              width: 1.307rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              font-size: 0;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
              margin-left: 0.854rem;
              .text_51 {
                width: 1.307rem;
                height: 1.067rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.746rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                white-space: nowrap;
                line-height: 1.067rem;
              }
              .text_52 {
                width: 1.307rem;
                height: 1.067rem;
                overflow-wrap: break-word;
                color: rgba(33, 135, 250, 1);
                font-size: 0.426rem;
                text-transform: uppercase;
                font-family: PingFangSC-Semibold;
                font-weight: 600;
                text-align: left;
                line-height: 1.067rem;
              }
            }
            .text_53 {
              width: 2.987rem;
              height: 0.534rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.534rem;
              margin-top: 0.16rem;
            }
          }
        }
        .image-text_12 {
          width: 2.72rem;
          height: 0.427rem;
          margin: 2.08rem 0 0.8rem 1.334rem;
          .text-group_30 {
            width: 1.707rem;
            height: 0.427rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.427rem;
          }
          .image_14 {
            width: 0.8rem;
            height: 0.427rem;
          }
        }
      }
      .box_14 {
        background-image: url(/pages/assets/img/6c4dffa94d5a49a0ae1ccce5d30bc4cd_mergeImage.png);
        background-repeat: no-repeat;
        background-size: cover;
        width: 18.667rem;
        height: 11.734rem;
      }
    }
    .section_9 {
      width: 18.72rem;
      height: 11.734rem;
      background: url(/pages/assets/img/SketchPng37aeb0fafd372ed5b583dc3e9342eca0fae085910028d87fc840b3e6908da8c7.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: -0.55rem 0 0 25.574rem;
      .box_15 {
        width: 2.934rem;
        height: 1.28rem;
        margin: 1.334rem 0 0 1.334rem;
        .image_15 {
          width: 2.16rem;
          height: 1.28rem;
        }
        .text_54 {
          width: 2.507rem;
          height: 1.2rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.853rem;
          text-transform: uppercase;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 1.2rem;
          margin: 0.027rem 0 0 -1.734rem;
        }
      }
      .text_55 {
        width: 15.467rem;
        height: 1.814rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.48rem;
        text-transform: uppercase;
        font-weight: NaN;
        text-align: left;
        line-height: 0.907rem;
        margin: 0.8rem 0 0 1.334rem;
      }
      .image_16 {
        width: 15.467rem;
        height: 0.027rem;
        margin: 0.64rem 0 0 1.334rem;
      }
      .list_3 {
        width: 15.467rem;
        height: 2.294rem;
        justify-content: space-between;
        margin: 0.774rem 0 0 1.334rem;
        .text-group_31 {
          position: relative;
          width: 2.24rem;
          height: 2.294rem;
          margin-right: 1.174rem;
          .text-wrapper_7 {
            position: absolute;
            left: 0.587rem;
            top: 0;
            width: 1.334rem;
            height: 1.067rem;
            overflow-wrap: break-word;
            font-size: 0;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 1.067rem;
            .text_56 {
              width: 1.334rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.746rem;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_57 {
              width: 1.334rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.426rem;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              line-height: 1.067rem;
            }
          }
          .text-wrapper_8 {
            position: absolute;
            left: 0.267rem;
            top: 0;
            width: 1.334rem;
            height: 1.067rem;
            overflow-wrap: break-word;
            font-size: 0;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 1.067rem;
            .text_58 {
              width: 1.334rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.746rem;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_59 {
              width: 1.334rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.426rem;
              text-transform: uppercase;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              line-height: 1.067rem;
            }
          }
          .text_60 {
            width: 2.614rem;
            height: 1.067rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            line-height: 0.534rem;
            margin-top: 1.227rem;
          }
        }
      }
      .image-text_13 {
        width: 2.72rem;
        height: 0.427rem;
        margin: 1.28rem 0 1.067rem 1.334rem;
        .text-group_32 {
          width: 1.707rem;
          height: 0.427rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 0.426rem;
          text-transform: uppercase;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.427rem;
        }
        .image_17 {
          width: 0.8rem;
          height: 0.427rem;
        }
      }
    }
    .section_10 {
      width: 37.387rem;
      height: 11.734rem;
      margin: 0.8rem 0 2.4rem 6.907rem;
      .section_11 {
        width: 18.72rem;
        height: 11.734rem;
        background: url(/pages/assets/img/SketchPng37aeb0fafd372ed5b583dc3e9342eca0fae085910028d87fc840b3e6908da8c7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .group_18 {
          width: 8.054rem;
          height: 1.28rem;
          margin: 1.334rem 0 0 1.334rem;
          .image_18 {
            width: 2.16rem;
            height: 1.28rem;
          }
          .text_61 {
            width: 7.627rem;
            height: 1.2rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.853rem;
            text-transform: uppercase;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 1.2rem;
            margin: 0.027rem 0 0 -1.734rem;
          }
        }
        .text_62 {
          width: 15.467rem;
          height: 1.814rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          text-transform: uppercase;
          font-weight: NaN;
          text-align: left;
          line-height: 0.907rem;
          margin: 0.8rem 0 0 1.334rem;
        }
        .image_19 {
          width: 15.467rem;
          height: 0.027rem;
          margin: 0.64rem 0 0 1.334rem;
        }
        .group_19 {
          width: 12.694rem;
          height: 1.014rem;
          margin: 1.04rem 0 0 1.334rem;
          .text-wrapper_9 {
            height: 1.014rem;
            background: url(/pages/assets/img/SketchPngb56405a074ef90b14a2eae5ba1cd8e08c7670b745d6e66a102ada8398f39ff5e.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 2.347rem;
            .text_63 {
              width: 1.707rem;
              height: 0.587rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.587rem;
              margin: 0.214rem 0 0 0.32rem;
            }
          }
          .text-wrapper_10 {
            height: 1.014rem;
            background: url(/pages/assets/img/SketchPngb56405a074ef90b14a2eae5ba1cd8e08c7670b745d6e66a102ada8398f39ff5e.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 0.534rem;
            width: 2.347rem;
            .text_64 {
              width: 1.707rem;
              height: 0.587rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.587rem;
              margin: 0.214rem 0 0 0.32rem;
            }
          }
          .text-wrapper_11 {
            height: 1.014rem;
            background: url(/pages/assets/img/SketchPngb7f8bb0c33ad800603bf3ddceee30292b601f7d0164d227d21dd04985af11ddc.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 0.534rem;
            width: 4.054rem;
            .text_65 {
              width: 3.414rem;
              height: 0.587rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.587rem;
              margin: 0.214rem 0 0 0.32rem;
            }
          }
          .text-wrapper_12 {
            height: 1.014rem;
            background: url(/pages/assets/img/SketchPngb56405a074ef90b14a2eae5ba1cd8e08c7670b745d6e66a102ada8398f39ff5e.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 0.534rem;
            width: 2.347rem;
            .text_66 {
              width: 1.707rem;
              height: 0.587rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.426rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.587rem;
              margin: 0.214rem 0 0 0.32rem;
            }
          }
        }
        .image-text_14 {
          width: 2.72rem;
          height: 0.427rem;
          margin: 2.294rem 0 1.067rem 1.334rem;
          .text-group_33 {
            width: 1.707rem;
            height: 0.427rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            text-transform: uppercase;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.427rem;
          }
          .image_20 {
            width: 0.8rem;
            height: 0.427rem;
          }
        }
      }
      .section_12 {
        background-image: url(/pages/assets/img/7d510c34337a47599abaf2bbe5075950_mergeImage.png);
        background-repeat: no-repeat;
        background-size: cover;
        height: 11.734rem;
        width: 18.667rem;
        .box_16 {
          height: 9.2rem;
          background: url(/pages/assets/img/SketchPnga086428520118285f85d49754e2d1fa1150e232730629b2a26fae694364dbde3.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 16.374rem;
          margin: 1.227rem 0 0 1.387rem;
          .box_17 {
            border-radius: 2px;
            background-image: url(/pages/assets/img/807efeb855224f29ab66d8e8c7750f1c_mergeImage.png);
            background-repeat: no-repeat;
            background-size: cover;
            width: 12.774rem;
            height: 7.814rem;
            border: 2px solid rgba(255, 255, 255, 1);
            margin: 0.347rem 0 0 1.814rem;
          }
        }
      }
    }
    .section_13 {
      background-image: url(/pages/assets/img/807f518fec94473e8639c28c3dfa9041_mergeImage.png);
      background-repeat: no-repeat;
      background-size: cover;
      position: absolute;
      left: 6.96rem;
      top: 19.627rem;
      width: 18.667rem;
      height: 11.734rem;
    }
  }
  .group_10 {
    background-color: rgba(255, 255, 255, 1);
    width: 51.2rem;
    height: 38.187rem;
    .text-group_34 {
      width: 9.6rem;
      height: 2.56rem;
      margin: 2.667rem 0 0 20.8rem;
      .text_24 {
        width: 5.12rem;
        height: 1.28rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 1.28rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: right;
        white-space: nowrap;
        line-height: 1.28rem;
        margin-left: 2.24rem;
      }
      .text_25 {
        width: 9.6rem;
        height: 0.534rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.533rem;
        text-transform: uppercase;
        font-weight: NaN;
        text-align: right;
        white-space: nowrap;
        line-height: 0.534rem;
        margin-top: 0.747rem;
      }
    }
    .block_4 {
      width: 37.334rem;
      height: 8.534rem;
      background: url(/pages/assets/img/SketchPnge06ca11ac067ef76dbac41503bb3f4fb2f5613f59866386a28239ff95bcc4b17.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 2.134rem 0 0 6.934rem;
      .box_6 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/9911750314186.png);
        background-repeat: no-repeat;
        background-size: cover;
        width: 11.734rem;
        height: 6.614rem;
        margin: 0.96rem 0 0 0.694rem;
      }
      .box_18 {
        width: 23.254rem;
        height: 6.507rem;
        margin: 1.014rem 0.8rem 0 0.854rem;
        .box_8 {
          width: 23.094rem;
          height: 2.454rem;
          background: url(/pages/assets/img/SketchPngb992f6199da537720d7e0e266dcadc5e31017b29ddbb8643310f7ae7c907c580.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_35 {
            width: 1.44rem;
            height: 1.654rem;
            margin: 0.427rem 0 0 0.427rem;
            .text_26 {
              width: 1.28rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(17, 26, 52, 1);
              font-size: 1.066rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_27 {
              width: 1.387rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 111, 131, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
              margin: 0.214rem 0 0 0.054rem;
            }
          }
        }
        .text-group_36 {
          width: 23.254rem;
          height: 3.307rem;
          margin-top: 0.747rem;
          .text_28 {
            width: 10.4rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(17, 26, 52, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_29 {
            width: 23.254rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(102, 111, 131, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.747rem;
            margin-top: 0.427rem;
          }
        }
      }
    }
    .block_5 {
      width: 37.334rem;
      height: 8.534rem;
      background: url(/pages/assets/img/SketchPngd787f278b402fce5b41424fd3f26804b0edc9e14eed485adbf764480810d85cd.png) -0.267rem -0.134rem
        no-repeat;
      background-size: 37.867rem 9.067rem;
      margin: 0.64rem 0 0 6.934rem;
      .section_3 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/9911750314186.png);
        background-repeat: no-repeat;
        background-size: cover;
        width: 11.734rem;
        height: 6.614rem;
        margin: 0.96rem 0 0 0.694rem;
      }
      .group_20 {
        width: 23.254rem;
        height: 6.507rem;
        margin: 1.014rem 0.8rem 0 0.854rem;
        .group_11 {
          width: 23.094rem;
          height: 2.454rem;
          background: url(/pages/assets/img/SketchPngb992f6199da537720d7e0e266dcadc5e31017b29ddbb8643310f7ae7c907c580.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_37 {
            width: 1.44rem;
            height: 1.654rem;
            margin: 0.427rem 0 0 0.427rem;
            .text_30 {
              width: 1.28rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(17, 26, 52, 1);
              font-size: 1.066rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_31 {
              width: 1.387rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 111, 131, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
              margin: 0.214rem 0 0 0.054rem;
            }
          }
        }
        .text-group_38 {
          width: 23.254rem;
          height: 3.307rem;
          margin-top: 0.747rem;
          .text_32 {
            width: 10.4rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(17, 26, 52, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_33 {
            width: 23.254rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(102, 111, 131, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.747rem;
            margin-top: 0.427rem;
          }
        }
      }
    }
    .block_6 {
      width: 37.334rem;
      height: 8.534rem;
      background: url(/pages/assets/img/SketchPnge06ca11ac067ef76dbac41503bb3f4fb2f5613f59866386a28239ff95bcc4b17.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 0.64rem 0 0 6.934rem;
      .group_12 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/9911750314186.png);
        background-repeat: no-repeat;
        background-size: cover;
        width: 11.734rem;
        height: 6.614rem;
        margin: 0.96rem 0 0 0.694rem;
      }
      .group_21 {
        width: 23.254rem;
        height: 6.507rem;
        margin: 1.014rem 0.8rem 0 0.854rem;
        .section_5 {
          width: 23.094rem;
          height: 2.454rem;
          background: url(/pages/assets/img/SketchPngb992f6199da537720d7e0e266dcadc5e31017b29ddbb8643310f7ae7c907c580.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_39 {
            width: 1.44rem;
            height: 1.654rem;
            margin: 0.427rem 0 0 0.427rem;
            .text_34 {
              width: 1.094rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(17, 26, 52, 1);
              font-size: 1.066rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_35 {
              width: 1.387rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 111, 131, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
              margin: 0.214rem 0 0 0.054rem;
            }
          }
        }
        .text-group_40 {
          width: 23.254rem;
          height: 3.307rem;
          margin-top: 0.747rem;
          .text_36 {
            width: 10.4rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(17, 26, 52, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_37 {
            width: 23.254rem;
            height: 2.24rem;
            overflow-wrap: break-word;
            color: rgba(102, 111, 131, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.747rem;
            margin-top: 0.427rem;
          }
        }
      }
    }
    .image-text_15 {
      width: 5.6rem;
      height: 0.747rem;
      margin: 1.6rem 0 1.6rem 22.8rem;
      .text-group_17 {
        width: 4.267rem;
        height: 0.747rem;
        overflow-wrap: break-word;
        color: rgba(33, 135, 250, 1);
        font-size: 0.533rem;
        text-transform: uppercase;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.747rem;
      }
      .image_10 {
        width: 1.014rem;
        height: 0.534rem;
        margin-top: 0.107rem;
      }
    }
  }
  .group_14 {
    background-color: rgba(17, 26, 52, 1);
    width: 51.2rem;
    height: 11.04rem;
    .box_19 {
      width: 31.787rem;
      height: 3.44rem;
      margin: 1.36rem 0 0 9.6rem;
      .block_9 {
        width: 7.44rem;
        height: 2.934rem;
        margin-top: 0.507rem;
        .image-text_16 {
          width: 4.374rem;
          height: 0.374rem;
          .thumbnail_5 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_18 {
            width: 3.734rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
        .image-text_17 {
          width: 5.707rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_6 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_19 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
            .text_38 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
            .text_39 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
          }
        }
        .image-text_18 {
          width: 7.44rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_7 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_20 {
            width: 6.8rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
      }
      .image-text_19 {
        width: 3.36rem;
        height: 3.44rem;
        .group_15 {
          border-radius: 8px;
          background-image: url(/pages/assets/img/e13d9355f9cb463191769802633149b7_mergeImage.png);
          background-repeat: no-repeat;
          background-size: cover;
          width: 2.667rem;
          height: 2.667rem;
          margin-left: 0.347rem;
        }
        .text-group_21 {
          width: 3.36rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
          margin-top: 0.4rem;
        }
      }
    }
    .box_10 {
      width: 51.2rem;
      height: 4.054rem;
      background: url(/pages/assets/img/SketchPng5c8ba53f71bd92f957537da4d03c76390cd3535fdd5fe926d77e0f7c541f1f80.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 1.387rem 0 0.8rem 0;
      .image_11 {
        width: 4.267rem;
        height: 0.64rem;
        margin: 0.854rem 0 0 9.6rem;
      }
      .paragraph_3 {
        width: 7.227rem;
        height: 1.494rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.426rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        line-height: 0.747rem;
        margin: 0.587rem 0 0.48rem 9.6rem;
      }
    }
  }
}
