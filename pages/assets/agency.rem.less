.page {
  background-color: rgba(243, 245, 249, 1);
  position: relative;
  width: 51.2rem;
  overflow: hidden;
  .box_1 {
    background-color: rgba(255, 255, 255, 1);
    height: 2.187rem;
    width: 51.2rem;
    .group_3 {
      width: 40.534rem;
      height: 0.64rem;
      margin: 0.774rem 0 0 5.334rem;
      .image_1 {
        width: 4.267rem;
        height: 0.64rem;
      }
      .text_1 {
        width: 0.907rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 15.867rem;
      }
      .text_2 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 2.08rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.08rem 0 0 0.107rem;
      }
      .text_3 {
        width: 3.174rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_4 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_5 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
      .text_6 {
        width: 1.814rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.453rem;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.08rem 0 0 1.6rem;
      }
    }
    .group_4 {
      width: 1.814rem;
      height: 0.08rem;
      margin: 0.24rem 0 0.454rem 33.227rem;
      .group_1 {
        background-color: rgba(33, 135, 250, 1);
        width: 1.814rem;
        height: 0.08rem;
      }
    }
  }
  .text-wrapper_1 {
    background-image: url(/pages/assets/img/ebc83f13d3b94c74b5f80aa5e3ff9fdf_mergeImage.png);
    background-repeat: no-repeat;
    background-size: cover;
    width: 51.2rem;
    height: 9.067rem;
    justify-content: flex-center;

    .text_7 {
      width: 6.4rem;
      height: 2.24rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 1.6rem;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 2.24rem;
      margin: 2.934rem 0 0 10.587rem;
    }

    .text_8 {
      width: 6.24rem;
      height: 0.667rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 0.48rem;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 0.667rem;
      margin: 0.32rem 0 2.907rem 10.587rem;
    }
  }

  .block_1 {
    background-image: url(/pages/assets/img/6c1273e51e93432a8f63c8da439d1b92_mergeImage.png);
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    width: 51.2rem;
    height: 34.614rem;

    .text_13 {
      width: 26.88rem;
      height: 0.667rem;
      overflow-wrap: break-word;
      color: rgba(119, 119, 119, 1);
      font-size: 0.48rem;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 0.667rem;
      margin: 1.067rem 0 0 6.934rem;
    }

    .text_14 {
      width: 3.2rem;
      height: 0.747rem;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 0.533rem;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
      line-height: 0.747rem;
      margin: 1.334rem 0 0 6.934rem;
    }

    .text-group_5 {
      width: 10.134rem;
      height: 3.734rem;
      margin: 0.534rem 0 0 6.934rem;

      .paragraph_3 {
        width: 10.134rem;
        height: 1.92rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.533rem;
        font-weight: normal;
        text-align: left;
        line-height: 0.96rem;
      }

      .text_15 {
        width: 4.8rem;
        height: 0.747rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.533rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 0.747rem;
        margin-top: 1.067rem;
      }
    }

    .text-group_6 {
      width: 21.76rem;
      height: 3.734rem;
      margin: 0.534rem 0 0 6.934rem;

      .paragraph_4 {
        width: 21.76rem;
        height: 1.92rem;
        overflow-wrap: break-word;
        color: rgba(102, 102, 102, 1);
        font-size: 0.533rem;
        font-weight: normal;
        text-align: left;
        line-height: 0.96rem;
      }

      .text_16 {
        width: 4.8rem;
        height: 0.747rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.533rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 0.747rem;
        margin-top: 1.067rem;
      }
    }

    .group_5 {
      background-color: rgba(255, 255, 255, 1);
      width: 37.334rem;
      height: 19.254rem;
      margin: 0.907rem 0 2.107rem 6.934rem;

      .group_6 {
        background-color: rgba(33, 135, 250, 1);
        width: 37.334rem;
        height: 1.947rem;

        .image_3 {
          width: 37.334rem;
          height: 0.027rem;
        }

        .text-wrapper_3 {
          width: 33.654rem;
          height: 0.747rem;
          margin: 0.614rem 0 0 0.8rem;

          .text_17 {
            width: 1.6rem;
            height: 0.747rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.747rem;
          }

          .text_18 {
            width: 2.134rem;
            height: 0.747rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: right;
            white-space: nowrap;
            line-height: 0.747rem;
            margin-left: 8.747rem;
          }

          .text_19 {
            width: 2.134rem;
            height: 0.747rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: right;
            white-space: nowrap;
            line-height: 0.747rem;
            margin-left: 7.76rem;
          }

          .text_20 {
            width: 4.8rem;
            height: 0.747rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.533rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: right;
            white-space: nowrap;
            line-height: 0.747rem;
            margin-left: 6.48rem;
          }
        }

        .image_4 {
          width: 37.334rem;
          height: 0.027rem;
          margin-top: 0.534rem;
        }
      }

      .group_7 {
        position: relative;
        width: 34.747rem;
        height: 1.467rem;
        margin: 0.534rem 0 0 0.8rem;

        .text-group_7 {
          width: 4.027rem;
          height: 1.147rem;
          margin-top: 0.32rem;

          .text_21 {
            width: 1.707rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
          }

          .text_22 {
            width: 4.027rem;
            height: 0.454rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.454rem;
            margin-top: 0.107rem;
          }
        }

        .text-wrapper_4 {
          width: 5.12rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: right;
          line-height: 0.64rem;
          margin-left: 4.827rem;

          .paragraph_5 {
            width: 5.12rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_23 {
            width: 5.12rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_24 {
            width: 5.12rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }
        }

        .text-wrapper_5 {
          width: 5.12rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: right;
          line-height: 0.64rem;
          margin-left: 4.774rem;

          .paragraph_6 {
            width: 5.12rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_25 {
            width: 5.12rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_26 {
            width: 5.12rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }
        }

        .thumbnail_6 {
          width: 0.48rem;
          height: 0.48rem;
          margin: 0.774rem 0.347rem 0 10.054rem;
        }

        .text-wrapper_6 {
          position: absolute;
          left: 27.76rem;
          top: 0;
          width: 7.987rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: NaN;
          text-align: center;
          line-height: 0.64rem;

          .paragraph_7 {
            width: 6.987rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_27 {
            width: 6.987rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(40, 139, 250, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_28 {
            width: 6.987rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: left;
            line-height: 0.64rem;
          }
        }
      }

      .image_5 {
        width: 37.334rem;
        height: 0.027rem;
        margin-top: 0.294rem;
      }

      .text-wrapper_7 {
        width: 31.68rem;
        height: 0.64rem;
        margin: 0.827rem 0 0 0.8rem;

        .text_29 {
          width: 3.84rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.426rem;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 0.587rem;
          margin-top: 0.054rem;
        }

        .text_30 {
          width: 0.694rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 0.426rem;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin-left: 7.227rem;
        }

        .text_31 {
          width: 1.067rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(33, 135, 250, 1);
          font-size: 0.426rem;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin-left: 9.014rem;
        }

        .text_32 {
          width: 0.854rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(183, 183, 183, 1);
          font-size: 0.426rem;
          font-weight: normal;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin-left: 8.987rem;
        }
      }

      .image_6 {
        width: 37.334rem;
        height: 0.027rem;
        margin-top: 0.827rem;
      }

      .group_8 {
        width: 35.094rem;
        height: 1.574rem;
        margin: 0.48rem 0 0 0.8rem;

        .text-group_8 {
          width: 5.867rem;
          height: 1.147rem;
          margin-top: 0.427rem;

          .text_33 {
            width: 2.56rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
          }

          .text_34 {
            width: 5.867rem;
            height: 0.454rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.454rem;
            margin-top: 0.107rem;
          }
        }

        .text-wrapper_8 {
          width: 4.374rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.374rem 0 0 3.36rem;

          .text_35 {
            width: 4.374rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_36 {
            width: 4.374rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_37 {
            width: 4.374rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
        }

        .text-wrapper_9 {
          width: 4.374rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.374rem 0 0 5.52rem;

          .text_38 {
            width: 4.374rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_39 {
            width: 4.374rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_40 {
            width: 4.374rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
        }

        .text-wrapper_10 {
          width: 7.827rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: center;
          line-height: 0.64rem;
          margin-left: 4.774rem;

          .text_41 {
            width: 6.827rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_42 {
            width: 6.827rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .paragraph_8 {
            width: 6.827rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_43 {
            width: 6.827rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_44 {
            width: 6.827rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }
        }
      }

      .image_7 {
        width: 37.334rem;
        height: 0.027rem;
        margin-top: 0.267rem;
      }

      .group_9 {
        width: 35.094rem;
        height: 1.574rem;
        margin: 0.48rem 0 0 0.8rem;

        .text-group_9 {
          width: 5.227rem;
          height: 1.147rem;
          margin-top: 0.427rem;

          .text_45 {
            width: 2.56rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
          }

          .text_46 {
            width: 5.227rem;
            height: 0.454rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.454rem;
            margin-top: 0.107rem;
          }
        }

        .text-wrapper_11 {
          width: 5.227rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.374rem 0 0 3.627rem;

          .text_47 {
            width: 5.227rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_48 {
            width: 5.227rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_49 {
            width: 5.227rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
        }

        .text-wrapper_12 {
          width: 5.227rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.374rem 0 0 4.614rem;

          .text_50 {
            width: 5.227rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_51 {
            width: 5.227rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_52 {
            width: 5.227rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
        }

        .text-wrapper_13 {
          width: 7.98rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: center;
          line-height: 0.64rem;
          margin-left: 4.294rem;

          .text_53 {
            width: 7.68rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_54 {
            width: 7.68rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .paragraph_9 {
            width: 7.68rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_55 {
            width: 7.68rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_56 {
            width: 7.68rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }
        }
      }

      .image_8 {
        width: 37.334rem;
        height: 0.027rem;
        margin-top: 0.267rem;
      }

      .group_10 {
        width: 31.68rem;
        height: 1.574rem;
        margin: 0.48rem 0 0 0.8rem;

        .text-group_10 {
          width: 5.547rem;
          height: 1.147rem;
          margin-top: 0.427rem;

          .text_57 {
            width: 5.12rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 0.587rem;
          }

          .text_58 {
            width: 5.547rem;
            height: 0.454rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.454rem;
            margin-top: 0.107rem;
          }
        }

        .text-wrapper_14 {
          width: 7.854rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: center;
          line-height: 0.64rem;
          margin-left: 2.274rem;

          .text_59 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_60 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .paragraph_10 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_61 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_62 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }
        }

        .text-wrapper_15 {
          width: 7.854rem;
          height: 1.28rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: center;
          line-height: 0.64rem;
          margin-left: 1.96rem;

          .text_63 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_64 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .paragraph_11 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_65 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }

          .text_66 {
            width: 6.854rem;
            height: 1.28rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.64rem;
          }
        }

        .text_67 {
          width: 0.854rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(183, 183, 183, 1);
          font-size: 0.426rem;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.374rem 0 0 5.34rem;
        }
      }

      .image_9 {
        width: 37.334rem;
        height: 0.027rem;
        margin-top: 0.267rem;
      }

      .group_11 {
        width: 31.68rem;
        height: 0.64rem;
        margin: 0.854rem 0 0 0.8rem;

        .text_68 {
          width: 3.84rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.426rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 0.587rem;
          margin-top: 0.054rem;
        }

        .text_69 {
          width: 0.854rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(183, 183, 183, 1);
          font-size: 0.426rem;
          font-weight: normal;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin-left: 7.174rem;
        }

        .text-wrapper_16 {
          width: 4.107rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin-left: 7.494rem;

          .text_70 {
            width: 4.107rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_71 {
            width: 4.107rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(33, 135, 250, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }

          .text_72 {
            width: 4.107rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.426rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
        }

        .text_73 {
          width: 0.854rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(183, 183, 183, 1);
          font-size: 0.426rem;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 0.64rem;
          margin-left: 7.36rem;
        }
      }

      .image_10 {
        width: 37.334rem;
        height: 0.027rem;
        margin-top: 0.827rem;
      }

      .group_12 {
        width: 31.68rem;
        height: 2.347rem;
        margin: 0.427rem 0 0.507rem 0.8rem;

        .text_74 {
          width: 2.987rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.426rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 0.587rem;
          margin-top: 0.48rem;
        }

        .text_75 {
          width: 0.854rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(183, 183, 183, 1);
          font-size: 0.426rem;
          font-weight: normal;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.374rem 0 0 8.027rem;
        }

        .text-group_11 {
          width: 8.8rem;
          height: 2.347rem;
          margin-left: 5.067rem;

          .text-wrapper_17 {
            width: 8.64rem;
            height: 1.467rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: normal;
            text-align: center;
            line-height: 0.64rem;
            margin-left: 0.16rem;

            .text_76 {
              width: 8.64rem;
              height: 1.467rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.426rem;
              font-weight: normal;
              text-align: center;
              line-height: 0.64rem;
            }

            .text_77 {
              width: 8.64rem;
              height: 1.467rem;
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 0.426rem;
              font-weight: normal;
              text-align: left;
              line-height: 0.64rem;
            }

            .paragraph_12 {
              width: 8.64rem;
              height: 1.467rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.426rem;
              font-weight: normal;
              text-align: left;
              line-height: 0.64rem;
            }
          }

          .text_78 {
            width: 8.8rem;
            height: 0.907rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-weight: normal;
            text-align: left;
            line-height: 0.454rem;
            margin-top: -0.026rem;
          }
        }

        .text_79 {
          width: 0.854rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(183, 183, 183, 1);
          font-size: 0.426rem;
          font-weight: normal;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.374rem 0 0 5.094rem;
        }
      }
    }

    .text-wrapper_18 {
      background-color: rgba(255, 255, 255, 1);
      height: 2.16rem;
      border: 1px solid rgba(225, 225, 225, 1);
      width: 7.174rem;
      position: absolute;
      border-radius: 0.3rem;
      left: 38.427rem;
      top: 11.52rem;

      .text_80 {
        width: 6.4rem;
        height: 1.36rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.32rem;
        font-weight: NaN;
        text-align: left;
        line-height: 0.454rem;
        margin: 0.32rem 0 0 0.32rem;
      }
    }
  }

  .box_8 {
    background-color: rgba(17, 26, 52, 1);
    width: 51.2rem;
    height: 11.04rem;
    .section_2 {
      width: 31.787rem;
      height: 3.44rem;
      margin: 1.36rem 0 0 9.6rem;
      .group_13 {
        width: 7.44rem;
        height: 2.934rem;
        margin-top: 0.507rem;
        .image-text_5 {
          width: 4.374rem;
          height: 0.374rem;
          .thumbnail_7 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_12 {
            width: 3.734rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
        .image-text_6 {
          width: 5.707rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_8 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_13 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
            .text_81 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
            .text_82 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
          }
        }
        .image-text_7 {
          width: 7.44rem;
          height: 0.374rem;
          margin-top: 0.907rem;
          .thumbnail_9 {
            width: 0.374rem;
            height: 0.374rem;
          }
          .text-group_14 {
            width: 6.8rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
      }
      .image-text_8 {
        width: 3.36rem;
        height: 3.44rem;
        .group_14 {
          border-radius: 8px;
          background-image: url(/pages/assets/img/9b9e1b524c894b799f2b7b15cc6d4af1_mergeImage.png);
          width: 2.667rem;
          height: 2.667rem;
          margin-left: 0.347rem;
        }
        .text-group_15 {
          width: 3.36rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
          margin-top: 0.4rem;
        }
      }
    }
    .section_3 {
      width: 51.2rem;
      height: 4.054rem;
      background: url(/pages/assets/img/SketchPng5c8ba53f71bd92f957537da4d03c76390cd3535fdd5fe926d77e0f7c541f1f80.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 1.387rem 0 0.8rem 0;
      .image_11 {
        width: 4.267rem;
        height: 0.64rem;
        margin: 0.854rem 0 0 9.6rem;
      }
      .paragraph_13 {
        width: 7.227rem;
        height: 1.494rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.426rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        line-height: 0.747rem;
        margin: 0.587rem 0 0.48rem 9.6rem;
      }
    }
  }
}


/* 添加flex布局类 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

/* 聚焦医药及医疗行业模块样式 */
.box_11 {
  position: relative;
  width: 51.2rem;
  height: 26.68rem;
  margin-bottom: 0.027rem;

  .text-wrapper_21 {
    height: 10.667rem;
    background: linear-gradient(135deg, #2187fa 0%, #1a6df0 100%);
    width: 51.2rem;

    .paragraph_14 {
      width: 16.267rem;
      height: 2.667rem;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 1);
      font-size: 0.96rem;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: center;
      line-height: 1.334rem;
      margin: 2.667rem 0 0 17.467rem;
    }
  }

  .block_2 {
    background-color: rgba(17, 26, 52, 1);
    width: 51.174rem;
    height: 11.04rem;
    margin: 16.534rem 0 0 0.027rem;

    .group_15 {
      width: 31.787rem;
      height: 3.44rem;
      margin: 1.36rem 0 0 9.6rem;

      .section_10 {
        width: 7.44rem;
        height: 2.934rem;
        margin-top: 0.507rem;

        .image-text_9 {
          width: 4.374rem;
          height: 0.374rem;

          .thumbnail_10 {
            width: 0.374rem;
            height: 0.374rem;
          }

          .text-group_23 {
            width: 3.734rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }

        .image-text_10 {
          width: 5.707rem;
          height: 0.374rem;
          margin-top: 0.907rem;

          .thumbnail_11 {
            width: 0.374rem;
            height: 0.374rem;
          }

          .text-group_24 {
            width: 5.067rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;

            .text_83 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }

            .text_84 {
              width: 5.067rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.373rem;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
            }
          }
        }

        .image-text_11 {
          width: 7.44rem;
          height: 0.374rem;
          margin-top: 0.907rem;

          .thumbnail_12 {
            width: 0.374rem;
            height: 0.374rem;
          }

          .text-group_25 {
            width: 6.8rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.374rem;
          }
        }
      }

      .image-text_12 {
        width: 3.36rem;
        height: 3.44rem;

        .box_12 {
          border-radius: 8px;
          background-image: url(/pages/assets/img/9b9e1b524c894b799f2b7b15cc6d4af1_mergeImage.png);
          width: 2.667rem;
          height: 2.667rem;
          margin-left: 0.347rem;
        }

        .text-group_26 {
          width: 3.36rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.373rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.374rem;
          margin-top: 0.4rem;
        }
      }
    }

    .group_16 {
      width: 51.174rem;
      height: 4.054rem;
      background: linear-gradient(135deg, #111a34 0%, #1a2850 100%);
      margin: 1.387rem 0 0.8rem 0;

      .image_12 {
        width: 4.267rem;
        height: 0.64rem;
        margin: 0.854rem 0 0 9.6rem;
      }

      .paragraph_15 {
        width: 7.227rem;
        height: 1.494rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.426rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        line-height: 0.747rem;
        margin: 0.587rem 0 0.48rem 9.6rem;
      }
    }
  }

  .block_3 {
    width: 42.72rem;
    height: 17.067rem;
    margin: 2.667rem 0 1.707rem 6.934rem;

    .box_13 {
      background-color: rgba(255, 255, 255, 1);
      width: 37.334rem;
      height: 17.067rem;

      .group_17 {
        width: 20.214rem;
        height: 0.48rem;
        margin: 1.6rem 0 0 1.6rem;

        .text-wrapper_22 {
          width: 5.334rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;

          .text_85 {
            width: 5.334rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(255, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;
          }

          .text_86 {
            width: 5.334rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;
          }

          .text_87 {
            width: 5.334rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.373rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;
          }
        }

        .text-wrapper_23 {
          width: 2.347rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;

          .text_88 {
            width: 2.347rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(255, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;
          }

          .text_89 {
            width: 2.347rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;
          }
        }
      }

      .group_18 {
        width: 34.134rem;
        height: 1.6rem;
        margin: 0.64rem 0 0 1.6rem;

        .text-wrapper_24 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 8px;
          height: 1.6rem;
          border: 1px solid rgba(219, 219, 219, 1);
          width: 16.267rem;

          .text_90 {
            width: 5.28rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;
            margin: 0.56rem 0 0 0.534rem;
          }
        }

        .block_4 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 8px;
          width: 16.267rem;
          height: 1.6rem;
          border: 1px solid rgba(33, 135, 250, 1);

          .text_91 {
            width: 3.6rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 0.48rem;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;
            margin: 0.56rem 0 0 0.534rem;
          }

          .thumbnail_13 {
            width: 0.267rem;
            height: 0.48rem;
            margin: 0.56rem 0.827rem 0 11.04rem;
          }
        }
      }

      .group_19 {
        width: 37.654rem;
        height: 7.707rem;
        margin: 0.347rem 0 0 1.6rem;

        .block_5 {
          width: 16.267rem;
          height: 6.507rem;
          margin-top: 0.72rem;

          .text-wrapper_25 {
            width: 1.867rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;

            .text_92 {
              width: 1.867rem;
              height: 0.48rem;
              overflow-wrap: break-word;
              color: rgba(255, 0, 0, 1);
              font-size: 0.48rem;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.48rem;
            }

            .text_93 {
              width: 1.867rem;
              height: 0.48rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.48rem;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.48rem;
            }
          }

          .text-wrapper_26 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 8px;
            height: 1.6rem;
            border: 1px solid rgba(219, 219, 219, 1);
            margin-top: 0.64rem;
            width: 16.267rem;

            .text_94 {
              width: 1.44rem;
              height: 0.48rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.48rem;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.48rem;
              margin: 0.56rem 0 0 0.534rem;
            }
          }

          .text-wrapper_27 {
            width: 5.547rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 0.48rem;
            margin-top: 1.067rem;

            .text_95 {
              width: 5.547rem;
              height: 0.48rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.48rem;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.48rem;
            }

            .text_96 {
              width: 5.547rem;
              height: 0.48rem;
              overflow-wrap: break-word;
              color: rgba(0, 0, 0, 1);
              font-size: 0.373rem;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.48rem;
            }
          }

          .box_14 {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 8px;
            width: 16.267rem;
            height: 1.6rem;
            border: 1px solid rgba(219, 219, 219, 1);
            margin-top: 0.64rem;

            .text_97 {
              width: 4.32rem;
              height: 0.48rem;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 0.48rem;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 0.48rem;
              margin: 0.56rem 0 0 0.534rem;
            }

            .thumbnail_14 {
              width: 0.48rem;
              height: 0.267rem;
              margin: 0.667rem 0.56rem 0 10.374rem;
            }
          }
        }

        .image_13 {
          width: 19.92rem;
          height: 7.707rem;
        }
      }

      .text-wrapper_28 {
        background-color: rgba(33, 135, 250, 1);
        border-radius: 8px;
        height: 1.707rem;
        width: 8.934rem;
        margin: 1.654rem 0 1.334rem 14.214rem;

        .text_98 {
          width: 1.28rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.64rem;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: right;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.534rem 0 0 3.84rem;
        }
      }
    }

    .text_99 {
      width: 2.4rem;
      height: 0.667rem;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 0.48rem;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 0.667rem;
      margin-top: 5.387rem;
    }
  }

  .block_6 {
    background-color: rgba(255, 255, 255, 1);
    position: absolute;
    left: 6.934rem;
    top: 7.467rem;
    width: 37.334rem;
    height: 17.067rem;

    .group_20 {
      width: 20.214rem;
      height: 0.48rem;
      margin: 1.6rem 0 0 1.6rem;

      .text-wrapper_29 {
        width: 5.334rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;

        .text_100 {
          width: 5.334rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
        }

        .text_101 {
          width: 5.334rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
        }

        .text_102 {
          width: 5.334rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.373rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
        }
      }

      .text-wrapper_30 {
        width: 2.347rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;

        .text_103 {
          width: 2.347rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
        }

        .text_104 {
          width: 2.347rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
        }
      }
    }

    .group_21 {
      width: 34.134rem;
      height: 1.6rem;
      margin: 0.64rem 0 0 1.6rem;

      .text-wrapper_31 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        height: 1.6rem;
        border: 1px solid rgba(219, 219, 219, 1);
        width: 16.267rem;

        .text_105 {
          width: 4.32rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
          margin: 0.56rem 0 0 0.534rem;
        }
      }

      .box_15 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        width: 16.267rem;
        height: 1.6rem;
        border: 1px solid rgba(219, 219, 219, 1);

        .text_106 {
          width: 2.4rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
          margin: 0.56rem 0 0 0.534rem;
        }

        .thumbnail_15 {
          width: 0.48rem;
          height: 0.267rem;
          margin: 0.667rem 0.72rem 0 12.134rem;
        }
      }
    }

    .group_22 {
      width: 20.214rem;
      height: 0.48rem;
      margin: 1.067rem 0 0 1.6rem;

      .text-wrapper_32 {
        width: 1.867rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;

        .text_107 {
          width: 1.867rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
        }

        .text_108 {
          width: 1.867rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
        }
      }

      .text-wrapper_33 {
        width: 2.347rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;

        .text_109 {
          width: 2.347rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
        }

        .text_110 {
          width: 2.347rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
        }
      }
    }

    .group_23 {
      width: 34.134rem;
      height: 1.6rem;
      margin: 0.64rem 0 0 1.6rem;

      .text-wrapper_34 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        height: 1.6rem;
        border: 1px solid rgba(219, 219, 219, 1);
        width: 16.267rem;

        .text_111 {
          width: 3.36rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
          margin: 0.56rem 0 0 0.534rem;
        }
      }

      .text-wrapper_35 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        height: 1.6rem;
        border: 1px solid rgba(219, 219, 219, 1);
        width: 16.267rem;

        .text_112 {
          width: 4.32rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.48rem;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 0.48rem;
          margin: 0.56rem 0 0 0.534rem;
        }
      }
    }

    .text-wrapper_36 {
      width: 5.547rem;
      height: 0.48rem;
      overflow-wrap: break-word;
      font-size: 0;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 0.48rem;
      margin: 1.067rem 0 0 1.6rem;

      .text_113 {
        width: 5.547rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.48rem;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
      }

      .text_114 {
        width: 5.547rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.373rem;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
      }
    }

    .group_24 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 8px;
      width: 16.267rem;
      height: 1.6rem;
      border: 1px solid rgba(219, 219, 219, 1);
      margin: 0.64rem 0 0 1.6rem;

      .text_115 {
        width: 4.32rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.48rem;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 0.48rem;
        margin: 0.56rem 0 0 0.534rem;
      }

      .thumbnail_16 {
        width: 0.48rem;
        height: 0.267rem;
        margin: 0.667rem 0.56rem 0 10.374rem;
      }
    }

    .text-wrapper_37 {
      background-color: rgba(33, 135, 250, 1);
      border-radius: 8px;
      height: 1.707rem;
      width: 8.934rem;
      margin: 2.134rem 0 1.334rem 14.214rem;
      border: none;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover:not(:disabled) {
        background-color: rgba(24, 119, 242, 1);
      }

      &:disabled {
        background-color: rgba(153, 153, 153, 1);
        cursor: not-allowed;
      }

      .text_116 {
        width: 1.28rem;
        height: 0.64rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.64rem;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: right;
        white-space: nowrap;
        line-height: 0.64rem;
        margin: 0.534rem 0 0 3.84rem;
      }
    }
  }
}

/* 表单元素样式 */
.form-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-weight: normal;
  text-align: left;
  line-height: 0.48rem;
  padding: 0.56rem 0 0 0.534rem;

  &::placeholder {
    color: rgba(153, 153, 153, 1);
    font-size: 0.48rem;
    font-weight: normal;
  }

  &:focus {
    outline: none;
  }
}

.form-select {
  width: 48%;
  height: 1.6rem;
  border: 1px solid rgba(219, 219, 219, 1);
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-weight: normal;
  padding: 0.56rem 0.534rem;
  outline: none;
  cursor: pointer;

  &:focus {
    border-color: rgba(33, 135, 250, 1);
    outline: none;
  }

  &:disabled {
    background-color: rgba(245, 245, 245, 1);
    color: rgba(153, 153, 153, 1);
    cursor: not-allowed;
  }

  option {
    color: rgba(0, 0, 0, 1);
    font-size: 0.48rem;
    padding: 0.4rem;
  }
}

.form-select-industry {
  width: 100%;
  height: 1.6rem;
  border: 1px solid rgba(219, 219, 219, 1);
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-weight: normal;
  padding: 0.56rem 0.534rem;
  outline: none;
  cursor: pointer;

  &:focus {
    border-color: rgba(33, 135, 250, 1);
    outline: none;
  }

  option {
    color: rgba(0, 0, 0, 1);
    font-size: 0.48rem;
    padding: 0.4rem;
  }
}

/* 提交按钮样式调整 */
.text-wrapper_37 {
  background-color: rgba(33, 135, 250, 1);
  border-radius: 8px;
  height: 1.707rem;
  width: 8.934rem;
  margin: 2.134rem 0 1.334rem 14.214rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover:not(:disabled) {
    background-color: rgba(24, 119, 242, 1);
  }

  &:disabled {
    background-color: rgba(153, 153, 153, 1);
    cursor: not-allowed;
  }

  .text_116 {
    width: 1.28rem;
    height: 0.64rem;
    overflow-wrap: break-word;
    color: rgba(255, 255, 255, 1);
    font-size: 0.64rem;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: right;
    white-space: nowrap;
    line-height: 0.64rem;
    margin: 0.534rem 0 0 3.84rem;
  }
}

/* 输入框容器样式调整 */
.box_15,
.text-wrapper_31,
.text-wrapper_34,
.text-wrapper_35,
.text-wrapper_38 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 16.267rem;
  height: 1.6rem;
  border: 1px solid rgba(219, 219, 219, 1);
}

/* 行业输入框容器特殊样式 */
.text-wrapper_38 {
  width: 100% !important;
}

.form-input-location {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(0, 0, 0, 1);
  font-size: 0.48rem;
  font-weight: normal;
  text-align: left;
  line-height: 0.48rem;
  border-radius: 8px;

  &::placeholder {
    color: rgba(153, 153, 153, 1);
    font-size: 0.48rem;
    font-weight: normal;
  }

  &:focus {
    outline: none;
  }
}
