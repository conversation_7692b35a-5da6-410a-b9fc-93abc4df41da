.page {
  background-color: rgba(243, 245, 249, 1);
  position: relative;
  width: 51.2rem;

  overflow: hidden;

  .block_3 {
    width: 51.2rem;
    height: 55.76rem;
    margin-top: 2.187rem; // 添加顶部导航栏的高度作为margin-top
    .text-wrapper_1 {
      background-image: url(/pages/assets/img/5e29a507865c43e396b2cd9f37677627_mergeImage.png);
      background-repeat: no-repeat;
      background-size: cover;
      width: 51.2rem;
      height: 9.067rem;
      justify-content: flex-center;
      .text_7 {
        width: 6.4rem;
        height: 2.24rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 1.6rem;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 2.24rem;
        margin: 2.934rem 0 0 10.587rem;
      }
      .text_8 {
        width: 7.68rem;
        height: 0.667rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.48rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.667rem;
        margin: 0.32rem 0 2.907rem 10.587rem;
      }
    }
    .group_3 {
      width: 37.334rem;
      height: 7.52rem;
      background: url(./img/SketchPng6afabe73a74054cbf3e3261c24c0b7e28382e7eaa467d5891c4d0c4ebb41d83f.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 0.64rem 0 0 6.934rem;
      .section_1 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/e882f6c854e74ea49c9af5c6209cec62_mergeImage.png);
        background-repeat: no-repeat;
        background-size: cover;
        width: 10.134rem;
        height: 6.24rem;
        margin: 0.64rem 0 0 0.64rem;
      }
      .section_2 {
        width: 25.28rem;
        height: 5.654rem;
        margin: 0.64rem 0.64rem 0 0;
        .group_4 {
          width: 25.28rem;
          height: 2.454rem;
          background: url(./img/SketchPngef5447757b4e9e70bd579431faf2a92e31570e21210f316778221630763837a0.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_1 {
            width: 2.854rem;
            height: 1.707rem;
            margin: 0.374rem 0 0 0.427rem;
            .text_9 {
              width: 2.854rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(17, 26, 52, 1);
              font-size: 1.066rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_10 {
              width: 0.907rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 111, 131, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
              margin: 0.267rem 0 0 0.054rem;
            }
          }
        }
        .text-group_2 {
          width: 25.28rem;
          height: 2.56rem;
          margin-top: 0.64rem;
          .text_11 {
            width: 10.4rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(17, 26, 52, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_12 {
            width: 25.28rem;
            height: 1.494rem;
            overflow-wrap: break-word;
            color: rgba(102, 111, 131, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.747rem;
            margin-top: 0.427rem;
          }
        }
      }
    }
    .group_5 {
      width: 37.334rem;
      height: 7.52rem;
      background: url(./img/SketchPng6afabe73a74054cbf3e3261c24c0b7e28382e7eaa467d5891c4d0c4ebb41d83f.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 0.64rem 0 0 6.934rem;
      .block_4 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/c3425ffed7714bacb8c7d3144011951a_mergeImage.png);
        background-repeat: no-repeat;
        background-size: cover;
        width: 10.134rem;
        height: 6.24rem;
        margin: 0.64rem 0 0 0.64rem;
      }
      .block_5 {
        width: 25.28rem;
        height: 5.654rem;
        margin: 0.64rem 0.64rem 0 0;
        .box_1 {
          width: 25.28rem;
          height: 2.454rem;
          background: url(./img/SketchPngef5447757b4e9e70bd579431faf2a92e31570e21210f316778221630763837a0.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_3 {
            width: 2.854rem;
            height: 1.707rem;
            margin: 0.374rem 0 0 0.427rem;
            .text_13 {
              width: 2.854rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(17, 26, 52, 1);
              font-size: 1.066rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_14 {
              width: 0.907rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 111, 131, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
              margin: 0.267rem 0 0 0.054rem;
            }
          }
        }
        .text-group_4 {
          width: 25.28rem;
          height: 2.56rem;
          margin-top: 0.64rem;
          .text_15 {
            width: 10.4rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(17, 26, 52, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_16 {
            width: 25.28rem;
            height: 1.494rem;
            overflow-wrap: break-word;
            color: rgba(102, 111, 131, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.747rem;
            margin-top: 0.427rem;
          }
        }
      }
    }
    .group_6 {
      width: 37.334rem;
      height: 7.52rem;
      background: url(./img/SketchPng6afabe73a74054cbf3e3261c24c0b7e28382e7eaa467d5891c4d0c4ebb41d83f.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 0.64rem 0 0 6.934rem;
      .block_6 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/4f78fbb6fcc4461287287b627387faf3_mergeImage.png);
        width: 10.134rem;
        height: 6.24rem;
        margin: 0.64rem 0 0 0.64rem;
      }
      .block_7 {
        width: 25.28rem;
        height: 5.654rem;
        margin: 0.64rem 0.64rem 0 0;
        .group_7 {
          width: 25.28rem;
          height: 2.454rem;
          background: url(./img/SketchPngef5447757b4e9e70bd579431faf2a92e31570e21210f316778221630763837a0.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_5 {
            width: 2.854rem;
            height: 1.707rem;
            margin: 0.374rem 0 0 0.427rem;
            .text_17 {
              width: 2.854rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(17, 26, 52, 1);
              font-size: 1.066rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_18 {
              width: 0.907rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 111, 131, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
              margin: 0.267rem 0 0 0.054rem;
            }
          }
        }
        .text-group_6 {
          width: 25.28rem;
          height: 2.56rem;
          margin-top: 0.64rem;
          .text_19 {
            width: 10.4rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(17, 26, 52, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_20 {
            width: 25.28rem;
            height: 1.494rem;
            overflow-wrap: break-word;
            color: rgba(102, 111, 131, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.747rem;
            margin-top: 0.427rem;
          }
        }
      }
    }
    .group_8 {
      width: 37.334rem;
      height: 7.52rem;
      background: url(./img/SketchPng6afabe73a74054cbf3e3261c24c0b7e28382e7eaa467d5891c4d0c4ebb41d83f.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 0.64rem 0 0 6.934rem;
      .group_9 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/b06d316b73f64dfc900ac99d4c985c3f_mergeImage.png);
        width: 10.134rem;
        height: 6.24rem;
        margin: 0.64rem 0 0 0.64rem;
      }
      .group_10 {
        width: 25.28rem;
        height: 5.654rem;
        margin: 0.64rem 0.64rem 0 0;
        .group_11 {
          width: 25.28rem;
          height: 2.454rem;
          background: url(./img/SketchPngef5447757b4e9e70bd579431faf2a92e31570e21210f316778221630763837a0.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_7 {
            width: 2.854rem;
            height: 1.707rem;
            margin: 0.374rem 0 0 0.427rem;
            .text_21 {
              width: 2.854rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(17, 26, 52, 1);
              font-size: 1.066rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_22 {
              width: 0.907rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 111, 131, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
              margin: 0.267rem 0 0 0.054rem;
            }
          }
        }
        .text-group_8 {
          width: 25.28rem;
          height: 2.56rem;
          margin-top: 0.64rem;
          .text_23 {
            width: 10.4rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(17, 26, 52, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_24 {
            width: 25.28rem;
            height: 1.494rem;
            overflow-wrap: break-word;
            color: rgba(102, 111, 131, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.747rem;
            margin-top: 0.427rem;
          }
        }
      }
    }
    .group_12 {
      width: 37.334rem;
      height: 7.52rem;
      background: url(./img/SketchPng6afabe73a74054cbf3e3261c24c0b7e28382e7eaa467d5891c4d0c4ebb41d83f.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 0.64rem 0 0 6.934rem;
      .box_2 {
        border-radius: 8px;
        background-image: url(/pages/assets/img/d8274b446493442f85defa533698f188_mergeImage.png);
        width: 10.134rem;
        height: 6.24rem;
        margin: 0.64rem 0 0 0.64rem;
      }
      .box_3 {
        width: 25.28rem;
        height: 5.654rem;
        margin: 0.64rem 0.64rem 0 0;
        .box_4 {
          width: 25.28rem;
          height: 2.454rem;
          background: url(./img/SketchPngef5447757b4e9e70bd579431faf2a92e31570e21210f316778221630763837a0.png)
            100% no-repeat;
          background-size: 100% 100%;
          .text-group_9 {
            width: 2.854rem;
            height: 1.707rem;
            margin: 0.374rem 0 0 0.427rem;
            .text_25 {
              width: 2.854rem;
              height: 1.067rem;
              overflow-wrap: break-word;
              color: rgba(17, 26, 52, 1);
              font-size: 1.066rem;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 1.067rem;
            }
            .text_26 {
              width: 0.907rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 111, 131, 1);
              font-size: 0.373rem;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.374rem;
              margin: 0.267rem 0 0 0.054rem;
            }
          }
        }
        .text-group_10 {
          width: 25.28rem;
          height: 2.56rem;
          margin-top: 0.64rem;
          .text_27 {
            width: 10.4rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(17, 26, 52, 1);
            font-size: 0.64rem;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_28 {
            width: 25.28rem;
            height: 1.494rem;
            overflow-wrap: break-word;
            color: rgba(102, 111, 131, 1);
            font-size: 0.426rem;
            font-weight: NaN;
            text-align: justify;
            line-height: 0.747rem;
            margin-top: 0.427rem;
          }
        }
      }
    }
    .group_13 {
      width: 22.854rem;
      height: 1.067rem;
      margin: 2.134rem 0 2.694rem 14.187rem;
      .text-wrapper_2 {
        background-color: rgba(249, 250, 251, 1);
        border-radius: 19px;
        height: 1.067rem;
        width: 2.134rem;
        .text_29 {
          width: 0.854rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.426rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.587rem;
          margin: 0.24rem 0 0 0.64rem;
        }
      }
      .label_1 {
        width: 0.64rem;
        height: 0.64rem;
        margin: 0.214rem 0 0 0.854rem;
      }
      .text-wrapper_3 {
        background-color: rgba(33, 135, 250, 1);
        border-radius: 50%;
        height: 1.067rem;
        margin-left: 0.534rem;
        width: 1.067rem;
        .text_30 {
          width: 0.214rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.426rem;
          font-family: PingFangSC-Semibold;
          font-weight: 600;
          text-align: right;
          white-space: nowrap;
          line-height: 0.587rem;
          margin: 0.24rem 0 0 0.427rem;
        }
      }
      .text_31 {
        width: 0.267rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-weight: NaN;
        text-align: right;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.24rem 0 0 0.694rem;
      }
      .text_32 {
        width: 0.267rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-weight: NaN;
        text-align: right;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.24rem 0 0 1.12rem;
      }
      .text_33 {
        width: 0.267rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-weight: NaN;
        text-align: right;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.24rem 0 0 1.12rem;
      }
      .text_34 {
        width: 0.267rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-weight: NaN;
        text-align: right;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.24rem 0 0 1.12rem;
      }
      .text_35 {
        width: 0.854rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.24rem 0 0 1.067rem;
      }
      .text-wrapper_4 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 4px;
        height: 1.067rem;
        border: 1px solid rgba(223, 225, 232, 1);
        margin-left: 0.107rem;
        width: 1.6rem;
        .text_36 {
          width: 0.24rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.426rem;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          text-align: right;
          white-space: nowrap;
          line-height: 0.587rem;
          margin: 0.24rem 0 0 0.694rem;
        }
      }
      .text_37 {
        width: 0.427rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.24rem 0 0 0.107rem;
      }
      .text-wrapper_5 {
        background-color: rgba(33, 135, 250, 1);
        border-radius: 4px;
        height: 1.067rem;
        margin-left: 0.64rem;
        width: 1.6rem;
        .text_38 {
          width: 0.854rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.426rem;
          font-weight: NaN;
          text-align: right;
          white-space: nowrap;
          line-height: 0.587rem;
          margin: 0.24rem 0 0 0.374rem;
        }
      }
      .text_39 {
        width: 1.627rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.24rem 0 0 0.32rem;
      }
      .label_2 {
        width: 0.64rem;
        height: 0.64rem;
        margin: 0.214rem 0 0 0.534rem;
      }
      .text-wrapper_6 {
        background-color: rgba(249, 250, 251, 1);
        border-radius: 19px;
        height: 1.067rem;
        margin-left: 0.854rem;
        width: 2.134rem;
        .text_40 {
          width: 0.854rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 0.426rem;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.587rem;
          margin: 0.24rem 0 0 0.64rem;
        }
      }
    }
  }
}
