<template>
  <div class="admin-page">
    <div class="header">
      <h1>代理申请管理</h1>
      <div class="stats">
        <div class="stat-item">
          <span class="label">总申请数</span>
          <span class="value">{{ stats.total }}</span>
        </div>
        <div class="stat-item">
          <span class="label">待处理</span>
          <span class="value pending">{{ stats.pending }}</span>
        </div>
        <div class="stat-item">
          <span class="label">已处理</span>
          <span class="value processed">{{ stats.processed }}</span>
        </div>
        <div class="stat-item">
          <span class="label">已拒绝</span>
          <span class="value rejected">{{ stats.rejected }}</span>
        </div>
      </div>
    </div>

    <div class="filters">
      <select v-model="currentStatus" @change="loadData">
        <option value="all">全部状态</option>
        <option value="pending">待处理</option>
        <option value="processed">已处理</option>
        <option value="rejected">已拒绝</option>
      </select>
      <button @click="loadData" class="refresh-btn">刷新</button>
    </div>

    <div class="table-container">
      <table class="submissions-table">
        <thead>
          <tr>
            <th>提交时间</th>
            <th>公司名称</th>
            <th>联系人</th>
            <th>联系方式</th>
            <th>所在地区</th>
            <th>所属行业</th>
            <th>状态</th>
            <th>IP地址</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in submissions" :key="item.id">
            <td>{{ formatDate(item.submitTime) }}</td>
            <td>{{ item.companyName }}</td>
            <td>{{ item.contactName }}</td>
            <td>{{ item.contactPhone }}</td>
            <td>{{ item.location }}</td>
            <td>{{ item.industry }}</td>
            <td>
              <span :class="['status', item.status]">
                {{ getStatusText(item.status) }}
              </span>
            </td>
            <td>{{ item.ip }}</td>
            <td>
              <button @click="viewDetails(item)" class="action-btn view">查看</button>
              <button @click="updateStatus(item, 'processed')" class="action-btn process" v-if="item.status === 'pending'">
                处理
              </button>
              <button @click="updateStatus(item, 'rejected')" class="action-btn reject" v-if="item.status === 'pending'">
                拒绝
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="pagination" v-if="pagination.totalPages > 1">
      <button
        @click="changePage(pagination.page - 1)"
        :disabled="pagination.page <= 1"
        class="page-btn"
      >
        上一页
      </button>
      <span class="page-info">
        第 {{ pagination.page }} 页 / 共 {{ pagination.totalPages }} 页
      </span>
      <button
        @click="changePage(pagination.page + 1)"
        :disabled="pagination.page >= pagination.totalPages"
        class="page-btn"
      >
        下一页
      </button>
    </div>

    <!-- 详情弹窗 -->
    <div v-if="selectedItem" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>申请详情</h3>
          <button @click="closeModal" class="close-btn">&times;</button>
        </div>
        <div class="modal-body">
          <div class="detail-row">
            <label>公司名称：</label>
            <span>{{ selectedItem.companyName }}</span>
          </div>
          <div class="detail-row">
            <label>联系人：</label>
            <span>{{ selectedItem.contactName }}</span>
          </div>
          <div class="detail-row">
            <label>联系方式：</label>
            <span>{{ selectedItem.contactPhone }}</span>
          </div>
          <div class="detail-row">
            <label>所在地区：</label>
            <span>{{ selectedItem.location }}</span>
          </div>
          <div class="detail-row">
            <label>所属行业：</label>
            <span>{{ selectedItem.industry }}</span>
          </div>
          <div class="detail-row">
            <label>提交时间：</label>
            <span>{{ formatDate(selectedItem.submitTime) }}</span>
          </div>
          <div class="detail-row">
            <label>来源：</label>
            <span>{{ selectedItem.source }}</span>
          </div>
          <div class="detail-row">
            <label>IP地址：</label>
            <span>{{ selectedItem.ip }}</span>
          </div>
          <div class="detail-row">
            <label>用户代理：</label>
            <span>{{ selectedItem.userAgent }}</span>
          </div>
          <div class="detail-row">
            <label>状态：</label>
            <span :class="['status', selectedItem.status]">
              {{ getStatusText(selectedItem.status) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const submissions = ref([])
const stats = ref({
  total: 0,
  pending: 0,
  processed: 0,
  rejected: 0
})
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
})
const currentStatus = ref('all')
const selectedItem = ref(null)
const loading = ref(false)

// 页面加载时获取数据
onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    const response = await $fetch('/api/agency/list', {
      query: {
        page: pagination.value.page,
        limit: pagination.value.limit,
        status: currentStatus.value
      }
    })

    if (response.success) {
      submissions.value = response.data.list
      stats.value = response.data.stats
      pagination.value = response.data.pagination
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    alert('加载数据失败，请刷新重试')
  } finally {
    loading.value = false
  }
}

// 翻页
const changePage = (page) => {
  pagination.value.page = page
  loadData()
}

// 查看详情
const viewDetails = (item) => {
  selectedItem.value = item
}

// 关闭弹窗
const closeModal = () => {
  selectedItem.value = null
}

// 更新状态
const updateStatus = async (item, newStatus) => {
  if (!confirm(`确定要${newStatus === 'processed' ? '处理' : '拒绝'}这个申请吗？`)) {
    return
  }

  try {
    // 这里可以调用更新状态的API
    // const response = await $fetch('/api/agency/update-status', {
    //   method: 'POST',
    //   body: { id: item.id, status: newStatus }
    // })

    // 临时更新本地状态
    item.status = newStatus
    alert(`状态更新成功！`)
    loadData() // 重新加载数据
  } catch (error) {
    console.error('更新状态失败:', error)
    alert('更新状态失败，请重试')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processed: '已处理',
    rejected: '已拒绝'
  }
  return statusMap[status] || status
}

// 页面头部信息
useHead({
  title: '代理申请管理 - 慧医云科技',
  meta: [
    { name: 'robots', content: 'noindex,nofollow' }
  ]
})
</script>

<style scoped>
.admin-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  margin-bottom: 30px;
}

.header h1 {
  margin: 0 0 20px 0;
  color: #333;
}

.stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-item {
  background: #f5f5f5;
  padding: 15px 20px;
  border-radius: 8px;
  min-width: 120px;
}

.stat-item .label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.stat-item .value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-item .value.pending {
  color: #ff9500;
}

.stat-item .value.processed {
  color: #34c759;
}

.stat-item .value.rejected {
  color: #ff3b30;
}

.filters {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.filters select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.refresh-btn {
  padding: 8px 16px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.refresh-btn:hover {
  background: #0056b3;
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #ddd;
  margin-bottom: 20px;
}

.submissions-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.submissions-table th,
.submissions-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.submissions-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.submissions-table tr:hover {
  background: #f8f9fa;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status.pending {
  background: #fff3cd;
  color: #856404;
}

.status.processed {
  background: #d4edda;
  color: #155724;
}

.status.rejected {
  background: #f8d7da;
  color: #721c24;
}

.action-btn {
  padding: 4px 8px;
  margin: 0 2px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.action-btn.view {
  background: #6c757d;
  color: white;
}

.action-btn.process {
  background: #28a745;
  color: white;
}

.action-btn.reject {
  background: #dc3545;
  color: white;
}

.action-btn:hover {
  opacity: 0.8;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.page-btn {
  padding: 8px 16px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.page-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.page-info {
  color: #666;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.detail-row {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
}

.detail-row label {
  min-width: 100px;
  font-weight: 600;
  color: #333;
}

.detail-row span {
  flex: 1;
  word-break: break-all;
}
</style>
