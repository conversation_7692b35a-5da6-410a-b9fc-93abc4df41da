// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 6/26/2025, 9:29:31 AM
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
import cfg3 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["/Users/<USER>/Documents/xplan_gitlab/hyy-website/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/plugins/**/*.{js,ts,mjs}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/composables/**/*.{js,ts,mjs}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/utils/**/*.{js,ts,mjs}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Documents/xplan_gitlab/hyy-website/app.config.{js,ts,mjs}"]}},
{},
cfg2,
cfg3
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;