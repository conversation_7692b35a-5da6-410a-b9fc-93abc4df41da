// Generated by auto imports
export {}
declare global {
  const ElIconAddLocation: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['AddLocation']
  const ElIconAim: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Aim']
  const ElIconAlarmClock: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['AlarmClock']
  const ElIconApple: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Apple']
  const ElIconArrowDown: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowDown']
  const ElIconArrowDownBold: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowDownBold']
  const ElIconArrowLeft: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowLeft']
  const ElIconArrowLeftBold: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowLeftBold']
  const ElIconArrowRight: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowRight']
  const ElIconArrowRightBold: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowRightBold']
  const ElIconArrowUp: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowUp']
  const ElIconArrowUpBold: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowUpBold']
  const ElIconAvatar: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Avatar']
  const ElIconBack: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Back']
  const ElIconBaseball: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Baseball']
  const ElIconBasketball: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Basketball']
  const ElIconBell: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Bell']
  const ElIconBellFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['BellFilled']
  const ElIconBicycle: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Bicycle']
  const ElIconBottom: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Bottom']
  const ElIconBottomLeft: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['BottomLeft']
  const ElIconBottomRight: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['BottomRight']
  const ElIconBowl: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Bowl']
  const ElIconBox: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Box']
  const ElIconBriefcase: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Briefcase']
  const ElIconBrush: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Brush']
  const ElIconBrushFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['BrushFilled']
  const ElIconBurger: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Burger']
  const ElIconCalendar: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Calendar']
  const ElIconCamera: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Camera']
  const ElIconCameraFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CameraFilled']
  const ElIconCaretBottom: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CaretBottom']
  const ElIconCaretLeft: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CaretLeft']
  const ElIconCaretRight: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CaretRight']
  const ElIconCaretTop: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CaretTop']
  const ElIconCellphone: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Cellphone']
  const ElIconChatDotRound: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatDotRound']
  const ElIconChatDotSquare: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatDotSquare']
  const ElIconChatLineRound: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatLineRound']
  const ElIconChatLineSquare: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatLineSquare']
  const ElIconChatRound: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatRound']
  const ElIconChatSquare: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatSquare']
  const ElIconCheck: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Check']
  const ElIconChecked: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Checked']
  const ElIconCherry: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Cherry']
  const ElIconChicken: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Chicken']
  const ElIconChromeFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChromeFilled']
  const ElIconCircleCheck: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CircleCheck']
  const ElIconCircleCheckFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CircleCheckFilled']
  const ElIconCircleClose: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CircleClose']
  const ElIconCircleCloseFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CircleCloseFilled']
  const ElIconCirclePlus: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CirclePlus']
  const ElIconCirclePlusFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CirclePlusFilled']
  const ElIconClock: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Clock']
  const ElIconClose: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Close']
  const ElIconCloseBold: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CloseBold']
  const ElIconCloudy: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Cloudy']
  const ElIconCoffee: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Coffee']
  const ElIconCoffeeCup: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CoffeeCup']
  const ElIconCoin: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Coin']
  const ElIconColdDrink: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ColdDrink']
  const ElIconCollection: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Collection']
  const ElIconCollectionTag: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CollectionTag']
  const ElIconComment: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Comment']
  const ElIconCompass: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Compass']
  const ElIconConnection: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Connection']
  const ElIconCoordinate: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Coordinate']
  const ElIconCopyDocument: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CopyDocument']
  const ElIconCpu: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Cpu']
  const ElIconCreditCard: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CreditCard']
  const ElIconCrop: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Crop']
  const ElIconDArrowLeft: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DArrowLeft']
  const ElIconDArrowRight: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DArrowRight']
  const ElIconDCaret: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DCaret']
  const ElIconDataAnalysis: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DataAnalysis']
  const ElIconDataBoard: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DataBoard']
  const ElIconDataLine: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DataLine']
  const ElIconDelete: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Delete']
  const ElIconDeleteFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DeleteFilled']
  const ElIconDeleteLocation: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DeleteLocation']
  const ElIconDessert: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Dessert']
  const ElIconDiscount: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Discount']
  const ElIconDish: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Dish']
  const ElIconDishDot: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DishDot']
  const ElIconDocument: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Document']
  const ElIconDocumentAdd: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentAdd']
  const ElIconDocumentChecked: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentChecked']
  const ElIconDocumentCopy: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentCopy']
  const ElIconDocumentDelete: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentDelete']
  const ElIconDocumentRemove: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentRemove']
  const ElIconDownload: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Download']
  const ElIconDrizzling: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Drizzling']
  const ElIconEdit: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Edit']
  const ElIconEditPen: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['EditPen']
  const ElIconEleme: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Eleme']
  const ElIconElemeFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ElemeFilled']
  const ElIconElementPlus: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ElementPlus']
  const ElIconExpand: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Expand']
  const ElIconFailed: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Failed']
  const ElIconFemale: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Female']
  const ElIconFiles: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Files']
  const ElIconFilm: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Film']
  const ElIconFilter: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Filter']
  const ElIconFinished: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Finished']
  const ElIconFirstAidKit: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FirstAidKit']
  const ElIconFlag: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Flag']
  const ElIconFold: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Fold']
  const ElIconFolder: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Folder']
  const ElIconFolderAdd: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderAdd']
  const ElIconFolderChecked: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderChecked']
  const ElIconFolderDelete: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderDelete']
  const ElIconFolderOpened: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderOpened']
  const ElIconFolderRemove: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderRemove']
  const ElIconFood: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Food']
  const ElIconFootball: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Football']
  const ElIconForkSpoon: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ForkSpoon']
  const ElIconFries: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Fries']
  const ElIconFullScreen: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FullScreen']
  const ElIconGoblet: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Goblet']
  const ElIconGobletFull: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GobletFull']
  const ElIconGobletSquare: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GobletSquare']
  const ElIconGobletSquareFull: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GobletSquareFull']
  const ElIconGoldMedal: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GoldMedal']
  const ElIconGoods: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Goods']
  const ElIconGoodsFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GoodsFilled']
  const ElIconGrape: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Grape']
  const ElIconGrid: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Grid']
  const ElIconGuide: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Guide']
  const ElIconHandbag: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Handbag']
  const ElIconHeadset: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Headset']
  const ElIconHelp: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Help']
  const ElIconHelpFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['HelpFilled']
  const ElIconHide: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Hide']
  const ElIconHistogram: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Histogram']
  const ElIconHomeFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['HomeFilled']
  const ElIconHotWater: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['HotWater']
  const ElIconHouse: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['House']
  const ElIconIceCream: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceCream']
  const ElIconIceCreamRound: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceCreamRound']
  const ElIconIceCreamSquare: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceCreamSquare']
  const ElIconIceDrink: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceDrink']
  const ElIconIceTea: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceTea']
  const ElIconInfoFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['InfoFilled']
  const ElIconIphone: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Iphone']
  const ElIconKey: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Key']
  const ElIconKnifeFork: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['KnifeFork']
  const ElIconLightning: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Lightning']
  const ElIconLink: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Link']
  const ElIconList: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['List']
  const ElIconLoading: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Loading']
  const ElIconLocation: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Location']
  const ElIconLocationFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['LocationFilled']
  const ElIconLocationInformation: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['LocationInformation']
  const ElIconLock: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Lock']
  const ElIconLollipop: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Lollipop']
  const ElIconMagicStick: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MagicStick']
  const ElIconMagnet: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Magnet']
  const ElIconMale: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Male']
  const ElIconManagement: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Management']
  const ElIconMapLocation: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MapLocation']
  const ElIconMedal: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Medal']
  const ElIconMemo: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Memo']
  const ElIconMenu: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Menu']
  const ElIconMessage: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Message']
  const ElIconMessageBox: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MessageBox']
  const ElIconMic: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Mic']
  const ElIconMicrophone: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Microphone']
  const ElIconMilkTea: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MilkTea']
  const ElIconMinus: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Minus']
  const ElIconMoney: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Money']
  const ElIconMonitor: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Monitor']
  const ElIconMoon: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Moon']
  const ElIconMoonNight: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MoonNight']
  const ElIconMore: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['More']
  const ElIconMoreFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MoreFilled']
  const ElIconMostlyCloudy: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MostlyCloudy']
  const ElIconMouse: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Mouse']
  const ElIconMug: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Mug']
  const ElIconMute: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Mute']
  const ElIconMuteNotification: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MuteNotification']
  const ElIconNoSmoking: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['NoSmoking']
  const ElIconNotebook: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Notebook']
  const ElIconNotification: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Notification']
  const ElIconOdometer: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Odometer']
  const ElIconOfficeBuilding: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['OfficeBuilding']
  const ElIconOpen: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Open']
  const ElIconOperation: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Operation']
  const ElIconOpportunity: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Opportunity']
  const ElIconOrange: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Orange']
  const ElIconPaperclip: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Paperclip']
  const ElIconPartlyCloudy: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PartlyCloudy']
  const ElIconPear: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Pear']
  const ElIconPhone: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Phone']
  const ElIconPhoneFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PhoneFilled']
  const ElIconPicture: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Picture']
  const ElIconPictureFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PictureFilled']
  const ElIconPictureRounded: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PictureRounded']
  const ElIconPieChart: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PieChart']
  const ElIconPlace: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Place']
  const ElIconPlatform: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Platform']
  const ElIconPlus: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Plus']
  const ElIconPointer: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Pointer']
  const ElIconPosition: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Position']
  const ElIconPostcard: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Postcard']
  const ElIconPouring: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Pouring']
  const ElIconPresent: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Present']
  const ElIconPriceTag: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PriceTag']
  const ElIconPrinter: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Printer']
  const ElIconPromotion: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Promotion']
  const ElIconQuartzWatch: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['QuartzWatch']
  const ElIconQuestionFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['QuestionFilled']
  const ElIconRank: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Rank']
  const ElIconReading: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Reading']
  const ElIconReadingLamp: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ReadingLamp']
  const ElIconRefresh: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Refresh']
  const ElIconRefreshLeft: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['RefreshLeft']
  const ElIconRefreshRight: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['RefreshRight']
  const ElIconRefrigerator: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Refrigerator']
  const ElIconRemove: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Remove']
  const ElIconRemoveFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['RemoveFilled']
  const ElIconRight: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Right']
  const ElIconScaleToOriginal: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ScaleToOriginal']
  const ElIconSchool: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['School']
  const ElIconScissor: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Scissor']
  const ElIconSearch: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Search']
  const ElIconSelect: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Select']
  const ElIconSell: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sell']
  const ElIconSemiSelect: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SemiSelect']
  const ElIconService: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Service']
  const ElIconSetUp: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SetUp']
  const ElIconSetting: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Setting']
  const ElIconShare: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Share']
  const ElIconShip: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Ship']
  const ElIconShop: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Shop']
  const ElIconShoppingBag: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ShoppingBag']
  const ElIconShoppingCart: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ShoppingCart']
  const ElIconShoppingCartFull: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ShoppingCartFull']
  const ElIconShoppingTrolley: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ShoppingTrolley']
  const ElIconSmoking: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Smoking']
  const ElIconSoccer: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Soccer']
  const ElIconSoldOut: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SoldOut']
  const ElIconSort: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sort']
  const ElIconSortDown: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SortDown']
  const ElIconSortUp: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SortUp']
  const ElIconStamp: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Stamp']
  const ElIconStar: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Star']
  const ElIconStarFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['StarFilled']
  const ElIconStopwatch: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Stopwatch']
  const ElIconSuccessFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SuccessFilled']
  const ElIconSugar: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sugar']
  const ElIconSuitcase: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Suitcase']
  const ElIconSuitcaseLine: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SuitcaseLine']
  const ElIconSunny: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sunny']
  const ElIconSunrise: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sunrise']
  const ElIconSunset: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sunset']
  const ElIconSwitch: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Switch']
  const ElIconSwitchButton: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SwitchButton']
  const ElIconSwitchFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SwitchFilled']
  const ElIconTakeawayBox: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TakeawayBox']
  const ElIconTicket: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Ticket']
  const ElIconTickets: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Tickets']
  const ElIconTimer: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Timer']
  const ElIconToiletPaper: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ToiletPaper']
  const ElIconTools: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Tools']
  const ElIconTop: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Top']
  const ElIconTopLeft: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TopLeft']
  const ElIconTopRight: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TopRight']
  const ElIconTrendCharts: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TrendCharts']
  const ElIconTrophy: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Trophy']
  const ElIconTrophyBase: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TrophyBase']
  const ElIconTurnOff: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TurnOff']
  const ElIconUmbrella: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Umbrella']
  const ElIconUnlock: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Unlock']
  const ElIconUpload: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Upload']
  const ElIconUploadFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['UploadFilled']
  const ElIconUser: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['User']
  const ElIconUserFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['UserFilled']
  const ElIconVan: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Van']
  const ElIconVideoCamera: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['VideoCamera']
  const ElIconVideoCameraFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['VideoCameraFilled']
  const ElIconVideoPause: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['VideoPause']
  const ElIconVideoPlay: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['VideoPlay']
  const ElIconView: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['View']
  const ElIconWallet: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Wallet']
  const ElIconWalletFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['WalletFilled']
  const ElIconWarnTriangleFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['WarnTriangleFilled']
  const ElIconWarning: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Warning']
  const ElIconWarningFilled: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['WarningFilled']
  const ElIconWatch: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Watch']
  const ElIconWatermelon: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Watermelon']
  const ElIconWindPower: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['WindPower']
  const ElIconZoomIn: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ZoomIn']
  const ElIconZoomOut: typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ZoomOut']
  const ElLoading: typeof import('../../node_modules/element-plus/es/components/loading/index')['ElLoading']
  const ElMessage: typeof import('../../node_modules/element-plus/es/components/message/index')['ElMessage']
  const ElMessageBox: typeof import('../../node_modules/element-plus/es/components/message-box/index')['ElMessageBox']
  const ElNotification: typeof import('../../node_modules/element-plus/es/components/notification/index')['ElNotification']
  const ID_INJECTION_KEY: typeof import('../../node_modules/element-plus/es/hooks/use-id/index')['ID_INJECTION_KEY']
  const ZINDEX_INJECTION_KEY: typeof import('../../node_modules/element-plus/es/hooks/use-z-index/index')['ZINDEX_INJECTION_KEY']
  const abortNavigation: typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']
  const acceptHMRUpdate: typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['acceptHMRUpdate']
  const addRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']
  const callOnce: typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']
  const cancelIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']
  const clearError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']
  const clearNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']
  const clearNuxtState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']
  const computed: typeof import('../../node_modules/vue')['computed']
  const createError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']
  const createSitePathResolver: typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/utils')['createSitePathResolver']
  const customRef: typeof import('../../node_modules/vue')['customRef']
  const defineAppConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']
  const defineAsyncComponent: typeof import('../../node_modules/vue')['defineAsyncComponent']
  const defineComponent: typeof import('../../node_modules/vue')['defineComponent']
  const defineNuxtComponent: typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']
  const definePageMeta: typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']
  const defineStore: typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['defineStore']
  const effect: typeof import('../../node_modules/vue')['effect']
  const effectScope: typeof import('../../node_modules/vue')['effectScope']
  const getAppManifest: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']
  const getCurrentInstance: typeof import('../../node_modules/vue')['getCurrentInstance']
  const getCurrentScope: typeof import('../../node_modules/vue')['getCurrentScope']
  const getRouteRules: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']
  const h: typeof import('../../node_modules/vue')['h']
  const hasInjectionContext: typeof import('../../node_modules/vue')['hasInjectionContext']
  const inject: typeof import('../../node_modules/vue')['inject']
  const injectHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['injectHead']
  const isNuxtError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']
  const isPrerendered: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']
  const isProxy: typeof import('../../node_modules/vue')['isProxy']
  const isReactive: typeof import('../../node_modules/vue')['isReactive']
  const isReadonly: typeof import('../../node_modules/vue')['isReadonly']
  const isRef: typeof import('../../node_modules/vue')['isRef']
  const isShallow: typeof import('../../node_modules/vue')['isShallow']
  const isVue2: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const loadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']
  const markRaw: typeof import('../../node_modules/vue')['markRaw']
  const mergeModels: typeof import('../../node_modules/vue')['mergeModels']
  const navigateTo: typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']
  const nextTick: typeof import('../../node_modules/vue')['nextTick']
  const onActivated: typeof import('../../node_modules/vue')['onActivated']
  const onBeforeMount: typeof import('../../node_modules/vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('../../node_modules/vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('../../node_modules/vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('../../node_modules/vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('../../node_modules/vue')['onBeforeUpdate']
  const onDeactivated: typeof import('../../node_modules/vue')['onDeactivated']
  const onErrorCaptured: typeof import('../../node_modules/vue')['onErrorCaptured']
  const onMounted: typeof import('../../node_modules/vue')['onMounted']
  const onNuxtReady: typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']
  const onPrehydrate: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']
  const onRenderTracked: typeof import('../../node_modules/vue')['onRenderTracked']
  const onRenderTriggered: typeof import('../../node_modules/vue')['onRenderTriggered']
  const onScopeDispose: typeof import('../../node_modules/vue')['onScopeDispose']
  const onServerPrefetch: typeof import('../../node_modules/vue')['onServerPrefetch']
  const onUnmounted: typeof import('../../node_modules/vue')['onUnmounted']
  const onUpdated: typeof import('../../node_modules/vue')['onUpdated']
  const prefetchComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']
  const preloadComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']
  const preloadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']
  const preloadRouteComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']
  const prerenderRoutes: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']
  const provide: typeof import('../../node_modules/vue')['provide']
  const provideGlobalConfig: typeof import('../../node_modules/element-plus/es/components/config-provider/src/hooks/use-global-config')['provideGlobalConfig']
  const proxyRefs: typeof import('../../node_modules/vue')['proxyRefs']
  const reactive: typeof import('../../node_modules/vue')['reactive']
  const readonly: typeof import('../../node_modules/vue')['readonly']
  const ref: typeof import('../../node_modules/vue')['ref']
  const refreshCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']
  const refreshNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']
  const reloadNuxtApp: typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']
  const requestIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']
  const resolveComponent: typeof import('../../node_modules/vue')['resolveComponent']
  const setInterval: typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']
  const setPageLayout: typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']
  const setResponseStatus: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']
  const shallowReactive: typeof import('../../node_modules/vue')['shallowReactive']
  const shallowReadonly: typeof import('../../node_modules/vue')['shallowReadonly']
  const shallowRef: typeof import('../../node_modules/vue')['shallowRef']
  const showError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']
  const storeToRefs: typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['storeToRefs']
  const toRaw: typeof import('../../node_modules/vue')['toRaw']
  const toRef: typeof import('../../node_modules/vue')['toRef']
  const toRefs: typeof import('../../node_modules/vue')['toRefs']
  const toValue: typeof import('../../node_modules/vue')['toValue']
  const triggerRef: typeof import('../../node_modules/vue')['triggerRef']
  const tryUseNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']
  const unref: typeof import('../../node_modules/vue')['unref']
  const updateAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']
  const updateSiteConfig: typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/updateSiteConfig')['updateSiteConfig']
  const useAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']
  const useAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']
  const useAttrs: typeof import('../../node_modules/vue')['useAttrs']
  const useCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']
  const useCssModule: typeof import('../../node_modules/vue')['useCssModule']
  const useCssVars: typeof import('../../node_modules/vue')['useCssVars']
  const useError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']
  const useFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']
  const useHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHead']
  const useHeadSafe: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHeadSafe']
  const useHydration: typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']
  const useId: typeof import('../../node_modules/vue')['useId']
  const useLazyAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']
  const useLink: typeof import('../../node_modules/vue-router')['useLink']
  const useLoadingIndicator: typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']
  const useModel: typeof import('../../node_modules/vue')['useModel']
  const useNitroOrigin: typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/useNitroOrigin')['useNitroOrigin']
  const useNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']
  const useNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']
  const usePinia: typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']
  const usePreviewMode: typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']
  const useRequestEvent: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']
  const useRequestFetch: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']
  const useRequestHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']
  const useRequestHeaders: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']
  const useRequestURL: typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']
  const useResponseHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']
  const useRoute: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']
  const useRouteAnnouncer: typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']
  const useRouter: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']
  const useRuntimeConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']
  const useRuntimeHook: typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']
  const useScript: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']
  const useScriptClarity: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']
  const useScriptCloudflareWebAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']
  const useScriptCrisp: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']
  const useScriptEventPage: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']
  const useScriptFathomAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']
  const useScriptGoogleAdsense: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']
  const useScriptGoogleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']
  const useScriptGoogleMaps: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']
  const useScriptGoogleTagManager: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']
  const useScriptHotjar: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']
  const useScriptIntercom: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']
  const useScriptLemonSqueezy: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']
  const useScriptMatomoAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']
  const useScriptMetaPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']
  const useScriptNpm: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']
  const useScriptPlausibleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']
  const useScriptRybbitAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptRybbitAnalytics']
  const useScriptSegment: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']
  const useScriptSnapchatPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']
  const useScriptStripe: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']
  const useScriptTriggerConsent: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']
  const useScriptTriggerElement: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']
  const useScriptUmamiAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']
  const useScriptVimeoPlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']
  const useScriptXPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']
  const useScriptYouTubePlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']
  const useSeoMeta: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useSeoMeta']
  const useServerHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHead']
  const useServerHeadSafe: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']
  const useShadowRoot: typeof import('../../node_modules/vue')['useShadowRoot']
  const useSiteConfig: typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/useSiteConfig')['useSiteConfig']
  const useSlots: typeof import('../../node_modules/vue')['useSlots']
  const useState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']
  const useTemplateRef: typeof import('../../node_modules/vue')['useTemplateRef']
  const useTransitionState: typeof import('../../node_modules/vue')['useTransitionState']
  const watch: typeof import('../../node_modules/vue')['watch']
  const watchEffect: typeof import('../../node_modules/vue')['watchEffect']
  const watchPostEffect: typeof import('../../node_modules/vue')['watchPostEffect']
  const watchSyncEffect: typeof import('../../node_modules/vue')['watchSyncEffect']
  const withCtx: typeof import('../../node_modules/vue')['withCtx']
  const withDirectives: typeof import('../../node_modules/vue')['withDirectives']
  const withKeys: typeof import('../../node_modules/vue')['withKeys']
  const withMemo: typeof import('../../node_modules/vue')['withMemo']
  const withModifiers: typeof import('../../node_modules/vue')['withModifiers']
  const withScopeId: typeof import('../../node_modules/vue')['withScopeId']
  const withSiteTrailingSlash: typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/utils')['withSiteTrailingSlash']
  const withSiteUrl: typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/utils')['withSiteUrl']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from '../../node_modules/vue'
  import('../../node_modules/vue')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly ElIconAddLocation: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['AddLocation']>
    readonly ElIconAim: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Aim']>
    readonly ElIconAlarmClock: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['AlarmClock']>
    readonly ElIconApple: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Apple']>
    readonly ElIconArrowDown: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowDown']>
    readonly ElIconArrowDownBold: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowDownBold']>
    readonly ElIconArrowLeft: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowLeft']>
    readonly ElIconArrowLeftBold: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowLeftBold']>
    readonly ElIconArrowRight: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowRight']>
    readonly ElIconArrowRightBold: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowRightBold']>
    readonly ElIconArrowUp: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowUp']>
    readonly ElIconArrowUpBold: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ArrowUpBold']>
    readonly ElIconAvatar: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Avatar']>
    readonly ElIconBack: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Back']>
    readonly ElIconBaseball: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Baseball']>
    readonly ElIconBasketball: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Basketball']>
    readonly ElIconBell: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Bell']>
    readonly ElIconBellFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['BellFilled']>
    readonly ElIconBicycle: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Bicycle']>
    readonly ElIconBottom: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Bottom']>
    readonly ElIconBottomLeft: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['BottomLeft']>
    readonly ElIconBottomRight: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['BottomRight']>
    readonly ElIconBowl: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Bowl']>
    readonly ElIconBox: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Box']>
    readonly ElIconBriefcase: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Briefcase']>
    readonly ElIconBrush: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Brush']>
    readonly ElIconBrushFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['BrushFilled']>
    readonly ElIconBurger: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Burger']>
    readonly ElIconCalendar: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Calendar']>
    readonly ElIconCamera: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Camera']>
    readonly ElIconCameraFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CameraFilled']>
    readonly ElIconCaretBottom: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CaretBottom']>
    readonly ElIconCaretLeft: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CaretLeft']>
    readonly ElIconCaretRight: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CaretRight']>
    readonly ElIconCaretTop: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CaretTop']>
    readonly ElIconCellphone: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Cellphone']>
    readonly ElIconChatDotRound: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatDotRound']>
    readonly ElIconChatDotSquare: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatDotSquare']>
    readonly ElIconChatLineRound: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatLineRound']>
    readonly ElIconChatLineSquare: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatLineSquare']>
    readonly ElIconChatRound: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatRound']>
    readonly ElIconChatSquare: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChatSquare']>
    readonly ElIconCheck: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Check']>
    readonly ElIconChecked: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Checked']>
    readonly ElIconCherry: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Cherry']>
    readonly ElIconChicken: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Chicken']>
    readonly ElIconChromeFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ChromeFilled']>
    readonly ElIconCircleCheck: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CircleCheck']>
    readonly ElIconCircleCheckFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CircleCheckFilled']>
    readonly ElIconCircleClose: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CircleClose']>
    readonly ElIconCircleCloseFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CircleCloseFilled']>
    readonly ElIconCirclePlus: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CirclePlus']>
    readonly ElIconCirclePlusFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CirclePlusFilled']>
    readonly ElIconClock: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Clock']>
    readonly ElIconClose: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Close']>
    readonly ElIconCloseBold: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CloseBold']>
    readonly ElIconCloudy: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Cloudy']>
    readonly ElIconCoffee: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Coffee']>
    readonly ElIconCoffeeCup: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CoffeeCup']>
    readonly ElIconCoin: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Coin']>
    readonly ElIconColdDrink: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ColdDrink']>
    readonly ElIconCollection: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Collection']>
    readonly ElIconCollectionTag: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CollectionTag']>
    readonly ElIconComment: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Comment']>
    readonly ElIconCompass: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Compass']>
    readonly ElIconConnection: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Connection']>
    readonly ElIconCoordinate: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Coordinate']>
    readonly ElIconCopyDocument: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CopyDocument']>
    readonly ElIconCpu: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Cpu']>
    readonly ElIconCreditCard: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['CreditCard']>
    readonly ElIconCrop: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Crop']>
    readonly ElIconDArrowLeft: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DArrowLeft']>
    readonly ElIconDArrowRight: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DArrowRight']>
    readonly ElIconDCaret: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DCaret']>
    readonly ElIconDataAnalysis: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DataAnalysis']>
    readonly ElIconDataBoard: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DataBoard']>
    readonly ElIconDataLine: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DataLine']>
    readonly ElIconDelete: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Delete']>
    readonly ElIconDeleteFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DeleteFilled']>
    readonly ElIconDeleteLocation: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DeleteLocation']>
    readonly ElIconDessert: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Dessert']>
    readonly ElIconDiscount: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Discount']>
    readonly ElIconDish: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Dish']>
    readonly ElIconDishDot: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DishDot']>
    readonly ElIconDocument: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Document']>
    readonly ElIconDocumentAdd: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentAdd']>
    readonly ElIconDocumentChecked: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentChecked']>
    readonly ElIconDocumentCopy: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentCopy']>
    readonly ElIconDocumentDelete: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentDelete']>
    readonly ElIconDocumentRemove: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['DocumentRemove']>
    readonly ElIconDownload: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Download']>
    readonly ElIconDrizzling: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Drizzling']>
    readonly ElIconEdit: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Edit']>
    readonly ElIconEditPen: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['EditPen']>
    readonly ElIconEleme: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Eleme']>
    readonly ElIconElemeFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ElemeFilled']>
    readonly ElIconElementPlus: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ElementPlus']>
    readonly ElIconExpand: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Expand']>
    readonly ElIconFailed: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Failed']>
    readonly ElIconFemale: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Female']>
    readonly ElIconFiles: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Files']>
    readonly ElIconFilm: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Film']>
    readonly ElIconFilter: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Filter']>
    readonly ElIconFinished: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Finished']>
    readonly ElIconFirstAidKit: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FirstAidKit']>
    readonly ElIconFlag: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Flag']>
    readonly ElIconFold: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Fold']>
    readonly ElIconFolder: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Folder']>
    readonly ElIconFolderAdd: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderAdd']>
    readonly ElIconFolderChecked: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderChecked']>
    readonly ElIconFolderDelete: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderDelete']>
    readonly ElIconFolderOpened: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderOpened']>
    readonly ElIconFolderRemove: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FolderRemove']>
    readonly ElIconFood: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Food']>
    readonly ElIconFootball: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Football']>
    readonly ElIconForkSpoon: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ForkSpoon']>
    readonly ElIconFries: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Fries']>
    readonly ElIconFullScreen: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['FullScreen']>
    readonly ElIconGoblet: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Goblet']>
    readonly ElIconGobletFull: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GobletFull']>
    readonly ElIconGobletSquare: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GobletSquare']>
    readonly ElIconGobletSquareFull: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GobletSquareFull']>
    readonly ElIconGoldMedal: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GoldMedal']>
    readonly ElIconGoods: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Goods']>
    readonly ElIconGoodsFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['GoodsFilled']>
    readonly ElIconGrape: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Grape']>
    readonly ElIconGrid: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Grid']>
    readonly ElIconGuide: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Guide']>
    readonly ElIconHandbag: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Handbag']>
    readonly ElIconHeadset: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Headset']>
    readonly ElIconHelp: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Help']>
    readonly ElIconHelpFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['HelpFilled']>
    readonly ElIconHide: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Hide']>
    readonly ElIconHistogram: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Histogram']>
    readonly ElIconHomeFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['HomeFilled']>
    readonly ElIconHotWater: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['HotWater']>
    readonly ElIconHouse: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['House']>
    readonly ElIconIceCream: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceCream']>
    readonly ElIconIceCreamRound: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceCreamRound']>
    readonly ElIconIceCreamSquare: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceCreamSquare']>
    readonly ElIconIceDrink: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceDrink']>
    readonly ElIconIceTea: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['IceTea']>
    readonly ElIconInfoFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['InfoFilled']>
    readonly ElIconIphone: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Iphone']>
    readonly ElIconKey: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Key']>
    readonly ElIconKnifeFork: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['KnifeFork']>
    readonly ElIconLightning: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Lightning']>
    readonly ElIconLink: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Link']>
    readonly ElIconList: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['List']>
    readonly ElIconLoading: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Loading']>
    readonly ElIconLocation: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Location']>
    readonly ElIconLocationFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['LocationFilled']>
    readonly ElIconLocationInformation: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['LocationInformation']>
    readonly ElIconLock: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Lock']>
    readonly ElIconLollipop: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Lollipop']>
    readonly ElIconMagicStick: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MagicStick']>
    readonly ElIconMagnet: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Magnet']>
    readonly ElIconMale: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Male']>
    readonly ElIconManagement: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Management']>
    readonly ElIconMapLocation: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MapLocation']>
    readonly ElIconMedal: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Medal']>
    readonly ElIconMemo: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Memo']>
    readonly ElIconMenu: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Menu']>
    readonly ElIconMessage: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Message']>
    readonly ElIconMessageBox: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MessageBox']>
    readonly ElIconMic: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Mic']>
    readonly ElIconMicrophone: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Microphone']>
    readonly ElIconMilkTea: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MilkTea']>
    readonly ElIconMinus: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Minus']>
    readonly ElIconMoney: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Money']>
    readonly ElIconMonitor: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Monitor']>
    readonly ElIconMoon: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Moon']>
    readonly ElIconMoonNight: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MoonNight']>
    readonly ElIconMore: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['More']>
    readonly ElIconMoreFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MoreFilled']>
    readonly ElIconMostlyCloudy: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MostlyCloudy']>
    readonly ElIconMouse: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Mouse']>
    readonly ElIconMug: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Mug']>
    readonly ElIconMute: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Mute']>
    readonly ElIconMuteNotification: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['MuteNotification']>
    readonly ElIconNoSmoking: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['NoSmoking']>
    readonly ElIconNotebook: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Notebook']>
    readonly ElIconNotification: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Notification']>
    readonly ElIconOdometer: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Odometer']>
    readonly ElIconOfficeBuilding: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['OfficeBuilding']>
    readonly ElIconOpen: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Open']>
    readonly ElIconOperation: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Operation']>
    readonly ElIconOpportunity: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Opportunity']>
    readonly ElIconOrange: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Orange']>
    readonly ElIconPaperclip: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Paperclip']>
    readonly ElIconPartlyCloudy: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PartlyCloudy']>
    readonly ElIconPear: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Pear']>
    readonly ElIconPhone: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Phone']>
    readonly ElIconPhoneFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PhoneFilled']>
    readonly ElIconPicture: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Picture']>
    readonly ElIconPictureFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PictureFilled']>
    readonly ElIconPictureRounded: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PictureRounded']>
    readonly ElIconPieChart: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PieChart']>
    readonly ElIconPlace: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Place']>
    readonly ElIconPlatform: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Platform']>
    readonly ElIconPlus: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Plus']>
    readonly ElIconPointer: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Pointer']>
    readonly ElIconPosition: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Position']>
    readonly ElIconPostcard: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Postcard']>
    readonly ElIconPouring: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Pouring']>
    readonly ElIconPresent: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Present']>
    readonly ElIconPriceTag: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['PriceTag']>
    readonly ElIconPrinter: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Printer']>
    readonly ElIconPromotion: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Promotion']>
    readonly ElIconQuartzWatch: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['QuartzWatch']>
    readonly ElIconQuestionFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['QuestionFilled']>
    readonly ElIconRank: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Rank']>
    readonly ElIconReading: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Reading']>
    readonly ElIconReadingLamp: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ReadingLamp']>
    readonly ElIconRefresh: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Refresh']>
    readonly ElIconRefreshLeft: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['RefreshLeft']>
    readonly ElIconRefreshRight: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['RefreshRight']>
    readonly ElIconRefrigerator: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Refrigerator']>
    readonly ElIconRemove: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Remove']>
    readonly ElIconRemoveFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['RemoveFilled']>
    readonly ElIconRight: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Right']>
    readonly ElIconScaleToOriginal: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ScaleToOriginal']>
    readonly ElIconSchool: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['School']>
    readonly ElIconScissor: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Scissor']>
    readonly ElIconSearch: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Search']>
    readonly ElIconSelect: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Select']>
    readonly ElIconSell: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sell']>
    readonly ElIconSemiSelect: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SemiSelect']>
    readonly ElIconService: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Service']>
    readonly ElIconSetUp: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SetUp']>
    readonly ElIconSetting: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Setting']>
    readonly ElIconShare: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Share']>
    readonly ElIconShip: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Ship']>
    readonly ElIconShop: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Shop']>
    readonly ElIconShoppingBag: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ShoppingBag']>
    readonly ElIconShoppingCart: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ShoppingCart']>
    readonly ElIconShoppingCartFull: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ShoppingCartFull']>
    readonly ElIconShoppingTrolley: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ShoppingTrolley']>
    readonly ElIconSmoking: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Smoking']>
    readonly ElIconSoccer: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Soccer']>
    readonly ElIconSoldOut: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SoldOut']>
    readonly ElIconSort: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sort']>
    readonly ElIconSortDown: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SortDown']>
    readonly ElIconSortUp: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SortUp']>
    readonly ElIconStamp: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Stamp']>
    readonly ElIconStar: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Star']>
    readonly ElIconStarFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['StarFilled']>
    readonly ElIconStopwatch: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Stopwatch']>
    readonly ElIconSuccessFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SuccessFilled']>
    readonly ElIconSugar: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sugar']>
    readonly ElIconSuitcase: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Suitcase']>
    readonly ElIconSuitcaseLine: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SuitcaseLine']>
    readonly ElIconSunny: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sunny']>
    readonly ElIconSunrise: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sunrise']>
    readonly ElIconSunset: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Sunset']>
    readonly ElIconSwitch: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Switch']>
    readonly ElIconSwitchButton: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SwitchButton']>
    readonly ElIconSwitchFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['SwitchFilled']>
    readonly ElIconTakeawayBox: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TakeawayBox']>
    readonly ElIconTicket: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Ticket']>
    readonly ElIconTickets: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Tickets']>
    readonly ElIconTimer: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Timer']>
    readonly ElIconToiletPaper: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ToiletPaper']>
    readonly ElIconTools: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Tools']>
    readonly ElIconTop: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Top']>
    readonly ElIconTopLeft: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TopLeft']>
    readonly ElIconTopRight: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TopRight']>
    readonly ElIconTrendCharts: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TrendCharts']>
    readonly ElIconTrophy: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Trophy']>
    readonly ElIconTrophyBase: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TrophyBase']>
    readonly ElIconTurnOff: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['TurnOff']>
    readonly ElIconUmbrella: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Umbrella']>
    readonly ElIconUnlock: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Unlock']>
    readonly ElIconUpload: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Upload']>
    readonly ElIconUploadFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['UploadFilled']>
    readonly ElIconUser: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['User']>
    readonly ElIconUserFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['UserFilled']>
    readonly ElIconVan: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Van']>
    readonly ElIconVideoCamera: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['VideoCamera']>
    readonly ElIconVideoCameraFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['VideoCameraFilled']>
    readonly ElIconVideoPause: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['VideoPause']>
    readonly ElIconVideoPlay: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['VideoPlay']>
    readonly ElIconView: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['View']>
    readonly ElIconWallet: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Wallet']>
    readonly ElIconWalletFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['WalletFilled']>
    readonly ElIconWarnTriangleFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['WarnTriangleFilled']>
    readonly ElIconWarning: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Warning']>
    readonly ElIconWarningFilled: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['WarningFilled']>
    readonly ElIconWatch: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Watch']>
    readonly ElIconWatermelon: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['Watermelon']>
    readonly ElIconWindPower: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['WindPower']>
    readonly ElIconZoomIn: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ZoomIn']>
    readonly ElIconZoomOut: UnwrapRef<typeof import('../../node_modules/@element-plus/icons-vue/dist/index')['ZoomOut']>
    readonly ElLoading: UnwrapRef<typeof import('../../node_modules/element-plus/es/components/loading/index')['ElLoading']>
    readonly ElMessage: UnwrapRef<typeof import('../../node_modules/element-plus/es/components/message/index')['ElMessage']>
    readonly ElMessageBox: UnwrapRef<typeof import('../../node_modules/element-plus/es/components/message-box/index')['ElMessageBox']>
    readonly ElNotification: UnwrapRef<typeof import('../../node_modules/element-plus/es/components/notification/index')['ElNotification']>
    readonly ID_INJECTION_KEY: UnwrapRef<typeof import('../../node_modules/element-plus/es/hooks/use-id/index')['ID_INJECTION_KEY']>
    readonly ZINDEX_INJECTION_KEY: UnwrapRef<typeof import('../../node_modules/element-plus/es/hooks/use-z-index/index')['ZINDEX_INJECTION_KEY']>
    readonly abortNavigation: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']>
    readonly acceptHMRUpdate: UnwrapRef<typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['acceptHMRUpdate']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']>
    readonly callOnce: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']>
    readonly cancelIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']>
    readonly clearError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']>
    readonly clearNuxtState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']>
    readonly computed: UnwrapRef<typeof import('../../node_modules/vue')['computed']>
    readonly createError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']>
    readonly createSitePathResolver: UnwrapRef<typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/utils')['createSitePathResolver']>
    readonly customRef: UnwrapRef<typeof import('../../node_modules/vue')['customRef']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('../../node_modules/vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('../../node_modules/vue')['defineComponent']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']>
    readonly definePageMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']>
    readonly defineStore: UnwrapRef<typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['defineStore']>
    readonly effect: UnwrapRef<typeof import('../../node_modules/vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('../../node_modules/vue')['effectScope']>
    readonly getAppManifest: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']>
    readonly getCurrentInstance: UnwrapRef<typeof import('../../node_modules/vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('../../node_modules/vue')['getCurrentScope']>
    readonly getRouteRules: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']>
    readonly h: UnwrapRef<typeof import('../../node_modules/vue')['h']>
    readonly hasInjectionContext: UnwrapRef<typeof import('../../node_modules/vue')['hasInjectionContext']>
    readonly inject: UnwrapRef<typeof import('../../node_modules/vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['injectHead']>
    readonly isNuxtError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']>
    readonly isPrerendered: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('../../node_modules/vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('../../node_modules/vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('../../node_modules/vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('../../node_modules/vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('../../node_modules/vue')['isShallow']>
    readonly isVue2: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly loadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']>
    readonly markRaw: UnwrapRef<typeof import('../../node_modules/vue')['markRaw']>
    readonly mergeModels: UnwrapRef<typeof import('../../node_modules/vue')['mergeModels']>
    readonly navigateTo: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']>
    readonly nextTick: UnwrapRef<typeof import('../../node_modules/vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('../../node_modules/vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('../../node_modules/vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('../../node_modules/vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('../../node_modules/vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('../../node_modules/vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('../../node_modules/vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']>
    readonly onPrehydrate: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']>
    readonly onRenderTracked: UnwrapRef<typeof import('../../node_modules/vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('../../node_modules/vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('../../node_modules/vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('../../node_modules/vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('../../node_modules/vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('../../node_modules/vue')['onUpdated']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']>
    readonly preloadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']>
    readonly prerenderRoutes: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']>
    readonly provide: UnwrapRef<typeof import('../../node_modules/vue')['provide']>
    readonly provideGlobalConfig: UnwrapRef<typeof import('../../node_modules/element-plus/es/components/config-provider/src/hooks/use-global-config')['provideGlobalConfig']>
    readonly proxyRefs: UnwrapRef<typeof import('../../node_modules/vue')['proxyRefs']>
    readonly reactive: UnwrapRef<typeof import('../../node_modules/vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('../../node_modules/vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('../../node_modules/vue')['ref']>
    readonly refreshCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']>
    readonly requestIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']>
    readonly resolveComponent: UnwrapRef<typeof import('../../node_modules/vue')['resolveComponent']>
    readonly setInterval: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']>
    readonly setPageLayout: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']>
    readonly shallowReactive: UnwrapRef<typeof import('../../node_modules/vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('../../node_modules/vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('../../node_modules/vue')['shallowRef']>
    readonly showError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']>
    readonly storeToRefs: UnwrapRef<typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['storeToRefs']>
    readonly toRaw: UnwrapRef<typeof import('../../node_modules/vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('../../node_modules/vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('../../node_modules/vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('../../node_modules/vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('../../node_modules/vue')['triggerRef']>
    readonly tryUseNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']>
    readonly unref: UnwrapRef<typeof import('../../node_modules/vue')['unref']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']>
    readonly updateSiteConfig: UnwrapRef<typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/updateSiteConfig')['updateSiteConfig']>
    readonly useAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']>
    readonly useAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']>
    readonly useAttrs: UnwrapRef<typeof import('../../node_modules/vue')['useAttrs']>
    readonly useCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']>
    readonly useCssModule: UnwrapRef<typeof import('../../node_modules/vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('../../node_modules/vue')['useCssVars']>
    readonly useError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']>
    readonly useFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']>
    readonly useHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHeadSafe']>
    readonly useHydration: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']>
    readonly useId: UnwrapRef<typeof import('../../node_modules/vue')['useId']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']>
    readonly useLink: UnwrapRef<typeof import('../../node_modules/vue-router')['useLink']>
    readonly useLoadingIndicator: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']>
    readonly useModel: UnwrapRef<typeof import('../../node_modules/vue')['useModel']>
    readonly useNitroOrigin: UnwrapRef<typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/useNitroOrigin')['useNitroOrigin']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']>
    readonly usePinia: UnwrapRef<typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']>
    readonly usePreviewMode: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']>
    readonly useRequestHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']>
    readonly useRequestURL: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']>
    readonly useResponseHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']>
    readonly useRoute: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']>
    readonly useRouteAnnouncer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']>
    readonly useRouter: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']>
    readonly useRuntimeHook: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']>
    readonly useScript: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']>
    readonly useScriptClarity: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']>
    readonly useScriptCloudflareWebAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']>
    readonly useScriptCrisp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']>
    readonly useScriptEventPage: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']>
    readonly useScriptFathomAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']>
    readonly useScriptGoogleAdsense: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']>
    readonly useScriptGoogleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']>
    readonly useScriptGoogleMaps: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']>
    readonly useScriptGoogleTagManager: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']>
    readonly useScriptHotjar: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']>
    readonly useScriptIntercom: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']>
    readonly useScriptLemonSqueezy: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']>
    readonly useScriptMatomoAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']>
    readonly useScriptMetaPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']>
    readonly useScriptNpm: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']>
    readonly useScriptPlausibleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']>
    readonly useScriptRybbitAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptRybbitAnalytics']>
    readonly useScriptSegment: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']>
    readonly useScriptSnapchatPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']>
    readonly useScriptStripe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']>
    readonly useScriptTriggerConsent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']>
    readonly useScriptTriggerElement: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']>
    readonly useScriptUmamiAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']>
    readonly useScriptVimeoPlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']>
    readonly useScriptXPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']>
    readonly useScriptYouTubePlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']>
    readonly useSeoMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']>
    readonly useShadowRoot: UnwrapRef<typeof import('../../node_modules/vue')['useShadowRoot']>
    readonly useSiteConfig: UnwrapRef<typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/useSiteConfig')['useSiteConfig']>
    readonly useSlots: UnwrapRef<typeof import('../../node_modules/vue')['useSlots']>
    readonly useState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']>
    readonly useTemplateRef: UnwrapRef<typeof import('../../node_modules/vue')['useTemplateRef']>
    readonly useTransitionState: UnwrapRef<typeof import('../../node_modules/vue')['useTransitionState']>
    readonly watch: UnwrapRef<typeof import('../../node_modules/vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchSyncEffect']>
    readonly withCtx: UnwrapRef<typeof import('../../node_modules/vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('../../node_modules/vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('../../node_modules/vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('../../node_modules/vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('../../node_modules/vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('../../node_modules/vue')['withScopeId']>
    readonly withSiteTrailingSlash: UnwrapRef<typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/utils')['withSiteTrailingSlash']>
    readonly withSiteUrl: UnwrapRef<typeof import('../../node_modules/nuxt-site-config/dist/runtime/app/composables/utils')['withSiteUrl']>
  }
}